import Cookies from 'js-cookie';

/**
 * Set authentication token cookie with proper expiration
 * @param {string} token - The authentication token
 * @param {string} tokenExpires - ISO date string for token expiration
 * @param {string} userRole - User role (student, tutor, admin)
 */
export const setAuthTokenCookie = (token, tokenExpires, userRole) => {
  if (!token) return;

  // Calculate expiration date from token_expires
  let cookieOptions = { path: '/' };

  if (tokenExpires) {
    const expirationDate = new Date(tokenExpires);
    cookieOptions.expires = expirationDate;
  } else {
    // Fallback to 7 days if token_expires is not provided
    cookieOptions.expires = 7;
  }

  // Remove existing cookies and set new ones
  Cookies.remove('token');
  Cookies.remove('userRole');

  Cookies.set('token', token, cookieOptions);

  // Set user role cookie with same expiration
  if (userRole) {
    Cookies.set('userRole', userRole, cookieOptions);
  }
};

/**
 * Remove authentication token and role cookies
 */
export const removeAuthTokenCookie = () => {
  Cookies.remove('token', { path: '/' });
  Cookies.remove('userRole', { path: '/' });
};

/**
 * Get authentication token from cookie
 * @returns {string|undefined} The authentication token
 */
export const getAuthTokenFromCookie = () => {
  return Cookies.get('token');
};

/**
 * Get user role from cookie
 * @returns {string|undefined} The user role (student, tutor, admin)
 */
export const getUserRoleFromCookie = () => {
  return Cookies.get('userRole');
};
