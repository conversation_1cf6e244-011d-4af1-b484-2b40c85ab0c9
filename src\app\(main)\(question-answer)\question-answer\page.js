'use client';
import Button, { ButtonIcon } from '@/components/Button';
import <PERSON><PERSON>iewer from '@/components/EditorViewer';
import DetailsModal from '@/components/form/modal/MissionConfirmationModal';
import GoBack from '@/components/shared/GoBack';
import useDataFetch from '@/hooks/useDataFetch';
import api from '@/lib/api';
import { Form, Formik } from 'formik';
import Image from 'next/image';
import { useRouter, useSearchParams } from 'next/navigation';
import React, { useEffect, useRef, useState } from 'react';
import { toast } from 'sonner';
import {
  getLastSubmissionContent,
  getTruncatedSubmissionContent,
  hasExistingSubmission,
  countWords,
} from '@/utils/submissionUtils';
import EssayFeedBackModal from '../../essay/_components/FeedbackModal';
import SimpleTiptapEditor from '@/components/form/SimpleTiptapEditor';

const HecAnswer = () => {
  const itemId = useSearchParams().get('itemId');
  const taskId = useSearchParams().get('taskId');
  const router = useRouter();
  const editorRef = useRef(null);
  const [value, setValue] = useState('');
  const [wordCount, setWordCount] = useState(0);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [autoSaveTimeout, setAutoSaveTimeout] = useState(null);
  const [responseData, setResponseData] = useState(null);

  const { data, isLoading, refetch } = useDataFetch({
    queryKey: ['hec-answer', itemId],
    endPoint: `/student-qa-mission/activeTask`,
    enabled: !!itemId,
  });

  const { data: QnAData, isLoading: isQnALoading } = useDataFetch({
    queryKey: ['hec-answer', taskId],
    endPoint: `student-qa-mission/task/${taskId}`,
    enabled: !!taskId,
  });

  useEffect(() => {
    if (data) {
      setResponseData(data);
    } else if (!isLoading && !data && !isQnALoading && !QnAData) {
      router.push('/hec-mission');
    } else {
      setResponseData(QnAData);
    }
  }, [data, QnAData]);

  // Set initial word count when response data is loaded
  useEffect(() => {
    if (responseData) {
      const initialContent = getLastSubmissionContent(responseData);
      if (initialContent) {
        setValue(initialContent);
        const initialWordCount = countWords(initialContent);
        setWordCount(initialWordCount);
      }
    }
  }, [responseData]);

  const showSubmission =
    ((responseData?.submission &&
      responseData?.submission?.submissionHistory?.content) ||
      responseData?.submissionHistory?.content) &&
    !isSubmitted;
  const showFeedback =
    responseData?.submission && responseData?.submission?.submissionMark;

  const handleSubmit = async (values) => {
    try {
      console.log('values:', values);
      // Get the content and calculate word count
      const content = values?.content?.answer || values?.content;
      const submissionWordCount = countWords(content);
      const minimumWords =
        responseData?.wordLimitMinimum ||
        responseData?.task?.wordLimitMinimum ||
        0;
      const maximumWords =
        responseData?.wordLimitMaximum ||
        responseData?.task?.wordLimitMaximum ||
        Infinity;

      if (
        submissionWordCount < minimumWords ||
        submissionWordCount > maximumWords
      ) {
        toast.message(
          `Your answer must contain between ${minimumWords} and ${maximumWords} words. Current word count: ${submissionWordCount}`
        );
        return;
      }

      // Check if there's an existing submissionMark to determine API method
      const hasSubmissionMark =
        responseData?.submission?.submissionHistory?.submissionMark ||
        responseData?.submissionHistory?.[0]?.submissionMark ||
        responseData?.submissionHistory?.submissionMark;

      // console.log('Checking for existing submission mark:', hasSubmissionMark);

      let response;
      if (hasSubmissionMark) {
        // Use PATCH method for existing submissions with marks
        console.log('Using PATCH method - submission has existing marks');
        response = await api.patch(`/student-qa-mission/submit/task`, {
          ...values,
          content: content,
          wordCount: submissionWordCount,
        });
      } else {
        // Use POST method for new submissions
        console.log('Using POST method - new submission');
        response = await api.post(`/student-qa-mission/submit/task`, {
          ...values,
          content: content,
          wordCount: submissionWordCount,
        });
      }
      console.log(response);

      // Clear auto-save timeout to prevent update API call after successful submission
      if (autoSaveTimeout) {
        clearTimeout(autoSaveTimeout);
        setAutoSaveTimeout(null);
      }

      // Set submitted flag before navigation to prevent unmount auto-save
      setIsSubmitted(true);

      // Navigate to mission page
      router.push('/hec-mission');
    } catch (error) {
      console.log(error);
    }
  };

  const handleUpdate = async (values) => {
    try {
      // Ensure we have the answer value
      console.log('Values for update.', isSubmitted);
      if (
        !values ||
        !values.content ||
        responseData?.submission?.status === 'submitted'
      )
        return;

      if (autoSaveTimeout) {
        clearTimeout(autoSaveTimeout);
      }

      const timeout = setTimeout(async () => {
        try {
          const submissionId = responseData?.submission?.id || responseData?.id;

          const content = values?.content?.answer || values?.content;
          const updateWordCount = countWords(content);

          const response = await api.post(
            `/student-qa-mission/submit/task/update`,
            {
              content: content,
              submissionId: submissionId,
              wordCount: updateWordCount,
            },
            { showSuccessToast: false }
          );
          // console.log('Auto-saved (debounced):', response);
        } catch (error) {
          console.error('Auto-save error:', error);
        }
      }, 300);
      setAutoSaveTimeout(timeout);

      // Only refetch and update UI state on explicit submission, not auto-save
      if (!values._autoSave) {
        refetch();
        setIsSubmitted(false);
      }
    } catch (error) {
      console.error('Error updating submission:', error);
    }
  };

  // Handle component unmount - save draft
  // useEffect(() => {
  //   return () => {
  //     // Only auto-save if not submitted and has content
  //     if (value && !isSubmitted && responseData?.id && !autoSaveTimeout) {
  //       setTimeout(() => {
  //         console.log('calling from main useEffect')
  //         handleUpdate({
  //           content: value,
  //           _autoSave: true,
  //         });
  //       }, 300);
  //     }
  //   };
  // }, [value]);

  return (
    <div className="relative">
      {!isLoading && !isQnALoading && (
        <div className="max-w-7xl mx-auto px-5 xl:px-0 relative z-10">
          <GoBack
            title={'HEC Mission Q & A'}
            linkClass="my-5 mb-8 w-full max-w-60"
          />

          <div className="p-5 rounded-lg bg-[#FFF9FB] shadow-lg space-y-5 mb-10">
            <div className="p-5 bg-[#EDFDFD] w-full rounded-lg shadow-lg space-y-5 relative">
              <div className="p-5 bg-[#FCF8EF] rounded-lg [box-shadow:2px_2px_12px_0px_#F5D1B066_inset,_-2px_-2px_12px_0px_#F5D1B066_inset]  flex items-start justify-between">
                <div>
                  <h1 className="text-2xl text-yellow-800 font-semibold">
                    {responseData?.task?.title || responseData?.title}
                  </h1>
                  <p>Instruction:</p>
                  <EditorViewer
                    data={
                      responseData?.task?.instructions ||
                      responseData?.task?.description ||
                      responseData?.instructions
                    }
                  />
                </div>

                <div className="relative">
                  <Image
                    src={'/assets/images/all-img/boardFrame.png'}
                    alt={'block-play'}
                    width={500}
                    height={500}
                    className="max-w-52 max-sm:max-w-32 max-sm:max-h-20 max-h-32"
                  />

                  <ul className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-sm min-w-32">
                    <li>
                      Total Score:{' '}
                      {responseData?.task?.totalScore ||
                        responseData?.totalScore ||
                        0}
                    </li>
                    {/* <li>Time (Minutes): 20</li> */}
                  </ul>
                </div>
              </div>
            </div>

            {showSubmission ? (
              <div className="space-y-3">
                <div className="p-5 shadow-lg rounded-lg border relative bg-white">
                  <h1 className="text-xl text-yellow-800 font-semibold">
                    My Answer
                  </h1>
                  <EditorViewer
                    data={getTruncatedSubmissionContent(responseData)}
                  />

                  {!(
                    responseData?.submission?.status === 'submitted' ||
                    responseData?.status === 'submitted'
                  ) && (
                    <div className="absolute right-2 top-2">
                      <ButtonIcon
                        icon={'ri:edit-2-fill'}
                        innerBtnCls={'h-10 w-10'}
                        btnIconCls={'h-5 w-5'}
                        onClick={() => setIsSubmitted(true)}
                      />
                    </div>
                  )}
                </div>

                {!(
                  responseData?.submission?.status === 'draft' ||
                  responseData?.status === 'draft'
                ) && (
                  <div className="p-5 shadow-lg rounded-lg border relative bg-white">
                    <h1 className="text-xl text-yellow-800 font-semibold">
                      Tutor Correction Zone
                    </h1>
                    {showFeedback ? (
                      <>
                        {(responseData?.submission?.submissionMark ||
                          responseData?.submissionHistory?.[0]
                            ?.submissionMark ||
                          responseData?.submissionHistory?.submissionMark) && (
                          <div className="rounded-md">
                            <ul className=" text-sm text-gray-800">
                              <EditorViewer
                                data={
                                  responseData?.submission?.submissionMark
                                    ?.taskRemarks ||
                                  responseData?.submissionHistory?.[0]
                                    ?.submissionMark?.taskRemarks ||
                                  'No correction provided yet.'
                                }
                              />
                            </ul>
                          </div>
                        )}

                        <div className="absolute right-2 top-2">
                          <ButtonIcon
                            icon={'arcticons:feedback-2'}
                            innerBtnCls={'h-10 w-10'}
                            btnIconCls={'h-4 w-4'}
                            onClick={() => setShowDetailsModal(true)}
                          />
                        </div>
                      </>
                    ) : (
                      <p
                        className={`${
                          !(data?.submission?.status === 'reviewed') &&
                          'text-red-600'
                        } text-center mt-2`}
                      >
                        {!(data?.submission?.status === 'reviewed') &&
                          'Not Reviewed yet'}
                      </p>
                    )}
                  </div>
                )}
              </div>
            ) : (
              <Formik
                initialValues={{
                  taskId: responseData?.taskId || responseData?.id,
                  content: value || getLastSubmissionContent(responseData),
                }}
                onSubmit={handleSubmit}
                enableReinitialize
              >
                {() => (
                  <Form>
                    <SimpleTiptapEditor
                      name="content"
                      editorRef={editorRef}
                      initialValue={getLastSubmissionContent(responseData)}
                      onAutoSave={(content) => {
                        setValue(content);
                        const newWordCount = countWords(
                          content?.answer || content
                        );
                        setWordCount(newWordCount);
                        // Set new timeout for auto-save
                        setTimeout(() => {
                          handleUpdate({
                            content: content,
                            _autoSave: true,
                          });
                        }, 300); // 0.3 second delay after typing stops
                      }}
                      setValue={setValue}
                      minWords={
                        responseData?.task?.wordLimitMinimum ||
                        responseData?.wordLimitMinimum
                      }
                      maxWords={
                        responseData?.task?.wordLimitMaximum ||
                        responseData?.wordLimitMaximum ||
                        Infinity
                      }
                    />

                    {/* Word count display */}
                    <div className="flex justify-end items-center mt-2">
                      <p
                        className={`text-sm ${
                          wordCount <
                            (responseData?.task?.wordLimitMinimum ||
                              responseData?.wordLimitMinimum ||
                              0) ||
                          wordCount >
                            (responseData?.task?.wordLimitMaximum ||
                              responseData?.wordLimitMaximum ||
                              Infinity)
                            ? 'text-red-500'
                            : 'text-green-600'
                        }`}
                      >
                        {wordCount <
                          (responseData?.task?.wordLimitMinimum ||
                            responseData?.wordLimitMinimum ||
                            0) &&
                          `Minimum ${
                            responseData?.task?.wordLimitMinimum ||
                            responseData?.wordLimitMinimum
                          } words required to submit.`}
                        {wordCount >
                          (responseData?.task?.wordLimitMaximum ||
                            responseData?.wordLimitMaximum ||
                            Infinity) &&
                          `Maximum ${
                            responseData?.task?.wordLimitMaximum ||
                            responseData?.wordLimitMaximum
                          } words allowed.`}
                        {wordCount >=
                          (responseData?.task?.wordLimitMinimum ||
                            responseData?.wordLimitMinimum ||
                            0) &&
                          wordCount <=
                            (responseData?.task?.wordLimitMaximum ||
                              responseData?.wordLimitMaximum ||
                              Infinity) &&
                          'Word count is within limits.'}
                      </p>
                    </div>

                    <div className="flex justify-center mt-14 gap-3">
                      <Button
                        buttonText="Cancel"
                        type="button"
                        onClick={() => setIsSubmitted(false)}
                      />
                      <Button
                        buttonText={
                          responseData?.submission?.submissionMark ||
                          responseData?.submissionHistory?.[0]
                            ?.submissionMark ||
                          responseData?.submissionHistory?.submissionMark
                            ? 'Update'
                            : 'Submit'
                        }
                        type="submit"
                        className="bg-yellow-400 hover:bg-yellow-500 text-black"
                      />
                    </div>
                  </Form>
                )}
              </Formik>
            )}
          </div>
        </div>
      )}

      {responseData?.submission && (
        <EssayFeedBackModal
          isOpen={showDetailsModal}
          onClose={() => setShowDetailsModal(false)}
          data={responseData?.submission?.submissionMark?.Feedback}
          title="Teacher's Feedback"
        />
      )}
    </div>
  );
};

export default HecAnswer;
