import React from 'react';
import WordBlock from './WordBlock';
import Image from 'next/image';

const GameInstructionView = ({ gameData }) => {
  if (!gameData) return null;

  // Extract data from gameData
  const startingWords = gameData?.word_blocks?.starting_words || [];
  const expandingWords = gameData?.word_blocks?.expanding_words || [];
  const title = 'Create Sentence Using The Words In The Block.';
  const instructions = [
    'Write a starting sentence from Block set # 1',
    'Expand that sentence using Block set # 2',
  ];
  const totalScore = gameData?.score || 0;
  return (
    <div className="w-full mx-auto relative bg-[#EDFDFD] p-3 sm:p-4 mb-6 sm:mb-8 rounded-xl">
      {/* Header with instructions on left and score image on right */}
      <div className="flex flex-col sm:flex-row justify-between items-start gap-4 sm:gap-8">
        {/* Left side - Instructions */}
        <div className="flex-1">
          <h1 className="text-lg sm:text-xl lg:text-2xl font-bold text-[#8B4513] mb-4 sm:mb-6">{title}</h1>
          <div className="text-[#5A3D1A] mb-4">
            <div className="font-semibold mb-2 text-sm sm:text-base">Instruction :</div>
            <ol className="list-decimal list-inside space-y-1 ml-2 sm:ml-4">
              {instructions.map((instruction, index) => (
                <li key={index} className="text-xs sm:text-sm">
                  {instruction}
                </li>
              ))}
            </ol>
          </div>
        </div>

        {/* Right side - Score card with background image */}
        <div
          className="w-full sm:w-48 h-20 sm:h-24 bg-cover bg-center bg-no-repeat rounded-[20px] flex items-center justify-center flex-shrink-0"
          style={{
            backgroundImage: "url('/assets/Frame 1000008044.png')",
          }}
        >
          <div className="text-[#8B4513] font-bold text-base sm:text-lg">
            • Total Score: {totalScore}
          </div>
        </div>
      </div>

      {/* Word Blocks */}
      {/* <div className="w-full space-y-8">
        // Starting Sentence Block
        {startingWords.length > 0 && (
          <WordBlock
            words={startingWords}
            usedWords={[]}
            title="Starting Sentence Block set # 1"
            disable={false}
            disableDrag={true}
            disableClick={true}
            showImage={true}
            imageSrc="/assets/cat.png"
            imageWidth={80}
            imageHeight={80}
            imageAlt="Cat Icon"
            containerClassName="border-orange-300"
          />
        )}

        // Expanding Sentence Block
        {expandingWords.length > 0 && (
          <WordBlock
            words={expandingWords}
            usedWords={[]}
            title="Expanding Sentence Block set # 2"
            disable={false}
            disableDrag={true}
            disableClick={true}
            showImage={true}
            imageSrc="/assets/tiger.png"
            imageWidth={80}
            imageHeight={80}
            imageAlt="Tiger Icon"
            containerClassName="border-orange-300"
          />
        )}
      </div> */}

      {/* Decorations */}
      <div className="w-full mt-6 sm:mt-8">
        <Image
          src="/assets/Frame 1000007108.png"
          alt="Decoration"
          width={500}
          height={20}
          className="w-full h-auto opacity-60"
        />
      </div>
    </div>
  );
};

export default GameInstructionView;
