import api from '@/lib/api';
import { Icon } from '@iconify/react';
import { useFormikContext } from 'formik';
import React, { useState, useEffect, useRef } from 'react';

// Item card component for search results
const ItemCard = ({ item, onSelect, isSelected, type }) => {
  let title = item.name || item.title;
  let category = '';

  if (type === 'shop_item') {
    category = `Category: ${item.categoryName || 'N/A'}`;
  }

  return (
    <div
      onClick={() => onSelect(item)}
      className={`border rounded-lg p-3 flex items-center bg-yellow-50 cursor-pointer gap-3 ${
        isSelected ? 'border-yellow-400 bg-yellow-50' : 'border-yellow-300'
      }`}
    >
      <input
        type="checkbox"
        checked={isSelected}
        className="h-4 w-4 text-yellow-500 focus:ring-yellow-400 border-gray-300 rounded"
      />
      <div className="flex-1">
        <div className="flex items-center gap-2">
          {item.filePath && (
            <img
              src={item.filePath}
              alt={title}
              className="w-10 h-10 object-cover rounded"
            />
          )}
          <div>
            <h3 className="font-medium text-gray-800">{title}</h3>
            {category && <p className="text-xs text-gray-500">{category}</p>}
          </div>
        </div>
      </div>
    </div>
  );
};

// Form content component to handle form state and API calls
const SelectItems = () => {
  // Get values and setFieldValue from Formik context
  const { values, setFieldValue } = useFormikContext();
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isInputFocused, setIsInputFocused] = useState(false);
  const searchInputRef = useRef(null);
  const searchResultsRef = useRef(null);

  // Handle clicks outside the search input and results
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        searchInputRef.current &&
        !searchInputRef.current.contains(event.target) &&
        searchResultsRef.current &&
        !searchResultsRef.current.contains(event.target)
      ) {
        setIsInputFocused(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Fetch initial data when applicable type changes
  useEffect(() => {
    setFieldValue('selectedItems', []);

    const fetchInitialData = async () => {
      if (!values.applicableType) {
        setSearchResults([]);
        return;
      }

      setIsLoading(true);
      try {
        let endpoint = '';

        switch (values.applicableType) {
          case 'shop_item':
            endpoint = '/admin/shop/items';
            break;
          case 'categoryIds':
            endpoint = '/admin/shop/categories';
            break;
          case 'planIds':
            endpoint = '/plans';
            break;
          default:
            return;
        }

        const response = await api.get(endpoint);
        setSearchResults(response.data?.items || []);
      } catch (error) {
        console.error('Error fetching initial data:', error);
        setSearchResults([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchInitialData();
  }, [values.applicableType]);

  // Handle search when search term changes
  useEffect(() => {
    if (!values.applicableType) {
      return;
    }

    const fetchSearchResults = async () => {
      setIsLoading(true);
      try {
        let endpoint = '';
        let params = {};

        switch (values.applicableType) {
          case 'shop_item':
            endpoint = '/admin/shop/items';
            // Only add title parameter if searchTerm is not empty and has at least 3 characters
            if (searchTerm && searchTerm.length >= 3) {
              params = { title: searchTerm };
            }
            break;
          case 'categoryIds':
            endpoint = '/admin/shop/categories';
            // Only add name parameter if searchTerm is not empty and has at least 3 characters
            if (searchTerm && searchTerm.length >= 3) {
              params = { name: searchTerm };
            }
            break;
          case 'planIds':
            endpoint = '/plans';
            // Only add search parameter if searchTerm is not empty and has at least 3 characters
            if (searchTerm && searchTerm.length >= 3) {
              params = { search: searchTerm };
            }
            break;
          default:
            return;
        }

        const response = await api.get(endpoint, { params });
        setSearchResults(response.data?.items || []);
      } catch (error) {
        console.error('Error searching items:', error);
      } finally {
        setIsLoading(false);
      }
    };

    // Debounce search
    const timeoutId = setTimeout(() => {
      // Always fetch results, regardless of search term length
      fetchSearchResults();
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [searchTerm, values.applicableType]);

  // Handle item selection
  const handleSelectItem = (item) => {
    const currentItems = [...values.selectedItems];
    const itemId = item.id;

    // Check if item is already selected
    const isAlreadySelected = currentItems.some(
      (selectedItem) => selectedItem.id === itemId
    );

    if (isAlreadySelected) {
      // Remove item if already selected
      const updatedItems = currentItems.filter(
        (selectedItem) => selectedItem.id !== itemId
      );
      setFieldValue('selectedItems', updatedItems);
    } else {
      // Add item if not already selected
      setFieldValue('selectedItems', [...currentItems, item]);
    }
  };

  // Check if an item is selected
  const isItemSelected = (itemId) => {
    return values.selectedItems.some((item) => item.id === itemId);
  };

  return (
    <>
      <div className="">
        <div>
          <label className="block text-sm font-medium mb-1 text-gray-700">
            Search Item <span className="text-red-500">*</span>
          </label>
          <div className="relative">
            <input
              ref={searchInputRef}
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onFocus={() => setIsInputFocused(true)}
              placeholder={`Search ${
                values.applicableType === 'shop_item'
                  ? 'items'
                  : values.applicableType === 'categoryIds'
                  ? 'categories'
                  : 'plans'
              }...`}
              className="w-full px-4 py-2 pr-10 border rounded-lg focus:outline-none focus:ring-1 focus:ring-yellow-400"
              disabled={!values.applicableType}
            />
            {/* Search Results */}
            <div
              ref={searchResultsRef}
              className={`absolute top-14 left-0 w-full z-10 ${
                isInputFocused ? 'block' : 'hidden'
              }`}
            >
              <div className="border rounded-lg p-4 bg-white shadow-lg max-h-[400px] overflow-y-auto">
                {isLoading ? (
                  <div className="flex justify-center items-center h-full">
                    <Icon
                      icon="eos-icons:loading"
                      className="w-8 h-8 text-yellow-500"
                    />
                  </div>
                ) : searchResults.length > 0 ? (
                  <div className="space-y-3">
                    {searchResults.map((item) => (
                      <ItemCard
                        key={item.id}
                        item={item}
                        onSelect={(selectedItem) => {
                          handleSelectItem(selectedItem);
                          // Keep dropdown open after selection
                        }}
                        isSelected={isItemSelected(item.id)}
                        type={values.applicableType}
                      />
                    ))}
                  </div>
                ) : searchResults.length === 0 ? (
                  <div className="flex flex-col items-center justify-center h-full text-gray-500">
                    <Icon
                      icon="mdi:file-search-outline"
                      className="w-12 h-12 mb-2"
                    />
                    <p>No items found</p>
                  </div>
                ) : !values.applicableType ? (
                  <div className="flex flex-col items-center justify-center h-full text-gray-500">
                    <Icon
                      icon="mdi:filter-variant"
                      className="w-12 h-12 mb-2"
                    />
                    <p>Select an application type to view items</p>
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center h-full text-gray-500">
                    <Icon icon="mdi:search" className="w-12 h-12 mb-2" />
                    <p>Type to search for specific items</p>
                  </div>
                )}
              </div>
            </div>
            <div className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400">
              {isLoading ? (
                <Icon icon="eos-icons:loading" className="w-5 h-5" />
              ) : (
                <Icon
                  icon={isInputFocused ? 'mdi:close' : 'stash:search'}
                  className="w-5 h-5 cursor-pointer"
                  onClick={() => {
                    if (isInputFocused) {
                      setIsInputFocused(false);
                    } else {
                      setIsInputFocused(true);
                      searchInputRef.current?.focus();
                    }
                  }}
                />
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default SelectItems;
