'use client';
import React, { useState } from 'react';
import Image from 'next/image';
import useDataFetch from '@/hooks/useDataFetch';
import { Icon } from '@iconify/react';
import { formatDate } from '@/utils/dateFormatter';

// Award Badge Component
const AwardBadge = ({
  points,
  name,
  imageSrc = '/assets/diary/award.png',
  data,
}) => {
  return (
    <div className="flex flex-col items-center bg-[#FFFDF5] rounded-lg border border-yellow-200 p-4 shadow-sm max-w-[250px]">
      <div className="relative pt-2">
        {/* Trophy icon for weekly/monthly winners */}
        {points !== '60' && (
          <div className="absolute -top-6 -left-6">
            <Image
              src="/assets/diary/trophy.png"
              alt="Trophy"
              width={30}
              height={30}
            />
          </div>
        )}

        <span className="text-sm absolute -right-3 -top-3">
          {formatDate(data?.awardDate, 'short')}
        </span>

        {/* Profile image */}
        <div className="w-24 h-24 rounded-full overflow-hidden">
          <Image
            src={imageSrc}
            alt="Profile"
            width={96}
            height={96}
            className="object-cover"
          />
        </div>
      </div>

      <div className="mt-4 text-center">
        <p className="font-bold text-sm">Best Performer</p>
        <p className="text-xs text-gray-600 mt-1">{name}</p>
        <p className="text-sm font-bold text-yellow-600 mt-2">
          Point: {points}
        </p>
      </div>
    </div>
  );
};

const AwardPage = () => {
  const [showIntro, setShowIntro] = useState(true);
  // Fetch awards data from API
  const {
    data: awardsData,
    isLoading,
    error,
  } = useDataFetch({
    queryKey: 'diary-awards',
    endPoint: '/awards/my-awards',
  });

  const myAward = awardsData?.myAward || null;
  const weeklyWinners =
    awardsData?.awards?.filter((item) => item?.metadata?.period === 'weekly') ||
    [];
  const monthlyWinners =
    awardsData?.awards?.filter(
      (item) => item?.metadata?.period === 'monthly'
    ) || [];

  // Loading state
  if (isLoading) {
    return (
      <div className="container p-6 space-y-8 bg-[#EDFDFD]">
        <div className="flex justify-center items-center min-h-[calc(100vh-180px)]">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-yellow-500"></div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="container p-6 space-y-8 bg-[#EDFDFD]">
        <div className="flex justify-center items-center min-h-[calc(100vh-180px)]">
          <div className="text-center">
            <p className="text-red-600 mb-4">Failed to load awards data</p>
            <p className="text-gray-600">{error.message}</p>
          </div>
        </div>
      </div>
    );
  }

  return showIntro ? (
    <div className="min-h-[calc(100vh-180px)] bg-[#EDFDFD] flex flex-col items-center justify-center p-6">
      <div className="max-w-2xl bg-white rounded-xl p-8 text-center">
        <div className="flex justify-center mb-6">
          <Icon
            icon="streamline-ultimate-color:award-ribbon-star-1"
            width="64"
            height="64"
          />
        </div>

        <h1 className="text-3xl font-bold text-[#8B4513] mb-4">
          Welcome to Awards!
        </h1>

        <div className="space-y-4 text-left mb-8">
          <div className="flex items-center">
            {/* <div className="bg-[#8B4513] text-white rounded-full w-6 h-6 flex items-center justify-center mr-3 mt-1 flex-shrink-0">
              1
            </div> */}
            <p className="text-gray-700">
              - Track your personal achievements and awards
            </p>
          </div>
          <div className="flex items-center">
            {/* <div className="bg-[#8B4513] text-white rounded-full w-6 h-6 flex items-center justify-center mr-3 mt-1 flex-shrink-0">
              2
            </div> */}
            <p className="text-gray-700">- See this week's top performers</p>
          </div>
          <div className="flex items-center">
            {/* <div className="bg-[#8B4513] text-white rounded-full w-6 h-6 flex items-center justify-center mr-3 mt-1 flex-shrink-0">
              3
            </div> */}
            <p className="text-gray-700">- Discover monthly award winners</p>
          </div>
        </div>

        <button
          onClick={() => setShowIntro(false)}
          className="bg-[#8B4513] text-white px-8 py-2 rounded-full font-medium hover:bg-[#A0522D] transition text-lg"
        >
          View Awards
        </button>
      </div>
    </div>
  ) : (
    <>
      <div className=" bg-[#EDFDFD]">
        {/* My Achieve Award Section */}
        <div className="relative">
          <div className="w-full h-full">
            <Image
              src="/assets/images/all-img/awardBg1.png"
              alt="Award"
              width={1600}
              height={400}
              className="w-full min-h-52 lg:h-full object-cover"
            />
          </div>

          <div className="p-5 lg:p-20 absolute top-1/2 -translate-y-1/2 space-y-2">
            <h2 className="text-2xl sm:text-4xl font-bold text-yellow-700 font-serif">
              HEC Diary Award
            </h2>
            <p className="text-xl sm:text-2xl font-semibold text-gray-700">
              Celebrate great writing, creativity, and connection through
              awards!
            </p>
          </div>
        </div>

        <div className="max-w-7xl mx-auto space-y-8 p-5 lg:p-8 text-[#723F11]">
          <div className="bg-[#F3FFF9] rounded text-left shadow-lg p-6 space-y-2">
            <h2 className="text-2xl font-bold text-brown-700">
              My Achieved Award
            </h2>
            <p className="mb-6">
              This section will show the awards you receive for your writing,
              design, accuracy, and how much your work is shared or followed by
              friends.
            </p>
            <div className="flex justify-start">
              {myAward ? (
                <AwardBadge points={myAward.points} name={myAward.name} />
              ) : (
                <div className="text-gray-500 text-center w-full py-8">
                  <p>No award data available</p>
                </div>
              )}
            </div>
          </div>

          {/* This Week Award Winners */}
          <div className="bg-white rounded-lg shadow-lg p-6 space-y-2">
            <h2 className="text-2xl font-bold text-left text-brown-700">
              This Week's Award Winners
            </h2>
            <p className="mb-6">
              Each week, students are selected for awards such as Best Writer
              (most likes), Best Designer (most creative layout), Best Perfect
              (fewest mistakes), and Best Friendship (most shares or friends).
            </p>
            <div className="flex flex-wrap gap-6">
              {weeklyWinners.length > 0 ? (
                weeklyWinners.map((winner) => (
                  <AwardBadge
                    key={winner.id}
                    points={winner.points}
                    name={winner.name}
                  />
                ))
              ) : (
                <div className="text-gray-500 text-center py-8 w-full">
                  <p>No weekly winners available</p>
                </div>
              )}
            </div>
          </div>

          {/* This Month Award Winners */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-2xl font-bold text-left text-brown-700 space-y-2">
              This Month's Award Winners
            </h2>
            <p className=" mb-6">
              This section highlights students who performed the best throughout
              the month in writing quality, creative design, writing accuracy,
              and sharing or connecting with others.
            </p>
            <div className="flex flex-wrap gap-6">
              {monthlyWinners.length > 0 ? (
                monthlyWinners.map((winner) => (
                  <AwardBadge
                    key={winner.id}
                    points={winner.rewardPoints}
                    name={winner.name}
                    data={winner}
                  />
                ))
              ) : (
                <div className="text-gray-500 text-center py-8 w-full">
                  <p>No monthly winners available</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default AwardPage;
