import { useField } from 'formik';
import { useState } from 'react';

const FormDatePicker = ({required, label, ...props }) => {
  const [field, meta, helpers] = useField(props);
  const { setValue } = helpers;

  return (
    <div className="mb-4">
      <label className="block text-sm font-medium mb-2">
        {label} {required && <span className="text-red-500">*</span>}
      </label>
      <input
        type="date"
        {...field}
        {...props}
        onChange={(e) => setValue(e.target.value)}
        className={`w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-1 focus:ring-yellow-500 ${meta.touched && meta.error ? 'border-red-500' : 'border-gray-300'}`}
      />
      {meta.touched && meta.error ? (
        <div className="text-red-500 text-xs mt-1">{meta.error}</div>
      ) : null}
    </div>
  );
};

export default FormDatePicker;