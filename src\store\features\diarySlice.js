// src/store/features/diarySlice.js
import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  subject: '',
  message: '',
  date: '',
  isDecorating: false,
  selectedSkin: null,
  isSaving: false,
  isLoading: true,
  todayEntry: null,
  selectedStage: null,
  isSkinModalOpen: false,
  isTodayDiaryOpen: false,
  layoutBackground: '#FFFDF5',
  decorationItems: [],
  selectedDecorationId: null,
  activeTool: 'decoration', // decoration, brush, eraser
  brushSettings: {
    size: 5,
    color: '#000000',
    opacity: 1
  },
  // Decoration submission state
  isSubmittingDecoration: false,
  decorationSubmissionError: null,
  decorationSubmissionSuccess: false,
  // Undo/Redo state for decorations
  decorationHistory: [[]],
  currentHistoryIndex: 0,
  maxHistorySize: 50,
};

const diarySlice = createSlice({
  name: 'diary',
  initialState,
  reducers: {
    setSubject: (state, action) => {
      state.subject = action.payload;
    },
    setMessage: (state, action) => {
      state.message = action.payload;
    },
    setDate: (state, action) => {
      state.date = action.payload;
    },
    setSelectedSkin: (state, action) => {
      state.selectedSkin = action.payload;
    },
    setIsSaving: (state, action) => {
      state.isSaving = action.payload;
    },
    setIsLoading: (state, action) => {
      state.isLoading = action.payload;
    },
    setTodayEntry: (state, action) => {
      state.todayEntry = action.payload;
    },
    setSelectedStage: (state, action) => {
      state.selectedStage = action.payload;
    },
    setIsSkinModalOpen: (state, action) => {
      state.isSkinModalOpen = action.payload;
    },
    setIsTodayDiaryOpen: (state, action) => {
      state.isTodayDiaryOpen = action.payload;
    },
    setLayoutBackground: (state, action) => {
      state.layoutBackground = action.payload;
    },
    resetDiaryState: () => initialState,
    setIsDecorating: (state, action) => {
      state.isDecorating = action.payload;
    },
    addDecorationItem: (state, action) => {
      // Save current state to history before making changes
      if (state.currentHistoryIndex < state.decorationHistory.length - 1) {
        // If we're not at the end of history, remove future history
        state.decorationHistory = state.decorationHistory.slice(0, state.currentHistoryIndex + 1);
      }

      // Add the new decoration item
      state.decorationItems.push(action.payload);

      // Save the new state (with the added item) to history
      const newState = [...state.decorationItems];
      state.decorationHistory.push(newState);

      if (state.decorationHistory.length > state.maxHistorySize) {
        state.decorationHistory = state.decorationHistory.slice(-state.maxHistorySize);
      }
      state.currentHistoryIndex = state.decorationHistory.length - 1;
    },
    updateDecorationItem: (state, action) => {
      const { id, updates } = action.payload;
      const itemIndex = state.decorationItems.findIndex(item => item.id === id);
      if (itemIndex !== -1) {
        // For updates, we don't save to history on every change to avoid too many history entries
        // History is saved when starting drag/resize operations
        state.decorationItems[itemIndex] = { ...state.decorationItems[itemIndex], ...updates };
        console.log('Decoration Updated:', { id, updates });
        console.log('Updated Item:', state.decorationItems[itemIndex]);
        console.log('All Decorations:', state.decorationItems);
      }
    },
    deleteDecorationItem: (state, action) => {
      // Save current state to history before making changes
      if (state.currentHistoryIndex < state.decorationHistory.length - 1) {
        // If we're not at the end of history, remove future history
        state.decorationHistory = state.decorationHistory.slice(0, state.currentHistoryIndex + 1);
      }

      // Delete the decoration item
      state.decorationItems = state.decorationItems.filter(item => item.id !== action.payload);
      if (state.selectedDecorationId === action.payload) {
        state.selectedDecorationId = null;
      }

      // Save the new state (after deletion) to history
      const newState = [...state.decorationItems];
      state.decorationHistory.push(newState);

      if (state.decorationHistory.length > state.maxHistorySize) {
        state.decorationHistory = state.decorationHistory.slice(-state.maxHistorySize);
      }
      state.currentHistoryIndex = state.decorationHistory.length - 1;
    },
    setSelectedDecorationId: (state, action) => {
      state.selectedDecorationId = action.payload;
    },
    clearDecorations: (state) => {
      state.decorationItems = [];
      state.selectedDecorationId = null;
      // Reset history when clearing decorations
      state.decorationHistory = [[]];
      state.currentHistoryIndex = 0;
    },
    setDecorationItems: (state, action) => {
      state.decorationItems = action.payload;
      // Reset history when loading decorations from API
      state.decorationHistory = [action.payload || []];
      state.currentHistoryIndex = 0;
      console.log('Decorations loaded from API:', action.payload);
    },
    setActiveTool: (state, action) => {
      state.activeTool = action.payload;
    },
    setBrushSettings: (state, action) => {
      state.brushSettings = { ...state.brushSettings, ...action.payload };
    },
    // Decoration submission actions
    setIsSubmittingDecoration: (state, action) => {
      state.isSubmittingDecoration = action.payload;
    },
    setDecorationSubmissionError: (state, action) => {
      state.decorationSubmissionError = action.payload;
    },
    setDecorationSubmissionSuccess: (state, action) => {
      state.decorationSubmissionSuccess = action.payload;
    },
    resetDecorationSubmissionState: (state) => {
      state.isSubmittingDecoration = false;
      state.decorationSubmissionError = null;
      state.decorationSubmissionSuccess = false;
    },
    // Undo/Redo actions
    saveDecorationHistory: (state) => {
      // Save current state to history (used before drag/resize operations)
      const currentState = [...state.decorationItems];
      if (state.currentHistoryIndex < state.decorationHistory.length - 1) {
        // If we're not at the end of history, remove future history
        state.decorationHistory = state.decorationHistory.slice(0, state.currentHistoryIndex + 1);
      }
      state.decorationHistory.push(currentState);
      if (state.decorationHistory.length > state.maxHistorySize) {
        state.decorationHistory = state.decorationHistory.slice(-state.maxHistorySize);
      }
      state.currentHistoryIndex = state.decorationHistory.length - 1;
    },
    undoDecoration: (state) => {
      if (state.currentHistoryIndex > 0) {
        state.currentHistoryIndex -= 1;
        state.decorationItems = [...state.decorationHistory[state.currentHistoryIndex]];
        state.selectedDecorationId = null; // Clear selection after undo
      }
    },
    redoDecoration: (state) => {
      if (state.currentHistoryIndex < state.decorationHistory.length - 1) {
        state.currentHistoryIndex += 1;
        state.decorationItems = [...state.decorationHistory[state.currentHistoryIndex]];
        state.selectedDecorationId = null; // Clear selection after redo
      }
    },
  },
});

export const {
  setSubject,
  setMessage,
  setDate,
  setSelectedSkin,
  setIsSaving,
  setIsLoading,
  setTodayEntry,
  setSelectedStage,
  setIsSkinModalOpen,
  setIsTodayDiaryOpen,
  setLayoutBackground,
  resetDiaryState,
  setIsDecorating,
  addDecorationItem,
  updateDecorationItem,
  deleteDecorationItem,
  setSelectedDecorationId,
  clearDecorations,
  setDecorationItems,
  setActiveTool,
  setBrushSettings,
  setIsSubmittingDecoration,
  setDecorationSubmissionError,
  setDecorationSubmissionSuccess,
  resetDecorationSubmissionState,
  saveDecorationHistory,
  undoDecoration,
  redoDecoration,
} = diarySlice.actions;

export const selectDiarySubject = (state) => state.diary.subject;
export const selectDiaryMessage = (state) => state.diary.message;
export const selectSelectedSkin = (state) => state.diary.selectedSkin;
export const selectIsSaving = (state) => state.diary.isSaving;
export const selectIsLoading = (state) => state.diary.isLoading;
export const selectTodayEntry = (state) => state.diary.todayEntry;
export const selectIsSkinModalOpen = (state) => state.diary.isSkinModalOpen;
export const selectLayoutBackground = (state) => state.diary.layoutBackground;
export const selectIsDecorating = (state) => state.diary.isDecorating;
export const selectDecorationItems = (state) => state.diary.decorationItems;
export const selectSelectedDecorationId = (state) => state.diary.selectedDecorationId;
export const selectActiveTool = (state) => state.diary.activeTool;
export const selectBrushSettings = (state) => state.diary.brushSettings;
export const selectIsSubmittingDecoration = (state) => state.diary.isSubmittingDecoration;
export const selectDecorationSubmissionError = (state) => state.diary.decorationSubmissionError;
export const selectDecorationSubmissionSuccess = (state) => state.diary.decorationSubmissionSuccess;
export const selectCanUndo = (state) => state.diary.currentHistoryIndex > 0;
export const selectCanRedo = (state) => state.diary.currentHistoryIndex < state.diary.decorationHistory.length - 1;

export default diarySlice.reducer;