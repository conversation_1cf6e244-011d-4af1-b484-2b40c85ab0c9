'use client';
import React, { useState, useEffect } from 'react';
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import { Formik, Form, Field, ErrorMessage } from 'formik';
import * as Yup from 'yup';
import { Icon } from '@iconify/react';
import { toast } from 'react-hot-toast';
import useDataFetch from '@/hooks/useDataFetch';
import api from '@/lib/api';
import FormSelect from '@/components/form/FormSelect';
import FormInput from '@/components/form/FormInput';

const TutorProfileEdit = () => {
  const router = useRouter();
  const { id } = useParams();
  const searchParams = useSearchParams();
  const section = searchParams.get('section') || 'personal'; // Default to personal if not specified
  
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [editingId, setEditingId] = useState(null);
  const [isAddingNew, setIsAddingNew] = useState(false);
  const [educations, setEducations] = useState([]);

  // Fetch tutor profile data
  const { data: response, isLoading, error, refetch } = useDataFetch({
    queryKey: ['tutor-profile', id],
    endPoint: `/profiles/tutor/${id}`,
  });

  // Extract the tutor profile data from the response
  const tutorProfile = response?.data || response;

  // Load education data from profile
  useEffect(() => {
    if (tutorProfile?.education) {
      setEducations(tutorProfile.education.map(edu => ({
        id: edu.id || Date.now() + Math.random(),
        degree: edu.degree || '',
        institution: edu.institution || '',
        fieldOfStudy: edu.fieldOfStudy || '',
        startDate: edu.startDate || '',
        endDate: edu.endDate || '',
        isCurrent: edu.isCurrent || false,
        grade: edu.grade || '',
        activities: edu.activities || '',
        description: edu.description || '',
        location: edu.location || ''
      })));
    }
    
    // If section is education and no educations exist, auto-open the add form
    if (section === 'education' && tutorProfile?.education?.length === 0) {
      setIsAddingNew(true);
    }
  }, [tutorProfile, section]);

  // Check for section parameter and scroll to appropriate section
  useEffect(() => {
    if (typeof window !== 'undefined') {
      if (section === 'education') {
        // Slight delay to ensure the component is fully rendered
        setTimeout(() => {
          const educationSection = document.getElementById('education');
          if (educationSection) {
            educationSection.scrollIntoView({ behavior: 'smooth' });
          }
        }, 300);
      }
    }
  }, [section]);

  const completionStatusOptions = [
    { value: 'true', label: 'Currently Enrolled' },
    { value: 'false', label: 'Completed' }
  ];

  // Validation schema
  const validationSchema = Yup.object().shape({
    firstName: Yup.string().required('First name is required'),
    lastName: Yup.string().required('Last name is required'),
    email: Yup.string().email('Invalid email').required('Email is required'),
    userId: Yup.string().required('ID is required'),
    phoneNumber: Yup.string().required('Phone number is required'),
    // alternativeNumber: Yup.string(),
    gender: Yup.string().required('Gender is required'),
    dateOfBirth: Yup.date().required('Date of birth is required'),
    address: Yup.string().required('Address is required'),
    fathersName: Yup.string(),
    mothersName: Yup.string(),
    biography: Yup.string(),
    bloodGroup: Yup.string(),
    religion: Yup.string(),
    coachingExperience: Yup.string()
  });

  // Education validation schema
  const educationSchema = Yup.object().shape({
    degree: Yup.string().required('Degree/Title is required'),
    institution: Yup.string().required('Institution is required'),
    fieldOfStudy: Yup.string().required('Field of study is required'),
    startDate: Yup.date().required('Start date is required'),
    isCurrent: Yup.boolean(),
    endDate: Yup.date().when('isCurrent', {
      is: false,
      then: schema => schema.required('End date is required'),
      otherwise: schema => schema.nullable()
    }),
    grade: Yup.string(),
    activities: Yup.string(),
    description: Yup.string(),
    location: Yup.string()
  });

  // Handle form submission
  const handleSubmit = async (values) => {
    setIsSubmitting(true);
    try {
      // Prepare data for API - only include fields that the API accepts
      const apiData = {
        name: `${values.firstName} ${values.lastName}`,
        phoneNumber: values.phoneNumber,
        address: values.address,
        gender: values.gender,
        dateOfBirth: values.dateOfBirth,
        bio: values.biography
      };

      console.log('Submitting data:', apiData);
      const response = await api.patch('/users/profile', apiData);
      console.log('Response:', response);
       
      router.push(`/dashboard/tutor-profile/${id}`);
    } catch (error) {
      console.error('Error updating profile:', error);
      
      // More detailed error handling
      if (error.response) {
        console.error('Error response data:', error.response.data);
        
        if (error.response.data?.validationErrors) {
          // Display all validation errors
          const validationErrors = error.response.data.validationErrors;
          const errorMessages = Object.entries(validationErrors)
            .map(([field, errors]) => `${field}: ${errors.join(', ')}`)
            .join('\n');
          
          console.log(`Validation errors:\n${errorMessages}`);
        } else if (error.response.data?.message) {
          console.log(`Error: ${error.response.data.message}`);
        } else {
          console.log(`Failed to update profile: ${error.response.status} ${error.response.statusText}`);
        }
      } else if (error.request) {
        console.log('No response received from server. Please check your connection.');
      } else {
        console.log(`Error: ${error.message}`);
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle education form submission
  const handleEducationSubmit = async (values, { setSubmitting, resetForm }) => {
    try {
      // Format the data for API
      const educationData = {
        ...values,
        isCurrent: values.isCurrent === 'true' || values.isCurrent === true
      };
      
      // If editing existing education
      if (editingId) {
        // Update local state
        setEducations(educations.map(edu => 
          edu.id === editingId ? { ...educationData, id: edu.id } : edu
        ));
        
        // Call API to update education
        await api.patch(`/users/education/${editingId}`, educationData);
        setEditingId(null);
      } else {
        // Add new education
        const response = await api.post('/users/education', educationData);
        const newEducation = response.data;
        
        // Update local state with the returned data (including ID)
        setEducations([...educations, { ...newEducation, id: newEducation.id || Date.now() }]);
        setIsAddingNew(false);
      }
      
      resetForm();
    } catch (error) {
      console.error('Error updating education:', error); 
    } finally {
      setSubmitting(false);
    }
  };

  const handleDelete = async (id) => {
    if (!confirm('Are you sure you want to delete this education?')) return;
    
    try {
      await api.delete(`/users/education/${id}`);
      setEducations(educations.filter(edu => edu.id !== id)); 
    } catch (error) {
      console.error('Error deleting education:', error); 
    }
  };

  const newEducationInitialValues = {
    degree: "",
    institution: "",
    fieldOfStudy: "",
    startDate: "",
    endDate: "",
    isCurrent: "false",
    grade: "",
    activities: "",
    description: "",
    location: ""
  };

  const renderEducationItem = (education) => {
    if (editingId === education.id) {
      return (
        <Formik
          key={education.id}
          initialValues={{
            ...education,
            isCurrent: education.isCurrent ? 'true' : 'false'
          }}
          validationSchema={educationSchema}
          onSubmit={handleEducationSubmit}
        >
          {({ isSubmitting, values, setFieldValue }) => (
            <Form className="bg-white border-b pb-4 mb-6">
              <div className="grid md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <FormInput label="DEGREE/TITLE" name="degree" type="text" />
                  <FormInput label="INSTITUTION" name="institution" type="text" />
                  <FormSelect
                    label="COMPLETION STATUS"
                    name="isCurrent"
                    options={completionStatusOptions}
                  />
                  <FormInput label="START DATE" name="startDate" type="date" />
                  {values.isCurrent === 'false' && (
                    <FormInput label="END DATE" name="endDate" type="date" />
                  )}
                </div>
                <div className="space-y-4">
                  <FormInput label="FIELD OF STUDY" name="fieldOfStudy" type="text" />
                  <FormInput label="GRADE" name="grade" type="text" />
                  <FormInput label="ACTIVITIES" name="activities" type="text" />
                  <FormInput label="LOCATION" name="location" type="text" />
                  <FormInput label="DESCRIPTION" name="description" type="textarea" rows={3} />
                </div>
              </div>
              <div className="flex justify-end gap-2 mt-4 lg:mt-0">
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="px-4 py-2 bg-yellow-500 text-white rounded-md hover:bg-yellow-600"
                >
                  {isSubmitting ? 'Saving...' : 'Save'}
                </button>
                <button
                  type="button"
                  onClick={() => setEditingId(null)}
                  className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300"
                >
                  Cancel
                </button>
              </div>
            </Form>
          )}
        </Formik>
      );
    }

    return (
      <div key={education.id} className="bg-white border-b pb-4 mb-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <Icon icon="material-symbols:school" className="w-6 h-6" />
            {education.degree}
          </h3>
          <div className="flex gap-2">
            <button
              type="button"
              onClick={() => setEditingId(education.id)}
              className="p-2 hover:bg-gray-100 rounded-full"
            >
              <Icon icon="material-symbols:edit-outline" className="w-5 h-5" />
            </button>
            <button
              type="button"
              onClick={() => handleDelete(education.id)}
              className="p-2 hover:bg-gray-100 rounded-full text-red-500"
            >
              <Icon icon="material-symbols:delete-outline" className="w-5 h-5" />
            </button>
          </div>
        </div>
        <div className="grid md:grid-cols-2 gap-6">
          <div>
            <p className="text-base font-semibold text-gray-600">INSTITUTION</p>
            <p className="mb-3">{education.institution}</p>
            <p className="text-base font-semibold text-gray-600">COMPLETION STATUS</p>
            <p className="mb-3">{education.isCurrent ? 'Currently Enrolled' : 'Completed'}</p>
            <p className="text-base font-semibold text-gray-600">DURATION</p>
            <p>
              {education.startDate ? new Date(education.startDate).toLocaleDateString() : 'N/A'} - 
              {education.isCurrent ? ' Present' : education.endDate ? ` ${new Date(education.endDate).toLocaleDateString()}` : ' N/A'}
            </p>
          </div>
          <div>
            <p className="text-base font-semibold text-gray-600">FIELD OF STUDY</p>
            <p className="mb-3">{education.fieldOfStudy}</p>
            {education.grade && (
              <>
                <p className="text-base font-semibold text-gray-600">GRADE</p>
                <p className="mb-3">{education.grade}</p>
              </>
            )}
            {education.activities && (
              <>
                <p className="text-base font-semibold text-gray-600">ACTIVITIES</p>
                <p className="mb-3">{education.activities}</p>
              </>
            )}
            {education.location && (
              <>
                <p className="text-base font-semibold text-gray-600">LOCATION</p>
                <p className="mb-3">{education.location}</p>
              </>
            )}
          </div>
        </div>
        {education.description && (
          <div className="mt-3">
            <p className="text-base font-semibold text-gray-600">DESCRIPTION</p>
            <p>{education.description}</p>
          </div>
        )}
      </div>
    );
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-[60vh]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-yellow-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <Icon icon="mdi:alert-circle" className="text-6xl text-red-500 mx-auto mb-4" />
        <h2 className="text-2xl font-bold text-gray-800 mb-2">Error Loading Profile</h2>
        <p className="text-gray-600 mb-6">{error.message || "There was an error loading the tutor profile."}</p>
        <button
          onClick={() => router.back()}
          className="px-4 py-2 bg-yellow-500 text-white rounded-lg hover:bg-yellow-600 transition-colors"
        >
          Go Back
        </button>
      </div>
    );
  }

  if (!tutorProfile) {
    return (
      <div className="text-center py-12">
        <Icon icon="mdi:account-alert" className="text-6xl text-yellow-500 mx-auto mb-4" />
        <h2 className="text-2xl font-bold text-gray-800 mb-2">Tutor Not Found</h2>
        <p className="text-gray-600 mb-6">The tutor profile you're looking for doesn't exist or you don't have access to it.</p>
        <button
          onClick={() => router.back()}
          className="px-4 py-2 bg-yellow-500 text-white rounded-lg hover:bg-yellow-600 transition-colors"
        >
          Go Back
        </button>
      </div>
    );
  }

  // Split name into first and last name
  const nameParts = tutorProfile.name ? tutorProfile.name.split(' ') : ['', ''];
  const firstName = nameParts[0] || '';
  const lastName = nameParts.slice(1).join(' ') || '';

  // Initial form values
  const initialValues = {
    firstName: firstName,
    lastName: lastName,
    email: tutorProfile.email || '',
    userId: tutorProfile.userId || '',
    phoneNumber: tutorProfile.phoneNumber || '',
    alternativeNumber: tutorProfile.alternativeNumber || '',
    gender: tutorProfile.gender || '',
    dateOfBirth: tutorProfile.dateOfBirth || '',
    fathersName: tutorProfile.fathersName || '',
    mothersName: tutorProfile.mothersName || '',
    address: tutorProfile.address || '',
    biography: tutorProfile.bio || '',
    bloodGroup: tutorProfile.bloodGroup || '',
    religion: tutorProfile.religion || '',
    coachingExperience: tutorProfile.coachingExperience || ''
  };

  return (
    <div className="max-w-7xl mx-auto bg-white min-h-screen pb-8">
      {/* Back button */}
      <div className="flex items-center p-3 text-gray-600 cursor-pointer border-b bg-white shadow-sm">
        <Icon icon="mdi:arrow-left" className="mr-2" onClick={() => router.back()} />
        <span className="font-medium">Edit Tutor</span>
      </div>

      <div className="p-6">
        {/* Personal Details Form - Only show if section is personal */}
        {section === 'personal' && (
          <Formik
            initialValues={initialValues}
            validationSchema={validationSchema}
            onSubmit={handleSubmit}
          >
            {({ errors, touched }) => (
              <Form>
                <div className="bg-white rounded-lg shadow-md p-6 mb-6 border">
                  <h3 className="text-lg font-semibold mb-4">Personal & Contact Details</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    {/* First column */}
                    <div>
                      <div className="mb-6">
                        <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 mb-1">
                          First Name <span className="text-red-500">*</span>
                        </label>
                        <Field
                          type="text"
                          id="firstName"
                          name="firstName"
                          placeholder="Uzumaki"
                          className={`w-full p-2 border rounded-md ${errors.firstName && touched.firstName ? 'border-red-500' : 'border-gray-300'}`}
                        />
                        <ErrorMessage name="firstName" component="div" className="text-red-500 text-sm mt-1" />
                      </div>

                      <div className="mb-6">
                        <label htmlFor="userId" className="block text-sm font-medium text-gray-700 mb-1">
                          ID <span className="text-red-500">*</span>
                        </label>
                        <Field
                          type="text"
                          id="userId"
                          name="userId"
                          placeholder="uzumaki-naruto"
                          className={`w-full p-2 border rounded-md ${errors.userId && touched.userId ? 'border-red-500' : 'border-gray-300'}`}
                          disabled
                        />
                        <ErrorMessage name="userId" component="div" className="text-red-500 text-sm mt-1" />
                      </div>

                      <div className="mb-10">
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Select Gender <span className="text-red-500">*</span>
                        </label>
                        <div className="flex gap-6 mt-2">
                          <label className="flex items-center">
                            <Field
                              type="radio"
                              name="gender"
                              value="male"
                              className="mr-2"
                            />
                            Male
                          </label>
                          <label className="flex items-center">
                            <Field
                              type="radio"
                              name="gender"
                              value="female"
                              className="mr-2"
                            />
                            Female
                          </label>
                        </div>
                        <ErrorMessage name="gender" component="div" className="text-red-500 text-sm mt-1" />
                      </div>

                      <div className="mb-6">
                        <label htmlFor="fathersName" className="block text-sm font-medium text-gray-700 mb-1">
                          Father's Name
                        </label>
                        <Field
                          type="text"
                          id="fathersName"
                          name="fathersName"
                          placeholder="Write your father's name"
                          className="w-full p-2 border border-gray-300 rounded-md"
                        />
                      </div>

                      <div className="mb-6">
                        <label htmlFor="biography" className="block text-sm font-medium text-gray-700 mb-1">
                          Biography
                        </label>
                        <Field
                          as="textarea"
                          id="biography"
                          name="biography"
                          placeholder="Write a short BIO"
                          rows="4"
                          className="w-full p-2 border border-gray-300 rounded-md"
                        />
                      </div>
                    </div>

                    {/* Second column */}
                    <div>
                      <div className="mb-6">
                        <label htmlFor="lastName" className="block text-sm font-medium text-gray-700 mb-1">
                          Last Name <span className="text-red-500">*</span>
                        </label>
                        <Field
                          type="text"
                          id="lastName"
                          name="lastName"
                          placeholder="Naruto"
                          className={`w-full p-2 border rounded-md ${errors.lastName && touched.lastName ? 'border-red-500' : 'border-gray-300'}`}
                        />
                        <ErrorMessage name="lastName" component="div" className="text-red-500 text-sm mt-1" />
                      </div>

                      <div className="mb-6">
                        <label htmlFor="phoneNumber" className="block text-sm font-medium text-gray-700 mb-1">
                          Phone Number <span className="text-red-500">*</span>
                        </label>
                        <Field
                          type="text"
                          id="phoneNumber"
                          name="phoneNumber"
                          placeholder="123456789"
                          className={`w-full p-2 border rounded-md ${errors.phoneNumber && touched.phoneNumber ? 'border-red-500' : 'border-gray-300'}`}
                        />
                        <ErrorMessage name="phoneNumber" component="div" className="text-red-500 text-sm mt-1" />
                      </div>

                      <div className="mb-6">
                        <label htmlFor="dateOfBirth" className="block text-sm font-medium text-gray-700 mb-1">
                          Select Date Of Birth <span className="text-red-500">*</span>
                        </label>
                        <Field
                          type="date"
                          id="dateOfBirth"
                          name="dateOfBirth"
                          className={`w-full p-2 border rounded-md ${errors.dateOfBirth && touched.dateOfBirth ? 'border-red-500' : 'border-gray-300'}`}
                        />
                        <ErrorMessage name="dateOfBirth" component="div" className="text-red-500 text-sm mt-1" />
                      </div>

                      <div className="mb-6">
                        <label htmlFor="mothersName" className="block text-sm font-medium text-gray-700 mb-1">
                          Mother's Name
                        </label>
                        <Field
                          type="text"
                          id="mothersName"
                          name="mothersName"
                          placeholder="Write your mother's name"
                          className="w-full p-2 border border-gray-300 rounded-md"
                        />
                      </div>

                      <div className="mb-6">
                        <label htmlFor="bloodGroup" className="block text-sm font-medium text-gray-700 mb-1">
                          Blood Group
                        </label>
                        <Field
                          as="select"
                          id="bloodGroup"
                          name="bloodGroup"
                          className="w-full p-2 border border-gray-300 rounded-md"
                        >
                          <option value="">Select Blood Group</option>
                          <option value="A+">A+</option>
                          <option value="A-">A-</option>
                          <option value="B+">B+</option>
                          <option value="B-">B-</option>
                          <option value="AB+">AB+</option>
                          <option value="AB-">AB-</option>
                          <option value="O+">O+</option>
                          <option value="O-">O-</option>
                        </Field>
                      </div>
                    </div>

                    {/* Third column */}
                    <div>
                      <div className="mb-6">
                        <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                          Email Address <span className="text-red-500">*</span>
                        </label>
                        <Field
                          type="email"
                          id="email"
                          name="email"
                          placeholder="<EMAIL>"
                          className={`w-full p-2 border rounded-md ${errors.email && touched.email ? 'border-red-500' : 'border-gray-300'}`}
                          disabled
                        />
                        <ErrorMessage name="email" component="div" className="text-red-500 text-sm mt-1" />
                      </div>

                      {/* <div className="mb-6 ">
                        <label htmlFor="alternativeNumber" className="block text-sm font-medium text-gray-700 mb-1">
                          Alternative Number 
                        </label>
                        <Field
                          type="text"
                          id="alternativeNumber"
                          name="alternativeNumber"
                          placeholder="456789123"
                          className={`w-full p-2 border rounded-md ${errors.alternativeNumber && touched.alternativeNumber ? 'border-red-500' : 'border-gray-300'}`}
                        />
                        <ErrorMessage name="alternativeNumber" component="div" className="text-red-500 text-sm mt-1" />
                      </div> */}

                      <div className="mb-6">
                        <label htmlFor="address" className="block text-sm font-medium text-gray-700 mb-1">
                          Address <span className="text-red-500">*</span>
                        </label>
                        <Field
                          type="text"
                          id="address"
                          name="address"
                          placeholder="775 Rolling Green Rd."
                          className={`w-full p-2 border rounded-md ${errors.address && touched.address ? 'border-red-500' : 'border-gray-300'}`}
                        />
                        <ErrorMessage name="address" component="div" className="text-red-500 text-sm mt-1" />
                      </div>

                      <div className="mb-6">
                        <label htmlFor="religion" className="block text-sm font-medium text-gray-700 mb-1">
                          Religion
                        </label>
                        <Field
                          type="text"
                          id="religion"
                          name="religion"
                          className="w-full p-2 border border-gray-300 rounded-md"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                <div className="mt-6 flex justify-end gap-3">
                  <button
                    type="button"
                    onClick={() => router.back()}
                    className="px-6 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="px-6 py-2 bg-yellow-400 text-black font-medium rounded-md hover:bg-yellow-500 transition-colors disabled:bg-yellow-300"
                  >
                    Update Details
                  </button>
                </div>
              </Form>
            )}
          </Formik>
        )}

        {/* Education Section - Only show if section is education */}
        {section === 'education' && (
          <div id="education" className="space-y-6 border rounded-lg shadow-md p-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold">Education</h3>
            </div>

            {/* Display existing educations */}
            {educations.length > 0 ? (
              educations.map(education => renderEducationItem(education))
            ) : (
              <p className="text-gray-500">No education information added yet.</p>
            )}

            {/* Add New Education Form */}
            {isAddingNew && (
              <Formik
                initialValues={newEducationInitialValues}
                validationSchema={educationSchema}
                onSubmit={handleEducationSubmit}
              >
                {({ isSubmitting, values }) => (
                  <Form className="bg-white mb-6 lg:p-4 border rounded-md">
                    <div className="grid md:grid-cols-2 gap-6">
                      <div className="space-y-4">
                        <FormInput label="DEGREE/TITLE" name="degree" type="text" />
                        <FormInput label="INSTITUTION" name="institution" type="text" />
                        <FormSelect
                          label="COMPLETION STATUS"
                          name="isCurrent"
                          options={completionStatusOptions}
                        />
                        <FormInput label="START DATE" name="startDate" type="date" />
                        {values.isCurrent === 'false' && (
                          <FormInput label="END DATE" name="endDate" type="date" />
                        )}
                      </div>
                      <div className="space-y-4">
                        <FormInput label="FIELD OF STUDY" name="fieldOfStudy" type="text" />
                        <FormInput label="GRADE" name="grade" type="text" />
                        <FormInput label="ACTIVITIES" name="activities" type="text" />
                        <FormInput label="LOCATION" name="location" type="text" />
                        <FormInput label="DESCRIPTION" name="description" type="textarea" rows={3} />
                      </div>
                    </div>
                    <div className="flex gap-2 mt-4">
                      <button
                        type="submit"
                        disabled={isSubmitting}
                        className="px-4 py-2 bg-yellow-500 text-white rounded-md hover:bg-yellow-600"
                      >
                        {isSubmitting ? 'Adding...' : 'Add Education'}
                      </button>
                      <button
                        type="button"
                        onClick={() => setIsAddingNew(false)}
                        className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300"
                      >
                        Cancel
                      </button>
                    </div>
                  </Form>
                )}
              </Formik>
            )}

            {/* Add New Education Button */}
            {!isAddingNew && (
              <button
                type="button"
                onClick={() => setIsAddingNew(true)}
                className="w-full px-4 py-2 bg-yellow-400 text-white rounded-md hover:bg-yellow-500"
              >
                Add New Education
              </button>
            )}
            
            {/* Back button */}
            <div className="mt-6 flex justify-end">
              <button
                type="button"
                onClick={() => router.back()}
                className="px-6 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors"
              >
                Back to Profile
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default TutorProfileEdit;









