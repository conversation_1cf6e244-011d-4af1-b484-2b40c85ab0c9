'use client';
import React, { useState, useEffect } from 'react';
import NewTablePage from "@/components/form/NewTablePage";
import useDataFetch from '@/hooks/useDataFetch';
import { useRouter } from 'next/navigation';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import api from '@/lib/api';
import Modal from '@/components/Modal';
import Button from '@/components/Button';
import { Icon } from '@iconify/react';

const Block = () => {
  const router = useRouter();
  const queryClient = useQueryClient();
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const [deleteData, setDeleteData] = useState(null);
  const [openDropdownId, setOpenDropdownId] = useState(null);

  const {data, isLoading, refetch} = useDataFetch({
    queryKey: ['block-', currentPage, rowsPerPage],
    endPoint: '/play/block/admin/games',
    params: { page: currentPage, limit: rowsPerPage },
  });

  // Debug: Log the data structure
  console.log('Block games data:', data);
  console.log('Block games items:', data?.items);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (openDropdownId && !event.target.closest('.status-dropdown')) {
        setOpenDropdownId(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [openDropdownId]);

  // Delete mutation
  const deleteBlockMutation = useMutation({
    mutationFn: async (id) => {
      const response = await api.delete(`/play/block/admin/games/${id}`);
      return response;
    },
    onSuccess: () => {
      queryClient.invalidateQueries(['block-']);
      setDeleteData(null);
      refetch();
    }
  });

  // Handle delete confirmation
  const handleDelete = () => {
    if (deleteData) {
      deleteBlockMutation.mutate(deleteData.id);
    }
  };

  // Toggle status mutation
  const toggleStatusMutation = useMutation({
    mutationFn: async ({ id, is_active }) => {
      const response = await api.patch(`/play/block/admin/games/${id}/toggle-status`, {
        is_active: is_active
      });
      return response;
    },
    onSuccess: () => {
      queryClient.invalidateQueries(['block-']);
      refetch();
    }
  });

  // Handle status toggle
  const handleToggleStatus = (row) => {
    if (!row || !row.id) {
      console.error('Invalid row data for toggle status:', row);
      return;
    }

    const newStatus = !row.is_active;
    toggleStatusMutation.mutate({
      id: row.id,
      is_active: newStatus
    });
  };

  // Define columns for the table
  const columns = [
    {
      label: 'Title',
      field: 'title',
    },
    {
      label: 'Score',
      field: 'score',
    },
    {
      label: 'Sentence Count',
      field: 'sentence_count',
    },
    {
      label: 'Status',
      field: 'is_active',
      cellRenderer: (value, row) => {
        if (!row) return 'Unknown';

        // Debug: Log the row data to see what fields are available
        console.log('Row data for status:', row);
        console.log('Status value:', value);
        console.log('is_active field:', row.is_active);
        console.log('status field:', row.status);
        console.log('active field:', row.active);

        // Try different possible field names for status
        const statusValue = value !== undefined ? value :
                           row.is_active !== undefined ? row.is_active :
                           row.status !== undefined ? row.status :
                           row.active !== undefined ? row.active :
                           false;

        const isActive = Boolean(statusValue);
        const isUpdating = toggleStatusMutation.isPending;

        return (
          <div className="relative status-dropdown">
            {/* Status Dropdown Button */}
            <button
              onClick={() => setOpenDropdownId(openDropdownId === row.id ? null : row.id)}
              disabled={isUpdating}
              className={`flex items-center justify-between w-24 px-3 py-1 rounded text-xs font-medium transition-colors ${
                isActive
                  ? 'bg-green-100 text-green-800 hover:bg-green-200'
                  : 'bg-red-100 text-red-800 hover:bg-red-200'
              } ${isUpdating ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
            >
              <span>
                {isUpdating ? 'Updating...' : (isActive ? 'Active' : 'Inactive')}
              </span>
              <Icon
                icon="lucide:chevron-down"
                className={`w-4 h-4 ml-2 transition-transform ${
                  openDropdownId === row.id ? 'rotate-180' : ''
                }`}
              />
            </button>

            {/* Dropdown Menu */}
            {openDropdownId === row.id && !isUpdating && (
              <div className="absolute top-full left-0 mt-1 w-24 bg-white border border-gray-200 rounded-md shadow-lg z-10">
                <button
                  onClick={() => {
                    if (!isActive) {
                      handleToggleStatus(row);
                    }
                    setOpenDropdownId(null);
                  }}
                  className={`w-full px-3 py-2 text-left text-xs hover:bg-gray-50 ${
                    isActive ? 'bg-green-50 text-green-800 font-medium' : 'text-gray-700'
                  }`}
                >
                  Active
                </button>
                <button
                  onClick={() => {
                    if (isActive) {
                      handleToggleStatus(row);
                    }
                    setOpenDropdownId(null);
                  }}
                  className={`w-full px-3 py-2 text-left text-xs hover:bg-gray-50 ${
                    !isActive ? 'bg-red-50 text-red-800 font-medium' : 'text-gray-700'
                  }`}
                >
                  Inactive
                </button>
              </div>
            )}
          </div>
        );
      },
    },
  ];

  // Define actions
  const actions = [
    {
      name: 'Edit',
      icon: 'lucide:edit',
      onClick: (row) => router.push(`/dashboard/module-management/hec-play/block/edit/${row.id}`),
    },
    {
      name: 'Delete',
      icon: 'lucide:trash',
      onClick: (row) => setDeleteData(row),
    },
  ];

  // Handle page change
  const handleChangePage = (page) => {
    setCurrentPage(page);
  };

  return (
    <div className="container mx-auto p-4">
      <NewTablePage
        title="Block Games"
        createButton="Add Block Game"
        createBtnLink="/dashboard/module-management/hec-play/block/add"
        columns={columns}
        data={data?.items || []}
        actions={actions}
        currentPage={currentPage}
        changePage={handleChangePage}
        totalItems={data?.totalItems || 0}
        rowsPerPage={rowsPerPage}
        setRowsPerPage={setRowsPerPage}
        totalPages={data?.totalPages || 1}
        showCheckboxes={false}
        showSearch={true}
        showNameFilter={true}
        showSortFilter={true}
        isLoading={isLoading}
      />

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={!!deleteData}
        onClose={() => setDeleteData(null)}
        position="center"
        title="Delete Block Game"
        width="md"
      >
        <div className="p-4">
          <p className="text-gray-600 mb-6">
            Are you sure you want to delete "<strong>{deleteData?.title}</strong>"?
            This action cannot be undone.
          </p>

          <div className="flex justify-end gap-3">
            <Button
              buttonText="Cancel"
              onClick={() => setDeleteData(null)}
              className="bg-gray-200 hover:bg-gray-300 text-gray-800"
            />
            <Button
              buttonText={deleteBlockMutation.isPending ? "Deleting..." : "Delete"}
              onClick={handleDelete}
              disabled={deleteBlockMutation.isPending}
              className="bg-red-600 hover:bg-red-700 text-white"
            />
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default Block;
