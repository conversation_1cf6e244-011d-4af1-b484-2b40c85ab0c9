'use client';

import React, { useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Icon } from '@iconify/react';

const PaymentSuccess = () => {
  const router = useRouter();
  const searchParams = useSearchParams();

  // Extract payment result parameters
  const paymentData = {
    ordr_idxx: searchParams.get('ordr_idxx'),
    good_name: searchParams.get('good_name'),
    good_mny: searchParams.get('good_mny'),
    buyr_name: searchParams.get('buyr_name'),
    tno: searchParams.get('tno'),
    amount: searchParams.get('amount'),
    app_time: searchParams.get('app_time'),
    card_name: searchParams.get('card_name'),
    card_no: searchParams.get('card_no'),
  };

  useEffect(() => {
    // You can add analytics tracking here
    console.log('Payment successful:', paymentData);
  }, []);

  const handleContinue = () => {
    router.push('/dashboard');
  };

  const handleViewOrder = () => {
    router.push('/orders');
  };

  return (
    <div className="min-h-[85vh] py-8 max-w-4xl mx-auto px-5 xl:px-0">
      <div className="bg-white rounded-lg shadow-lg p-8 text-center">
        {/* Success Icon */}
        <div className="mb-6">
          <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Icon 
              icon="material-symbols:check-circle" 
              className="text-green-600 text-4xl"
            />
          </div>
          <h1 className="text-3xl font-bold text-gray-800 mb-2">
            Payment Successful!
          </h1>
          <p className="text-gray-600">
            Thank you for your purchase. Your payment has been processed successfully.
          </p>
        </div>

        {/* Payment Details */}
        {paymentData.ordr_idxx && (
          <div className="bg-gray-50 rounded-lg p-6 mb-6 text-left max-w-md mx-auto">
            <h3 className="font-semibold mb-4 text-center">Payment Details</h3>
            <div className="space-y-3">
              {paymentData.ordr_idxx && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Order ID:</span>
                  <span className="font-medium">{paymentData.ordr_idxx}</span>
                </div>
              )}
              {paymentData.good_name && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Product:</span>
                  <span className="font-medium">{paymentData.good_name}</span>
                </div>
              )}
              {paymentData.good_mny && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Amount:</span>
                  <span className="font-medium">₩{parseInt(paymentData.good_mny).toLocaleString()}</span>
                </div>
              )}
              {paymentData.buyr_name && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Buyer:</span>
                  <span className="font-medium">{paymentData.buyr_name}</span>
                </div>
              )}
              {paymentData.tno && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Transaction ID:</span>
                  <span className="font-medium">{paymentData.tno}</span>
                </div>
              )}
              {paymentData.app_time && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Payment Time:</span>
                  <span className="font-medium">{paymentData.app_time}</span>
                </div>
              )}
              {paymentData.card_name && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Card Type:</span>
                  <span className="font-medium">{paymentData.card_name}</span>
                </div>
              )}
              {paymentData.card_no && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Card Number:</span>
                  <span className="font-medium">****-****-****-{paymentData.card_no.slice(-4)}</span>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <button
            onClick={handleContinue}
            className="px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
          >
            Continue to Dashboard
          </button>
          <button
            onClick={handleViewOrder}
            className="px-8 py-3 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors font-medium"
          >
            View Orders
          </button>
        </div>

        {/* Additional Information */}
        <div className="mt-8 p-4 bg-blue-50 rounded-lg">
          <p className="text-sm text-blue-800">
            <Icon icon="material-symbols:info" className="inline mr-1" />
            A confirmation email has been sent to your registered email address.
          </p>
        </div>
      </div>
    </div>
  );
};

export default PaymentSuccess;
