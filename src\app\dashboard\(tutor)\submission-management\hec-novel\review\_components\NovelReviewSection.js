'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import ContentEditable from 'react-contenteditable';
import api from '@/lib/api';
import { formatDate } from '@/utils/dateFormatter';
import HistoryModal from '@/app/(main)/novel/_components/HistoryModal';
import SimpleTiptapEditor from '@/components/form/SimpleTiptapEditor';
import FeedbackModal from '@/components/FeedbackModal';

const isHtmlEmpty = (html) => {
  if (!html) return true;
  const strippedHtml = html.replace(/<[^>]*>?/gm, '').trim();
  return strippedHtml.length === 0;
};

const NovelReviewSection = ({ data, entryId }) => {
  const router = useRouter();
  const editorRef = useRef(null);
  // Fix: Initialize with correction text if it exists, otherwise use original content
  const [correctionHtml, setCorrectionHtml] = useState(
    data?.correction?.correction || data?.content || ''
  );
  const [score, setScore] = useState('');
  const [isFeedbackModalOpen, setIsFeedbackModalOpen] = useState(false);
  const [isSubmittingReview, setIsSubmittingReview] = useState(false);
  const [isCompletingReview, setIsCompletingReview] = useState(false);
  const contentEditableRef = useRef(null);
  const isCorrectionReviewed = !!data?.correction?.correctionText;
  const hasExistingScore = !!data?.correction?.score;
  const [showModal, setShowModal] = useState(false);

  useEffect(() => {
    // Fix: Update with correction text if it exists, otherwise use original content
    setCorrectionHtml(data?.correction?.correction || data?.content || '');
  }, [data?.content, data?.correction?.correction]);

  useEffect(() => {
    if (data?.correction?.score && !score) {
      setScore(data.correction.score.toString());
    }
  }, [data?.correction?.score, score]);

  const handleCorrectionChange = (evt) => {
    setCorrectionHtml(evt.target.value);
  };

  const prepareToTypeBlue = () => {
    document.execCommand('foreColor', false, 'blue');
  };

  // // Fixed validation function
  // const isSubmitDisabled = () => {
  //   const hasCorrection = !correctionHtml;
  //   const hasScore = data?.score || data?.correction?.score;
  //   // Require at least correction text to submit
  //   return hasCorrection || isSubmittingReview || !hasScore;
  // };

  // console.log(isSubmitDisabled());

  const submitReview = async () => {
    try {
      setIsSubmittingReview(true);

      // Strip HTML tags from correction content and ensure it's a plain string
      // const plainTextCorrection = correctionHtml
      //   ? correctionHtml.replace(/<[^>]*>?/gm, '').trim()
      //   : '';

      // Check if this is an update (has existing score) or new review
      if (hasExistingScore) {
        // Update existing review - only send correction, NO score
        const payload = {
          correction: correctionHtml,
        };

        console.log('Updating review with payload:', payload);

        const response = await api.put(
          `/tutor/novel/entries/${entryId}/correction`,
          payload
        );

        if (response.success) {
          router.push('/dashboard/submission-management/hec-novel');
        } else {
          throw new Error(response.message);
        }
      } else {
        // New review - send both correction and score
        const scoreToSubmit = score || data?.correction?.score;
        const payload = {
          correction: correctionHtml || 'Review completed',
        };

        // Only add score if it exists and is valid
        if (scoreToSubmit && !isNaN(parseInt(scoreToSubmit))) {
          payload.score = parseInt(scoreToSubmit);
        }

        console.log('Creating new review with payload:', payload);

        const response = await api.post(
          `/tutor/novel/entries/${entryId}/review`,
          payload
        );

        if (response.success) {
          router.push('/dashboard/submission-management/hec-novel');
        } else {
          throw new Error(response.message);
        }
      }
    } catch (err) {
      console.error('Submit error:', err);
      toast.error(err.message || 'Failed to submit review');
    } finally {
      setIsSubmittingReview(false);
    }
  };

  const completeReview = async () => {
    try {
      setIsCompletingReview(true);
      const response = await api.post(
        `/tutor/novel/entries/${entryId}/confirm`
      );
      if (response.success) {
        router.push('/dashboard/submission-management/hec-novel');
      } else {
        throw new Error(response.message);
      }
    } catch (err) {
      toast.error(err.message || 'Failed to complete review');
    } finally {
      setIsCompletingReview(false);
    }
  };

  return (
    <>
      <div className="p-2 shadow-xl h-full bg-white">
        <div className="mb-4 rounded-md border shadow-lg p-4 ">
          <div className="">
            <div className="flex justify-between items-center border-b-2 border-dashed border-gray-300 mb-3">
              <h3 className="text-lg font-semibold mb-2">Original Content</h3>
              <div className="flex items-center gap-3 text-sm">
                Date: {formatDate(data.submittedAt, 'ordinal')}
              </div>
            </div>
            <div
              dangerouslySetInnerHTML={{
                __html: data?.originalReviewedVersion?.content || data.content,
              }}
              className="text-sm h-[204px] overflow-y-auto"
            ></div>
          </div>
          <span className="flex justify-end">
            <button
              className="bg-[#FFF9E6] border border-[#D4A574] text-[#8B4513] hover:bg-[#FFF5D6] hover:border-[#C19A5B] text-xs font-medium px-4 py-1 rounded-full transition-colors duration-200 shadow"
              onClick={() => setShowModal(true)}
              aria-label="View History"
            >
              View History
            </button>
          </span>
        </div>

        <div className="shadow-lg p-4 rounded-md border">
          <p className="text-sm text-[#864D0D] text-center font-medium">
            Tutor Review Zone
          </p>
          <div className="flex justify-between items-center border-b-2 border-dashed border-gray-300 mb-3">
            <h3 className="text-lg font-semibold mb-2">Make Corrections</h3>
            <div className="flex items-center gap-3 text-sm">
              Date: {formatDate(data.submittedAt, 'ordinal')}
            </div>
          </div>

          <div className="mb-4">
            {data?.status === 'reviewed' || data?.isResubmission ? (
              <div
                dangerouslySetInnerHTML={{
                  __html: data?.correction?.correction || '',
                }}
                className="h-[200px] overflow-y-auto"
              ></div>
            ) : (
              <SimpleTiptapEditor
                editorRef={editorRef}
                initialValue={correctionHtml}
                setValue={setCorrectionHtml}
                height={200}
              />
            )}
          </div>

          <div className="flex flex-wrap gap-3 sm:justify-between">
            {/* Left section: Feedback & Score */}
            <div className="flex flex-wrap gap-3">
              <button
                type="button"
                onClick={() => setIsFeedbackModalOpen(true)}
                className="px-3 py-1 bg-[#FEFCE8] text-xs sm:text-sm text-[#723F11] rounded-md border border-[#723F11] font-medium hover:bg-[#FFF8D6] min-w-[110px]"
              >
                Give feedback
              </button>

              <div className="flex items-center border border-[#723F11] rounded-md overflow-hidden text-xs sm:text-sm">
                <label className="px-3 py-1 bg-[#FEFCE8] text-[#723F11] font-medium whitespace-nowrap">
                  Score
                </label>
                {isCorrectionReviewed ? (
                  <div className="px-3 py-1 text-gray-700 bg-white">
                    {data.correction.score}
                  </div>
                ) : hasExistingScore ? (
                  <div className="px-3 py-1 text-gray-700 bg-gray-100">
                    {data.correction.score}
                  </div>
                ) : (
                  <input
                    type="number"
                    value={score}
                    onChange={(e) => setScore(e.target.value)}
                    className="w-16 px-2 py-1 focus:outline-none text-gray-700 text-center"
                    min="0"
                    max="100"
                    placeholder="---"
                  />
                )}
              </div>
            </div>

            {/* Right section: Confirm & Submit */}
            <div className="flex flex-wrap gap-3">
              {(data?.status === 'submitted' && !data?.isResubmission) && (
                <button
                  type="button"
                  onClick={submitReview}
                  disabled={!score || isSubmittingReview || !correctionHtml}
                  className="px-4 py-1 text-xs sm:text-sm bg-yellow-500 text-white rounded-md hover:bg-yellow-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
                >
                  {isSubmittingReview ? 'Submitting...' : 'Submit'}
                </button>
              )}
            </div>
          </div>
        </div>
      </div>

      {isFeedbackModalOpen && entryId && (
        <FeedbackModal
          isOpen={isFeedbackModalOpen}
          onClose={() => setIsFeedbackModalOpen(false)}
          studentsSubmission={data?.content}
          getEndpoint={`/tutor/novel/entries/${entryId}/feedbacks`}
          onSubmit={async (feedback) => {
            const response = await api.post(
              `/tutor/novel/entries/${entryId}/feedback`,
              { feedback }
            );
            if (!response.success) {
              throw new Error(response.message || 'Failed to submit feedback');
            }
          }}
          title="Teachers Feedback"
          placeholder="Write here"
          submitButtonText="Submit"
          submitButtonColor="bg-yellow-500 hover:bg-yellow-600"
        />
      )}

      {showModal && entryId && (
        <HistoryModal
          isOpen={showModal}
          onClose={() => setShowModal(false)}
          endPoint={`/tutor/novel/entries/${entryId}/history`}
        />
      )}
    </>
  );
};

export default NovelReviewSection;
