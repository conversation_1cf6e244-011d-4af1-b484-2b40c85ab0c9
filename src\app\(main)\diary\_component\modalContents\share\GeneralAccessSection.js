import React from 'react';
import Image from 'next/image';
import { Icon } from '@iconify/react';

const GeneralAccessSection = ({ shareLink, qrCodeUrl, onDownloadQR }) => {
  return (
    <div className="mb-6 pt-6">
      <h3 className="text-2xl font-semibold text-gray-700 mb-4">
        General access
      </h3>
      <div className="flex items-center">
        <div className="p-1.5 bg-yellow-300 rounded-full flex items-center justify-center text-gray-800 mr-3">
          <Icon icon="tabler:world-check" width="24" height="24" />
        </div>
        <input
          type="text"
          className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-yellow-500 focus:border-yellow-500"
          value={shareLink}
          readOnly
          placeholder="Generate a link to share"
        />
      </div>

      {/* QR Code section */}
      {qrCodeUrl && (
        <div className="mt-4">
          <div className="flex justify-center">
            <div className="relative w-40 h-40 border border-gray-300 rounded-md overflow-hidden">
              <Image
                src={qrCodeUrl}
                alt="QR Code for sharing"
                fill
                className="object-contain"
              />
            </div>
          </div>
          <div className="mt-3 flex justify-center">
            <button
              className="flex items-center gap-1 px-4 py-2 bg-yellow-500 text-white rounded-md hover:bg-yellow-600 transition-colors"
              onClick={onDownloadQR}
            >
              <Icon icon="material-symbols:download" width="20" height="20" />
              Download QR
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default GeneralAccessSection;
