'use client';

import NewTablePage from "@/components/form/NewTablePage";
import NovelViewModal from "./view/page"; // Adjust path as needed

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import api from '@/lib/api';

const NovelTopicList = () => {
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const [novelTopics, setNovelTopics] = useState([]);
  const [totalItems, setTotalItems] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [isError, setIsError] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  
  // Modal states
  const [showModal, setShowModal] = useState(false);
  const [selectedNovel, setSelectedNovel] = useState(null);
  const [modalLoading, setModalLoading] = useState(false);
  const [modalError, setModalError] = useState('');
  
  const router = useRouter();

  // Fetch novel topics
  const fetchNovelTopics = async () => {
    try {
      setIsLoading(true);
      const endpoint = `/admin/novel/suggestions?page=${currentPage}&limit=${rowsPerPage}`;
      
      console.log(`Fetching from: ${endpoint}`);
      
      const response = await api.get(endpoint);
      console.log('Raw API response:', response);
      
      // Improved data extraction to handle different response structures
      if (response?.data) {
        let items = [];
        let totalCount = 0;
        let totalPagesCount = 0;
        
        // Check different possible data structures
        if (response.data.items && Array.isArray(response.data.items)) {
          // Direct items array at top level
          items = response.data.items;
          totalCount = response.data.totalCount || response.data.totalItems || 0;
          totalPagesCount = response.data.totalPages || 0;
        } else if (response.data.data) {
          // Items in nested data property
          if (response.data.data.items && Array.isArray(response.data.data.items)) {
            items = response.data.data.items;
            totalCount = response.data.data.totalCount || response.data.data.totalItems || 0;
            totalPagesCount = response.data.data.totalPages || 0;
          } else if (Array.isArray(response.data.data)) {
            // Direct array in data property
            items = response.data.data;
            totalCount = items.length;
            totalPagesCount = 1;
          }
        } else if (Array.isArray(response.data)) {
          // Response data is directly an array
          items = response.data;
          totalCount = items.length;
          totalPagesCount = 1;
        }
        
        if (items.length > 0) {
          const formattedTopics = items.map(item => ({
            id: item.id,
            userName: item.studentName || item.userName || `${item.firstName || ''} ${item.lastName || ''}`.trim(),
            topicTitle: item.title || item.topic || item.suggestion,
            status: item.status,
            createdAt: item.createdAt,
            updatedAt: item.updatedAt,
            description: item.description,
            studentId: item.studentId
          }));
          
          setNovelTopics(formattedTopics);
          setTotalItems(totalCount);
          setTotalPages(totalPagesCount);
          setIsError(false);
          setErrorMessage('');
        } else {
          console.log('No items found in response');
          setNovelTopics([]);
          setTotalItems(0);
          setTotalPages(0);
          setIsError(false);
          setErrorMessage('');
        }
      } else {
        console.error('Unexpected data structure:', response);
        setIsError(true);
        setErrorMessage('Unexpected data structure received from API');
        setNovelTopics([]);
      }
    } catch (err) {
      console.error(`Error fetching novel topics:`, err);
      setIsError(true);
      setErrorMessage(err.message || `An error occurred while fetching novel topics`);
      setNovelTopics([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Get novel details from existing data (no API call needed)
  const getNovelDetails = (novelId) => {
    const novel = novelTopics.find(topic => topic.id === novelId);
    if (novel) {
      setSelectedNovel(novel);
      setModalError('');
    } else {
      setModalError('Novel suggestion not found');
    }
    setModalLoading(false);
  };

  useEffect(() => {
    fetchNovelTopics();
  }, [currentPage, rowsPerPage]);

  // View novel topic details - Open modal
  const handleViewDetails = (row) => {
    console.log('View details for:', row);
    setShowModal(true);
    setModalLoading(true);
    // Use existing data instead of API call
    setTimeout(() => getNovelDetails(row.id), 100); // Small delay for better UX
  };

  // Close modal
  const handleCloseModal = () => {
    setShowModal(false);
    setSelectedNovel(null);
    setModalError('');
  };

  // Handle refresh after status update
  const handleRefreshList = () => {
    fetchNovelTopics();
  };

 

  // Define columns for the novel topics table
  const novelTopicColumns = [
    {
      label: 'NOVEL PROJECT DESCRIPTION',
      field: 'description',
    },
    {
      label: 'CREATED BY',
      field: 'userName',
    },
    
  ];

  // Define actions
  const novelTopicActions = [
    {
      name: 'view',
      icon: 'heroicons-outline:eye',
      className: 'text-blue-600',
      onClick: handleViewDetails,
    },
  ];

  // Handle page change
  const handleChangePage = (page) => {
    setCurrentPage(page);
  };

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-6">HEC Novel Topic List</h1>
      
      {/* Content Area - Novel Topics */}
      <div className="bg-white rounded-lg shadow">
        <NewTablePage
          title=""
          createButton={false}
          columns={novelTopicColumns}
          data={novelTopics}
          actions={novelTopicActions}
          currentPage={currentPage}
          changePage={handleChangePage}
          totalItems={totalItems}
          rowsPerPage={rowsPerPage}
          setRowsPerPage={setRowsPerPage}
          totalPages={totalPages}
          loading={isLoading}
          error={isError}
          errorMessage={errorMessage}
          showCheckboxes={false}
          showSearch={false}
          showNameFilter={false}
          showSortFilter={false}
          showCreateButton={false}
          hideTitle={true}
        />
      </div>

      {/* Novel View Modal */}
      <NovelViewModal
        showModal={showModal}
        selectedNovel={selectedNovel}
        modalLoading={modalLoading}
        modalError={modalError}
        onClose={handleCloseModal}
        onRefreshList={handleRefreshList}
      />
    </div>
  );
};

export default NovelTopicList;