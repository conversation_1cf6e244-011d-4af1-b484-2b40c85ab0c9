'use client';
import { useRef, useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { useParams } from 'next/navigation';
import html2canvas from 'html2canvas';
import Canvas from '@/components/skin/Canvas';
import {
  setPreviewMode,
  setInitialTemplate
} from '@/store/features/canvasSlice';
import api from '@/lib/api';
import { toast } from 'sonner';

export default function EditSkin({editId}) {
  const { id } = useParams();
  const canvasRef = useRef(null);
  const dispatch = useDispatch();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchSkinData = async () => {
      setIsLoading(true);
      try {
        const response = await api.get(`/diary/skins/${id || editId}`);
        if (response.success && response.data) {
          const skinData = response.data;

          if (window.updateSkinFormValues) {
            window.updateSkinFormValues({
              name: skinData.name,
              description: skinData.description,
              isActive: skinData.isActive
            });
          }

          if (skinData.templateContent) {
            try {
              const template = JSON.parse(skinData.templateContent);
              dispatch(setInitialTemplate({
                items: template.items || [],
                background: template.background,
                width: template.width || 800,
                height: template.height || 600
              }));
            } catch (parseError) {
              console.error('Error parsing template content:', parseError);
              setError('Failed to load skin template. Invalid format.');
            }
          }
        } else {
          setError('Failed to load skin data. Please try again.');
        }
      } catch (fetchError) {
        console.error('Error fetching skin data:', fetchError);
        setError(fetchError.message || 'Failed to load skin data. Please try again.');
        toast.error('Failed to load skin data');
      } finally {
        setIsLoading(false);
      }
    };

    fetchSkinData();
  }, [id, dispatch]);

  useEffect(() => {
    dispatch(setPreviewMode(false));
  }, [dispatch]);

  const captureCanvas = async () => {
    if (!canvasRef.current?.canvasRef?.current) return null;
    const canvasElement = canvasRef.current.canvasRef.current;

    try {
      const selectedElements = canvasElement.querySelectorAll('.selected');
      const originalBorders = [];

      selectedElements.forEach(el => {
        originalBorders.push(el.style.border);
        el.style.border = 'none';
      });

      const canvas = await html2canvas(canvasElement, {
        backgroundColor: null,
        scale: 1,
        useCORS: true,
        allowTaint: true,
        logging: false
      });

      selectedElements.forEach((el, index) => {
        el.style.border = originalBorders[index];
      });

      return new Promise(resolve => {
        canvas.toBlob(blob => {
          if (!blob) {
            console.error('Failed to create blob from canvas');
            resolve(null);
            return;
          }
          const file = new File([blob], 'skin-preview.png', { type: 'image/png' });
          resolve(file);
        }, 'image/png');
      });
    } catch (error) {
      console.error('Error capturing canvas:', error);
      return null;
    }
  };

  useEffect(() => {
    window.captureCanvasImage = captureCanvas;
    return () => {
      delete window.captureCanvasImage;
    };
  }, []);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#8B4513]"></div>
        <p className="ml-3 text-[#8B4513] font-medium">Loading skin data...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-full">
        <div className="text-red-500 text-5xl mb-4">⚠️</div>
        <h3 className="text-xl font-bold text-red-600 mb-2">Error</h3>
        <p className="text-gray-700 mb-4">{error}</p>
      </div>
    );
  }

  return (
    <>
      <Canvas ref={canvasRef} />
    </>
  );
}
