'use client';

import Modal from '@/components/Modal';
import CreateSkin from '@/components/SkinManagement/add/CreateSkin';
import CreateSkinLayout from '@/components/SkinManagement/add/CreateSkinLayout';
import { setOpenCreateSkinModal } from '@/store/features/commonSlice';
import React, { use } from 'react';
import { useDispatch, useSelector } from 'react-redux';

const CreateSkinModal = () => {
  const dispatch = useDispatch();
  const { openCreateSkinModal } = useSelector((state) => state.common);
  // console.log(openCreateSkinModal)

  const onClose = () => {
    dispatch(setOpenCreateSkinModal(false));
  };
  return (
    <div>
      <Modal
        isOpen={openCreateSkinModal}
        onClose={onClose}
        title="Create Skin"
        width="7xl"
      >
        <CreateSkinLayout children={<CreateSkin />} />
      </Modal>
    </div>
  );
};

export default CreateSkinModal;
