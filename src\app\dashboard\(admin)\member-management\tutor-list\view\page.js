'use client';
import React, { useState } from 'react';
import { X, User, Mail, Phone, Calendar, MapPin, Users, Maximize2, Minimize2, GraduationCap, BookOpen } from 'lucide-react';

const TutorViewModal = ({ isOpen, onClose, tutorData }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  
  if (!isOpen || !tutorData) return null;

  const formatDate = (dateString) => {
    if (!dateString) return 'Not provided';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const calculateAge = (birthDate) => {
    if (!birthDate) return null;
    const today = new Date();
    const birth = new Date(birthDate);
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--;
    }
    
    return age;
  };

  const getAgeDisplay = () => {
    // If age is provided, use it
    if (tutorData.age) {
      return tutorData.dateOfBirth 
        ? `${tutorData.age} (Born: ${formatDate(tutorData.dateOfBirth)})`
        : tutorData.age;
    }
    
    // If age is not provided but birthdate is, calculate age
    if (tutorData.dateOfBirth) {
      const calculatedAge = calculateAge(tutorData.dateOfBirth);
      return `${calculatedAge} (Born: ${formatDate(tutorData.dateOfBirth)})`;
    }
    
    return 'Not provided';
  };

  const getGenderDisplay = (gender) => {
    if (!gender) return 'Not specified';
    return gender.charAt(0).toUpperCase() + gender.slice(1);
  };

  const modalSize = isExpanded 
    ? "max-w-6xl w-full max-h-[95vh]" 
    : "max-w-2xl w-full max-h-[80vh]";

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className={`bg-white rounded-lg shadow-xl ${modalSize} flex flex-col`}>
        {/* Header with more bottom space */}
        <div className="flex items-center justify-between p-6 pb-8 border-b border-gray-200 flex-shrink-0">
          <div className="flex items-center space-x-3 mt-6">
            <div className="flex items-center justify-center h-12 w-12 rounded-full bg-amber-100 text-amber-800">
              <GraduationCap className="h-8 w-8" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900 ml-5">{tutorData?.name || 'Tutor'}</h2>
              <p className="text-sm text-gray-500 ml-5">User ID: {tutorData?.userId}</p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="p-2 hover:bg-gray-100 rounded-full transition-colors"
              title={isExpanded ? "Minimize" : "Expand"}
            >
              {isExpanded ? (
                <Minimize2 className="h-5 w-5 text-gray-500" />
              ) : (
                <Maximize2 className="h-5 w-5 text-gray-500" />
              )}
            </button>
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-full transition-colors"
            >
              <X className="h-5 w-5 text-gray-500" />
            </button>
          </div>
        </div>

        {/* Scrollable Content */}
        <div className="flex-1 overflow-y-auto p-6">
          {/* Personal and Contact Details - Side by Side */}
          <div className="flex flex-col lg:flex-row gap-8">
            {/* Personal Details */}
            <div className="flex-1">
              <h3 className="text-lg font-medium text-[#723F11] mb-4">Personal Details</h3>
              <div className="space-y-4">
                {/* Biography */}
                <div>
                  <label className="block text-sm font-medium text-black mb-1">Biography</label>
                  <div className="p-3 bg-gray-50 rounded-md text-sm text-gray-600">
                    {tutorData?.bio || 'No biography provided'}
                  </div>
                </div>

                {/* Age */}
                <div>
                  <label className="block text-sm font-medium text-black mb-1">Age</label>
                  <div className="flex items-center space-x-2">
                    <Calendar className="h-4 w-4 text-gray-400" />
                    <span className="text-sm text-gray-900">
                      {getAgeDisplay()}
                    </span>
                  </div>
                </div>

                {/* Gender */}
                <div>
                  <label className="block text-sm font-medium text-black mb-1">Gender</label>
                  <div className="flex items-center space-x-2">
                    <User className="h-4 w-4 text-gray-400" />
                    <span className="text-sm text-gray-900">{getGenderDisplay(tutorData?.gender)}</span>
                  </div>
                </div>

               
              </div>
            </div>

            {/* Dashed line separator */}
            <div className="hidden lg:block w-px border-l-2 border-dashed border-gray-300 mx-4"></div>

            {/* Contact Details */}
            <div className="flex-1">
              <h3 className="text-lg font-medium text-[#723F11] mb-4">Contact Details</h3>
              <div className="space-y-4">
                {/* Address */}
                <div>
                  <label className="block text-sm font-medium text-black mb-1">Address</label>
                  <div className="flex items-start space-x-2">
                    <MapPin className="h-4 w-4 text-gray-400 mt-0.5" />
                    <div className="text-sm text-gray-900">
                      {(() => {
                        const addressParts = [
                          tutorData?.address,
                          tutorData?.city,
                          tutorData?.state,
                          tutorData?.country,
                          tutorData?.postalCode
                        ].filter(Boolean);
                        
                        return addressParts.length > 0 
                          ? addressParts.join(', ')
                          : 'No address provided';
                      })()}
                    </div>
                  </div>
                </div>

                {/* Phone Number */}
                <div>
                  <label className="block text-sm font-medium text-black mb-1">Phone Number</label>
                  <div className="flex items-center space-x-2">
                    <Phone className="h-4 w-4 text-gray-400" />
                    <span className="text-sm text-gray-900">
                      {tutorData?.phoneNumber || 'Not provided'}
                    </span>
                  </div>
                </div>

                {/* Email */}
                <div>
                  <label className="block text-sm font-medium text-black mb-1">Email</label>
                  <div className="flex items-center space-x-2">
                    <Mail className="h-4 w-4 text-gray-400" />
                    <span className="text-sm text-gray-900">{tutorData?.email}</span>
                  </div>
                </div>

                {/* User ID */}
                <div>
                  <label className="block text-sm font-medium text-black mb-1">User ID</label>
                  <div className="flex items-center space-x-2">
                    <Users className="h-4 w-4 text-gray-400" />
                    <span className="text-sm text-gray-900">{tutorData?.userId}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Dotted line separator */}
          <div className="my-8">
            <div className="border-t-2 border-dotted border-gray-300"></div>
          </div>

          {/* Teaching Information */}
          <div>
            <h3 className="text-lg font-medium text-[#723F11] mb-4">Teaching Information</h3>
            <div className="grid gap-4 grid-cols-1 sm:grid-cols-2">
              {/* Assigned Students */}
              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="text-sm font-medium text-blue-800 flex items-center">
                  <Users className="h-4 w-4 mr-2" />
                  Assigned Students
                </div>
                <div className="text-sm text-blue-600 mt-1">
                  {tutorData?.assignedStudentCount || 0} students
                </div>
              </div>

              {/* Assigned Modules */}
              <div className="bg-green-50 p-4 rounded-lg">
                <div className="text-sm font-medium text-green-800 flex items-center">
                  <BookOpen className="h-4 w-4 mr-2" />
                  Assigned Modules
                </div>
                <div className="text-sm text-green-600 mt-1">
                  {tutorData?.assignedModuleCount || 0} modules
                </div>
              </div>
            </div>

            {/* Education */}
            {tutorData?.education && tutorData.education.length > 0 && (
              <div className="mt-6">
                <h4 className="text-md font-medium text-gray-900 mb-3">Education</h4>
                <div className="space-y-3">
                  {tutorData.education.map((edu, index) => (
                    <div key={index} className="bg-gray-50 p-3 rounded-md">
                      <div className="text-sm font-medium text-gray-900">{edu.degree || 'Degree'}</div>
                      <div className="text-sm text-gray-600">{edu.institution || 'Institution'}</div>
                      {edu.year && <div className="text-xs text-gray-500">{edu.year}</div>}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Dotted line separator */}
          <div className="my-8">
            <div className="border-t-2 border-dotted border-gray-300"></div>
          </div>

          {/* Account Status */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">Account Status</h3>
            <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 md:grid-cols-3">
              <div className="bg-green-50 p-4 rounded-lg">
                <div className="text-sm font-medium text-green-800">Status</div>
                <div className="text-sm text-green-600">
                  {tutorData?.isActive ? 'Active' : 'Inactive'}
                </div>
              </div>
              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="text-sm font-medium text-blue-800">Confirmed</div>
                <div className="text-sm text-blue-600">
                  {tutorData?.isConfirmed ? 'Yes' : 'No'}
                </div>
              </div>
              <div className="bg-purple-50 p-4 rounded-lg">
                <div className="text-sm font-medium text-purple-800">Role</div>
                <div className="text-sm text-purple-600">
                  {tutorData?.selectedRole || 'Tutor'}
                </div>
              </div>
            </div>
          </div>

          {/* Dotted line separator */}
          <div className="my-8">
            <div className="border-t-2 border-dotted border-gray-300"></div>
          </div>

          {/* Timestamps */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">Activity Information</h3>
            <div className="grid gap-4 text-sm grid-cols-1">
              <div>
                <span className="font-medium text-gray-700">Created At:</span>
                <span className="ml-2 text-gray-600">{formatDate(tutorData?.createdAt)}</span>
              </div>
              <div>
                <span className="font-medium text-gray-700">Last Updated:</span>
                <span className="ml-2 text-gray-600">{formatDate(tutorData?.updatedAt)}</span>
              </div>
              {tutorData?.lastLoginAt && (
                <div>
                  <span className="font-medium text-gray-700">Last Login:</span>
                  <span className="ml-2 text-gray-600">{formatDate(tutorData.lastLoginAt)}</span>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-50 flex-shrink-0">
          <button
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default TutorViewModal;