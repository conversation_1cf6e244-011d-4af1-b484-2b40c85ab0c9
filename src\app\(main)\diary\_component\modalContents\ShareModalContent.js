import React, { useState } from 'react';
import { useSelector } from 'react-redux';
import RecentlyInvitedPeople from './share/RecentlyInvitedPeople';
import GeneralAccessSection from './share/GeneralAccessSection';
import ShareActions from './share/ShareActions';
import useShareLink from './share/hooks/useShareLink';

const ShareModalContent = ({ entryId }) => {
  const [phoneNumber, setPhoneNumber] = useState('');

  // Get the diary entry ID from props or from Redux store if not provided
  const todayEntry = useSelector((state) => state.diary?.todayEntry);
  const diaryEntryId = entryId || todayEntry?.id;

  // Use custom hook for share link functionality
  const {
    shareLink,
    qrCodeUrl,
    isGeneratingLink,
    isCopied,
    handleGenerateLink,
    handleCopyLink,
    handleDownloadQR,
  } = useShareLink(diaryEntryId);



  return (
    <div className="px-2">
      {/* Input for adding people */}
      {/* <div className="mb-6">
        <input
          type="text"
          className="w-full px-3 py-3 border border-gray-500 rounded-md focus:outline-none focus:ring-yellow-500 focus:border-yellow-500"
          placeholder="Add people via phone number"
          value={phoneNumber}
          onChange={(e) => setPhoneNumber(e.target.value)}
        />
      </div> */}

      {/* Recently invited people section */}
      <RecentlyInvitedPeople entryId={diaryEntryId} />

      {/* General access section */}
      <GeneralAccessSection
        shareLink={shareLink}
        qrCodeUrl={qrCodeUrl}
        onDownloadQR={handleDownloadQR}
      />

      {/* Share actions */}
      <ShareActions
        isCopied={isCopied}
        isGeneratingLink={isGeneratingLink}
        onCopyLink={handleCopyLink}
        onGenerateLink={handleGenerateLink}
      />
    </div>
  );
};

export default ShareModalContent;
