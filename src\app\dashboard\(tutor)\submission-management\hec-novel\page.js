'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import NovelSubmissionList from './_components/NovelSubmissionList';
import HecNovelLayout from './review/_components/HecNovelLayout';

const HecNovel = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const tabParam = searchParams.get('tab');

  // Set default active tab or use the one from URL
  const [activeTab, setActiveTab] = useState(tabParam || 'NovelSubmissionList');

  // Update state when URL changes
  useEffect(() => {
    if (tabParam) {
      setActiveTab(tabParam);
    }
  }, [tabParam]);

  // Function to render the correct content based on active tab
  const renderContent = () => {
    switch(activeTab) {
      case 'NovelSubmissionList':
        return <NovelSubmissionList />;
      default:
        return <NovelSubmissionList />;
    }
  };

  return (
    <HecNovelLayout activeTab={activeTab}>
      {renderContent()}
    </HecNovelLayout>
  );
};

export default HecNovel;