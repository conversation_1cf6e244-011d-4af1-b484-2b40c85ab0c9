import { createSlice } from '@reduxjs/toolkit';
import { format } from 'date-fns';

export const backgroundColors = [
  { name: 'Light Cream', value: '#FFFDF5' },
  { name: 'Light Blue', value: '#F0F7FF' },
  { name: 'Light Pink', value: '#FFF0F7' },
  { name: 'Light Green', value: '#F0FFF4' },
  { name: 'Light Yellow', value: '#FFFBEB' },
  { name: 'Light Gray', value: '#F9FAFB' },
  { name: '<PERSON>', value: '#FFFFF0' },
  { name: 'Lavender', value: '#E6E6FA' },
  { name: 'Mint', value: '#F5FFFA' },
  { name: 'Peach', value: '#FFDAB9' },
  { name: 'Be<PERSON>', value: '#F5F5DC' },
  { name: 'Sky Blue', value: '#87CEEB' },
  { name: 'Coral', value: '#FF7F50' },
  { name: 'Khaki', value: '#F0E68C' },
  { name: 'Aquamarine', value: '#7FFFD4' },
  { name: 'Thistle', value: '#D8BFD8' },
  { name: 'Navy Blue', value: '#000080' },
  { name: '<PERSON> Green', value: '#228B22' },
  { name: 'Maroon', value: '#800000' },
  { name: 'Purple', value: '#800080' },
  { name: 'Teal', value: '#008080' },
  { name: 'Chocolate', value: '#D2691E' },
];

export const fontFamilies = [
  { name: 'Roboto', value: 'Roboto' },
  { name: 'Poppins', value: 'Poppins' },
  { name: 'Anek Bangla', value: 'Anek Bangla' },
  { name: 'Sans', value: 'sans-serif' },
];

export const fontSizes = [
  { name: 'Small', value: '0.875rem' },
  { name: 'Medium', value: '1rem' },
  { name: 'Large', value: '1.125rem' },
  { name: 'X-Large', value: '1.25rem' },
  { name: '2X-Large', value: '1.5rem' },
];

export const colors = [
  { name: 'Black', value: '#000000' },
  { name: 'Gray', value: '#4B5563' },
  { name: 'Blue', value: '#2563EB' },
  { name: 'Red', value: '#DC2626' },
  { name: 'Green', value: '#059669' },
  { name: 'Yellow', value: '#D97706' },
  { name: 'Purple', value: '#7C3AED' },
  { name: 'Pink', value: '#DB2777' },
  { name: 'Indigo', value: '#4F46E5' },
  { name: 'Teal', value: '#0D9488' },
  { name: 'Orange', value: '#EA580C' },
];

export const dateFormats = [
  { name: 'DD/MM/YYYY', value: 'dd/MM/yyyy' },
  { name: 'MM/DD/YYYY', value: 'MM/dd/yyyy' },
  { name: 'DD MMM YYYY', value: 'dd MMM yyyy' },
  { name: 'MMMM DD, YYYY', value: 'MMMM dd, yyyy' },
  { name: 'YYYY-MM-DD', value: 'yyyy-MM-dd' },
];

export const textAlignments = [
  { name: 'Left', value: 'left', icon: 'mdi:format-align-left' },
  { name: 'Center', value: 'center', icon: 'mdi:format-align-center' },
  { name: 'Right', value: 'right', icon: 'mdi:format-align-right' },
  { name: 'Justify', value: 'justify', icon: 'mdi:format-align-justify' },
];

export const DEFAULT_BACKGROUND = '#FFFDF5';

const defaultCanvasItems = [
  {
    id: 'subject',
    type: 'text',
    textType: 'subject',
    zIndex: 5,
    content: 'My Diary Subject',
    styles: {
      fontFamily: 'Roboto',
      color: '#000000',
      fontSize: '1.25rem',
      width: 300,
      height: 40,
      x: 50,
      y: 20,
      textAlign: 'left',
    },
  },
  {
    id: 'date',
    type: 'text',
    textType: 'date',
    zIndex: 5,
    content: format(new Date(), 'dd MMM yyyy'),
    dateFormat: 'dd MMM yyyy',
    styles: {
      fontFamily: 'Roboto',
      color: '#4B5563',
      fontSize: '0.875rem',
      width: 200,
      height: 30,
      x: 200,
      y: 20,
      textAlign: 'right',
    },
  },
  {
    id: 'body',
    type: 'text',
    textType: 'body',
    zIndex: 5,
    content:
      'Write your diary entry here. This is the body text of your diary where you can describe your day, thoughts, and experiences.',
    styles: {
      fontFamily: 'Roboto',
      color: '#000000',
      fontSize: '1rem',
      width: 400,
      height: 60,
      x: 50,
      y: 70,
      textAlign: 'left',
    },
  },
];

const initialState = {
  canvasItems: defaultCanvasItems,
  canvasBackground: '#ddd',
  selectedId: null,
  previewMode: false,
  canvasWidth: 627,
  canvasHeight: 357,
};

export const canvasSlice = createSlice({
  name: 'canvas',
  initialState,
  reducers: {
    setPreviewMode: (state, action) => {
      state.previewMode = action.payload;
      if (action.payload) state.selectedId = null;
    },
    setSelectedId: (state, action) => {
      state.selectedId = action.payload;
    },
    clearSelection: (state) => {
      state.selectedId = null;
    },
    addImageToCanvas: (state, action) => {
      if (typeof action.payload === 'string') {
        const imageSrc = action.payload;
        const newId = `image-${Date.now()}`;
        const newItem = {
          id: newId,
          type: 'image',
          styles: {
            x: 100,
            y: 100,
            width: 150,
            height: 150,
          },
          image: imageSrc,
          zIndex: 1,
        };
        state.canvasItems.push(newItem);
        // No automatic selection
      } else {
        const { imageSrc, id, styles, zIndex } = action.payload;
        const newItem = {
          id: id || `image-${Date.now()}`,
          type: 'image',
          styles: styles || {
            x: 100,
            y: 100,
            width: 150,
            height: 150,
          },
          image: imageSrc,
          zIndex: zIndex || 1,
        };
        state.canvasItems.push(newItem);
        // No automatic selection
      }
    },
    updateCanvasItem: (state, action) => {
      const { id, updates } = action.payload;
      const itemIndex = state.canvasItems.findIndex((item) => item.id === id);
      if (itemIndex !== -1) {
        if (updates.styles) {
          state.canvasItems[itemIndex] = {
            ...state.canvasItems[itemIndex],
            ...updates,
            styles: {
              ...state.canvasItems[itemIndex].styles,
              ...updates.styles,
            },
          };
        } else {
          state.canvasItems[itemIndex] = {
            ...state.canvasItems[itemIndex],
            ...updates,
          };
        }
      }
    },
    changeBackground: (state, action) => {
      state.canvasBackground = action.payload;
    },
    deleteCanvasItem: (state, action) => {
      const idToDelete = action.payload;
      state.canvasItems = state.canvasItems.filter((item) => item.id !== idToDelete);
      if (state.selectedId === idToDelete) state.selectedId = null;
    },
    resetCanvas: (state) => {
     // Completely reset to initial state
     state.canvasItems = [];  // Clear all items first
     state.canvasBackground = DEFAULT_BACKGROUND;  // Reset to default background
     state.selectedId = null;  // Clear selection

     // Any other state that needs to be reset
     // This ensures a complete reset of the canvas
   },
   addTextItem: (state, action) => {
     // Add a text item directly to the canvas
     const textItem = action.payload;
     state.canvasItems.push(textItem);
   },
    setInitialTemplate: (state, action) => {
      const { items, background, width, height } = action.payload || {};
      state.canvasItems = Array.isArray(items) ? items : [];
      state.canvasBackground = background || DEFAULT_BACKGROUND;
      state.canvasWidth = width || 800;
      state.canvasHeight = height || 600;
      state.selectedId = null;
    }
  },
});

export const {
  setPreviewMode,
  setSelectedId,
  clearSelection,
  addImageToCanvas,
  addTextItem,
  updateCanvasItem,
  changeBackground,
  deleteCanvasItem,
  resetCanvas,
  setInitialTemplate,
} = canvasSlice.actions;

export const selectCanvasItems = (state) => state.canvas.canvasItems;
export const selectCanvasBackground = (state) => state.canvas.canvasBackground;
export const selectSelectedId = (state) => state.canvas.selectedId;
export const selectPreviewMode = (state) => state.canvas.previewMode;
export const selectCanvasWidth = (state) => state.canvas.canvasWidth;
export const selectCanvasHeight = (state) => state.canvas.canvasHeight;
export const selectSelectedItem = (state) => {
  const selectedId = state.canvas.selectedId;
  return selectedId ? state.canvas.canvasItems.find((item) => item.id === selectedId) : null;
};

export default canvasSlice.reducer;
