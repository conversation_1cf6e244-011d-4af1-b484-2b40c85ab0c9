'use client';

import React, { useRef, useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  selectMessageInput,
  selectAttachedFiles,
  selectIsSendingMessage,
  setMessageInput,
  addAttachedFile,
  removeAttachedFile,
} from '@/store/features/chatSlice';

const MessageInput = ({ onSendMessage, onFileSelect, onTyping }) => {
  const dispatch = useDispatch();
  const messageInput = useSelector(selectMessageInput);
  const attachedFiles = useSelector(selectAttachedFiles);
  const isSending = useSelector(selectIsSendingMessage);
  const fileInputRef = useRef(null);
  const typingTimeoutRef = useRef(null);

  // Local state to store actual File objects (not in Redux)
  const [fileObjects, setFileObjects] = useState(new Map());

  // Clean up file objects when attachedFiles is cleared
  useEffect(() => {
    if (attachedFiles.length === 0) {
      setFileObjects(new Map());
    }
  }, [attachedFiles.length]);

  const MAX_FILES = 5;
  const ALLOWED_TYPES = [
    'image/jpeg',
    'image/png',
    'image/webp',
    'image/gif',
    'application/pdf',
    'text/plain',
    'application/zip',
    'application/x-rar-compressed',
    'video/mp4',
    'audio/mpeg'
  ];

  const handleInputChange = (e) => {
    const value = e.target.value;
    dispatch(setMessageInput(value));

    // Handle typing indicator
    if (onTyping) {
      onTyping(true);
      clearTimeout(typingTimeoutRef.current);
      typingTimeoutRef.current = setTimeout(() => {
        onTyping(false);
      }, 650);
    } else {
      console.log('📝 onTyping callback not provided');
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  const handleSend = () => {
    if ((!messageInput.trim() && attachedFiles.length === 0) || isSending) {
      return;
    }

    if (onSendMessage) {
      // Pass files with their actual File objects
      const filesWithObjects = attachedFiles.map(fileData => ({
        ...fileData,
        file: fileObjects.get(fileData.id)
      })).filter(f => f.file); // Only include files that have actual File objects

      onSendMessage(filesWithObjects);
    }
  };

  // Function to get files with their objects for external use
  const getFilesWithObjects = () => {
    return attachedFiles.map(fileData => ({
      ...fileData,
      file: fileObjects.get(fileData.id)
    })).filter(f => f.file);
  };

  const validateFile = (file) => {
    if (!ALLOWED_TYPES.includes(file.type)) {
      return 'File type not allowed';
    }
    if (file.size > 10 * 1024 * 1024) { // 10MB
      return 'File size too large (max 10MB)';
    }
    return null;
  };

  const createFilePreview = (file) => {
    return new Promise((resolve) => {
      if (file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = (e) => resolve(e.target.result);
        reader.readAsDataURL(file);
      } else {
        resolve(null);
      }
    });
  };

  const handleFileSelect = async (e) => {
    const files = Array.from(e.target.files);
    const remainingSlots = MAX_FILES - attachedFiles.length;
    const filesToProcess = files.slice(0, remainingSlots);

    for (const file of filesToProcess) {
      const error = validateFile(file);
      if (error) {
        console.warn(`File ${file.name}: ${error}`);
        continue;
      }

      const preview = await createFilePreview(file);
      const fileId = `temp-${Date.now()}-${Math.random()}`;

      // Store only serializable data in Redux
      const fileData = {
        id: fileId,
        name: file.name,
        size: file.size,
        type: file.type,
        preview,
      };

      // Store actual File object separately in local state
      setFileObjects(prev => new Map(prev).set(fileId, file));

      dispatch(addAttachedFile(fileData));
    }

    // Reset file input
    e.target.value = '';

    if (onFileSelect) {
      // Pass file objects with their IDs for upload
      const filesWithIds = filesToProcess.map(file => {
        const fileId = `temp-${Date.now()}-${Math.random()}`;
        return { file, id: fileId };
      });
      onFileSelect(filesWithIds);
    }
  };

  const handleRemoveFile = (index) => {
    // Get the file ID before removing from Redux
    const fileToRemove = attachedFiles[index];
    if (fileToRemove) {
      // Remove from local file objects map
      setFileObjects(prev => {
        const newMap = new Map(prev);
        newMap.delete(fileToRemove.id);
        return newMap;
      });
    }

    dispatch(removeAttachedFile(index));
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div style={{ display: 'flex', flexDirection: 'column' }}>
      {/* File Attachments Preview */}
      {attachedFiles.length > 0 && (
        <div
          style={{
            padding: '12px 20px',
            display: 'flex',
            flexWrap: 'wrap',
            gap: '8px',
            background: '#fff',
            borderTop: '1px solid #e5e7eb',
          }}
        >
          {attachedFiles.map((fileData, index) => (
            <div
              key={fileData.id}
              style={{
                position: 'relative',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
              }}
            >
              {fileData.preview ? (
                <img
                  src={fileData.preview}
                  alt={fileData.name}
                  style={{
                    width: '60px',
                    height: '60px',
                    objectFit: 'cover',
                    borderRadius: '6px',
                    border: '1px solid #e5e7eb',
                  }}
                />
              ) : (
                <div
                  style={{
                    width: '60px',
                    height: '60px',
                    borderRadius: '6px',
                    background: '#f3f4f6',
                    border: '1px solid #e5e7eb',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontSize: '24px',
                  }}
                >
                  📄
                </div>
              )}

              <div
                style={{
                  fontSize: '10px',
                  color: '#6b7280',
                  marginTop: '2px',
                  textAlign: 'center',
                  maxWidth: '60px',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                }}
              >
                {fileData.name}
              </div>

              <div
                style={{
                  fontSize: '9px',
                  color: '#9ca3af',
                  textAlign: 'center',
                }}
              >
                {formatFileSize(fileData.size)}
              </div>

              <button
                onClick={() => handleRemoveFile(index)}
                style={{
                  position: 'absolute',
                  top: '-6px',
                  right: '-6px',
                  width: '18px',
                  height: '18px',
                  borderRadius: '50%',
                  background: '#ef4444',
                  color: '#fff',
                  border: 'none',
                  fontSize: '12px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  cursor: 'pointer',
                }}
              >
                ×
              </button>
            </div>
          ))}
        </div>
      )}

      {/* Input Area */}
      <div
        style={{
          height: '64px',
          display: 'flex',
          alignItems: 'center',
          padding: '0 20px',
          borderTop: '1px solid #e5e7eb',
          background: '#fff',
        }}
      >
        <input
          type="text"
          value={messageInput}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          placeholder="Type a message…"
          disabled={isSending}
          // Disable spell checking and grammar checking
          spellCheck="false"
          autoComplete="off"
          autoCorrect="off"
          autoCapitalize="off"
          data-gramm="false"
          data-gramm_editor="false"
          data-enable-grammarly="false"
          data-grammarly-disable="true"
          data-lt-disable="true"
          data-pwa-disable="true"
          data-ginger-disable="true"
          data-whitesmoke-disable="true"
          data-ms-editor="false"
          data-webkit-grammar-checking="false"
          data-moz-spellcheck="false"
          data-spell-check="false"
          data-grammar-check="false"
          translate="no"
          style={{
            flex: 1,
            border: '1px solid #d1d5db',
            borderRadius: '20px',
            padding: '10px 14px',
            outline: 'none',
            fontSize: '14px',
            opacity: isSending ? 0.6 : 1,
          }}
          className='focus:ring-1 ring-yellow-200'
        />

        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept={ALLOWED_TYPES.join(',')}
          onChange={handleFileSelect}
          style={{ display: 'none' }}
        />

        <button
          onClick={() => fileInputRef.current?.click()}
          disabled={isSending || attachedFiles.length >= MAX_FILES}
          style={{
            marginLeft: '12px',
            background: 'none',
            border: 'none',
            cursor: isSending || attachedFiles.length >= MAX_FILES ? 'not-allowed' : 'pointer',
            fontSize: '20px',
            opacity: isSending || attachedFiles.length >= MAX_FILES ? 0.4 : 1,
            padding: '4px',
          }}
          title={attachedFiles.length >= MAX_FILES ? `Maximum ${MAX_FILES} files allowed` : 'Attach file'}
        >
          📎
        </button>

        <button
          onClick={handleSend}
          disabled={(!messageInput.trim() && attachedFiles.length === 0) || isSending}
          style={{
            marginLeft: '12px',
            background: '#2563eb',
            color: '#fff',
            border: 'none',
            padding: '8px 18px',
            borderRadius: '20px',
            cursor: (!messageInput.trim() && attachedFiles.length === 0) || isSending ? 'not-allowed' : 'pointer',
            fontSize: '14px',
            opacity: (!messageInput.trim() && attachedFiles.length === 0) || isSending ? 0.6 : 1,
          }}
        >
          {isSending ? 'Sending...' : 'Send'}
        </button>
      </div>
    </div>
  );
};

export default MessageInput;
