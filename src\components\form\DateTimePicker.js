import React, { useState, useRef, useEffect } from 'react';
import { useField, useFormikContext } from 'formik';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { format } from 'date-fns';
import { Icon } from '@iconify/react';

export default function DateTimePicker({
  name,
  label,
  required,
  minuteDiffarent = 1,
  ...props
}) {
  const { setFieldValue } = useFormikContext();
  const [field, meta] = useField(name);
  const [open, setOpen] = useState(false);
  const wrapperRef = useRef(null);

  // Safely convert value to a valid Date object
  const safeDate =
    field.value && new Date(field.value).toString() !== 'Invalid Date'
      ? new Date(field.value)
      : null;

  useEffect(() => {
    function handleClickOutside(event) {
      if (wrapperRef.current && !wrapperRef.current.contains(event.target)) {
        setOpen(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleChange = (date) => {
    setFieldValue(name, date);
    setOpen(false);
  };

  return (
    <div className="relative" ref={wrapperRef}>
      <label
        htmlFor={name}
        className="block text-sm sm:text-base font-[500] text-gray-700 mb-1"
      >
        {label} {required && <span className="text-red-500">*</span>}
      </label>
      <button
        type="button"
        onClick={() => setOpen(!open)}
        className={`w-full px-4 py-2 border ${
          meta.touched && meta.error ? 'border-red-500' : 'border-gray-300'
        } rounded-md text-left bg-white shadow-sm text-gray-500 flex justify-between items-center`}
      >
        {safeDate
          ? format(safeDate, 'MMMM dd, yyyy hh:mm aa')
          : 'Select date and time'}
        <Icon
          icon="material-symbols:calendar-month-outline"
          className="inline-block ml-2"
        />
      </button>
      {open && (
        <div className="absolute z-50 mt-2 rounded-lg shadow-lg">
          <DatePicker
            selected={safeDate}
            onChange={handleChange}
            showTimeSelect
            timeFormat="hh:mm aa"
            timeIntervals={minuteDiffarent}
            dateFormat="MMMM dd, yyyy h:mm aa"
            inline
            calendarClassName="custom-calendar"
            dayClassName={(date) =>
              safeDate &&
              format(date, 'd') === format(safeDate, 'd')
                ? 'custom-day-selected'
                : 'custom-day'
            }
          />
        </div>
      )}
      {meta.touched && meta.error ? (
        <div className="text-red-500 text-xs mt-1">{meta.error}</div>
      ) : null}
    </div>
  );
}
