import React, { useState, useEffect } from 'react';
import { Icon } from '@iconify/react';
import api from '@/lib/api';
import Image from 'next/image';

const OwnedItemCart = ({ cartItems, cartRefetch }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [itemCount, setItemCount] = useState(0);

  useEffect(() => {
    // Set item count from cart data
    if (cartItems?.length > 0) {
      setItemCount(cartItems.length || 0);
    }
  }, [cartItems]);

  const handleDeleteFromCart = async (id) => {
    console.log(id);
    try {
      await api.delete(`/shop/cart/items/${id}`);
      cartRefetch();
    } catch (error) {
      console.log(error);
    }
  };

  const calculateTotal = () => {
    if (!cartItems || cartItems.length === 0) return 0;
    return cartItems.reduce(
      (total, item) => total + (item.price || 0) * item.quantity,
      0
    );
  };

  const handleCheckout = async () => {
    try {
      await api.post('/shop/cart/checkout', {
        paymentMethod: 'kcp_card',
        paymentDetails: {
          cardToken: 'tok_visa',
        },
        returnUrl: 'https://example.com/payment/success',
        cancelUrl: 'https://example.com/payment/cancel',
      });
      cartRefetch();
    } catch (error) {
      console.log(error);
    }
  };

  return (
    <div className="fixed right-10 top-1/2 transform -translate-y-1/2 z-30">
      {/* Cart Icon */}

      {/* Cart Dropdown */}
      {isOpen ? (
        <div className=" mt-2 w-72 bg-white rounded-lg shadow-xl overflow-hidden z-30">
          <div className="p-4 border-b flex items-center justify-between">
            <h3 className="font-bold text-lg">Selected Items ({itemCount})</h3>
            <Icon
              className="cursor-pointer"
              icon="tabler:x"
              onClick={() => setIsOpen(false)}
            />
          </div>

          {cartItems && cartItems.length > 0 ? (
            <>
              <div className="max-h-96 overflow-y-auto">
                {cartItems.map((item) => (
                  <div key={item.id} className="p-4 border-b flex">
                    <div className="flex-shrink-0 mr-3">
                      <Image
                        src={item.imageUrl || 'https://via.placeholder.com/50'}
                        alt={item.title}
                        className="h-12 w-12 object-cover rounded"
                        width={50}
                        height={50}
                        onError={(e) => {
                          e.target.onerror = null;
                          e.target.src = 'https://via.placeholder.com/50';
                        }}
                      />
                    </div>
                    <div className="flex-grow">
                      <h4 className="font-medium text-sm">{item.title}</h4>
                      <p className="text-xs text-gray-500">
                        {item.categoryName}
                      </p>
                      <div className="flex items-center mt-1">
                        {item.isOnSale && item.discountPercentage > 0 ? (
                          <>
                            <span className="text-sm font-bold">
                              ${item.price.toFixed(2)}
                            </span>
                            <span className="text-xs text-gray-500 line-through ml-2">
                              ${item.originalPrice.toFixed(2)}
                            </span>
                            <span className="text-xs text-green-600 ml-2">
                              ({item.discountPercentage}% off)
                            </span>
                          </>
                        ) : (
                          <span className="text-sm font-bold">
                            ${item.originalPrice.toFixed(2)}
                          </span>
                        )}
                        <span className="text-xs text-gray-500 ml-2">
                          x {item.quantity}
                        </span>
                      </div>
                    </div>
                    <button
                      onClick={() => handleDeleteFromCart(item.id)}
                      className="text-gray-400 hover:text-red-500 transition"
                      aria-label="Remove item"
                    >
                      <Icon icon="mdi:trash-can-outline" className="w-5 h-5" />
                    </button>
                  </div>
                ))}
              </div>

              <div className="p-4 border-t">
                <div className="flex justify-between font-bold mb-4">
                  <span>Total:</span>
                  <span>${calculateTotal().toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-sm text-gray-600 mb-4">
                  <span>Reward Points:</span>
                  <span>{cartItems.totalRewardPoints || 0}</span>
                </div>
                <button
                  onClick={handleCheckout}
                  className="w-full bg-yellow-500 hover:bg-yellow-600 text-white py-2 px-4 rounded transition flex items-center justify-center"
                >
                  Proceed to Checkout
                  <Icon icon="mdi:arrow-right" className="ml-2" />
                </button>
              </div>
            </>
          ) : (
            <div className="p-4 text-center text-gray-500">
              <Icon
                icon="mdi:cart-remove"
                className="w-10 h-10 mx-auto text-gray-300 mb-2"
              />
              No items in cart
            </div>
          )}
        </div>
      ) : (
        <div
          className="relative bg-white p-3 rounded-full shadow-lg cursor-pointer hover:bg-gray-100 transition"
          onClick={() => setIsOpen(!isOpen)}
        >
          <Icon icon="mdi:cart-outline" className="w-6 h-6 text-gray-700" />
          {itemCount > 0 && (
            <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center">
              {itemCount}
            </span>
          )}
        </div>
      )}
    </div>
  );
};

export default OwnedItemCart;
