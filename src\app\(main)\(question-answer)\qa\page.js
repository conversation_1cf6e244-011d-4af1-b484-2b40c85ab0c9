'use client';
import Button, { ButtonIcon } from '@/components/Button';
import <PERSON><PERSON>iewer from '@/components/EditorViewer';
import SimpleTiptapEditor from '@/components/form/SimpleTiptapEditor';
import GoBack from '@/components/shared/GoBack';
import useDataFetch from '@/hooks/useDataFetch';
import api from '@/lib/api';
import { Form, Formik } from 'formik';
import Image from 'next/image';
import { useRouter, useSearchParams } from 'next/navigation';
import React, { useEffect, useRef, useState } from 'react';
import { useSelector } from 'react-redux';
import { toast } from 'sonner';
import EssayFeedBackModal from '../../essay/_components/FeedbackModal';
import { Icon } from '@iconify/react';

const HecLatestAnswer = () => {
  const router = useRouter();
  const editorRef = useRef(null);
  const [value, setValue] = useState('');
  const [wordCount, setWordCount] = useState(0);
  const { user } = useSelector((state) => state.auth);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [autoSaveTimeout, setAutoSaveTimeout] = useState(null);
  const [showPreviewMode, setShowPreviewMode] = useState(false);
  const [showEmptyDataMessage, setShowEmptyDataMessage] = useState(false);
  const [hasRefetchedAfterFirstSave, setHasRefetchedAfterFirstSave] =
    useState(false);

  const { data, isLoading, refetch } = useDataFetch({
    queryKey: ['hec-answer'],
    endPoint: `/qa/latest-assignments/${user?.id}`,
  });

  // console.log('data:', data);

  // const showSubmission = (data?.submissions?.length > 0 && !isSubmitted) || showPreviewMode;
  const showFeedback =
    data?.submissions?.length > 0 &&
    data?.submissions[data?.submissions?.length - 1]?.status === 'reviewed';

  // Word limit variables based on API response structure
  const minimumWords = data?.assignments?.[0]?.question?.minimumWords || 0;

  // Count words in HTML content
  const countWords = (html) => {
    if (!html) return 0;
    // Remove HTML tags
    const text = html?.replace(/<[^>]*>/g, ' ');
    // Remove entities
    const cleanText = text.replace(/&nbsp;|&amp;|&lt;|&gt;|&quot;|&#39;/g, ' ');
    // Remove extra spaces and split by whitespace
    const words = cleanText
      .trim()
      .split(/\s+/)
      .filter((word) => word.length > 0);
    return words.length;
  };

  const handleSubmit = async (values) => {
    try {
      console.log('values:', values);
      // Check minimum word count
      const wordCount = countWords(values?.answer?.answer || values?.answer);

      if (wordCount < minimumWords) {
        toast.message(
          `Your answer must contain at least ${minimumWords} words. Current word count: ${wordCount}`
        );
        return;
      }

      const response = await api.put(
        `/qa/submissions/${data?.submissions[0]?.id}/submit`,
        {
          answer: values.answer?.answer || values.answer,
          setSequence: data?.setSequence,
          wordCount: wordCount,
        }
      );
      console.log(response);
      refetch();
      setIsSubmitted(true);
      setShowPreviewMode(true);
      router.push('/qa');
    } catch (error) {
      console.log(error);
    }
  };

  const handleUpdate = async (values) => {
    try {
      // Ensure we have the answer value
      if (!values || !values.answer) return;

      if (autoSaveTimeout) {
        clearTimeout(autoSaveTimeout);
      }

      const timeout = setTimeout(async () => {
        try {
          const response = await api.post(
            `/qa/submissions`,
            {
              answer: values.answer?.answer || values.answer,
              setSequence: data?.setSequence,
            },
            { showSuccessToast: false }
          );

          // Refetch data only once after first draft save to get the submission ID
          if (response?.data && !hasRefetchedAfterFirstSave) {
            setHasRefetchedAfterFirstSave(true);
            refetch();
          }
        } catch (error) {
          console.error('Auto-save error:', error);
        }
      }, 300);
      setAutoSaveTimeout(timeout);

      // Only refetch and update UI state on explicit submission, not auto-save
      if (!values._autoSave) {
        refetch();
        setIsSubmitted(false);
      }
    } catch (error) {
      console.error('Error updating submission:', error);
    }
  };

  useEffect(() => {
    if (data?.submissions?.length > 0) {
      const totalWord = countWords(
        data?.submissions[data?.submissions?.length - 1]?.answer
      );
      setWordCount(totalWord);
    }
  }, [data]);

  // Initialize preview mode based on existing submissions
  useEffect(() => {
    if (
      data?.submissions?.length > 0 &&
      !isSubmitted &&
      !hasRefetchedAfterFirstSave
    ) {
      setShowPreviewMode(true);
    }
  }, [data?.submissions, isSubmitted]);

  // Handle component unmount - save draft
  useEffect(() => {
    return () => {
      setTimeout(() => {
        if (value && !isSubmitted && data?.id) {
          handleUpdate({
            answer: value,
            _autoSave: true,
          });
        }
      }, 300);
    };
  }, [value, data?.id]);

  useEffect(() => {
    if (data && data?.assignments?.length > 0) {
      setShowEmptyDataMessage(false);
    } else {
      setShowEmptyDataMessage(true);
    }
  }, [data]);

  

  if (isLoading) {
    return (
      <div className="max-w-7xl mx-auto p-5 xl:px-0 flex justify-center items-center min-h-[60vh]">
        <div className="flex flex-col items-center">
          <Icon
            icon="eos-icons:loading"
            className="text-yellow-500 text-5xl animate-spin"
          />
        </div>
      </div>
    );
  }

  return (
    <div className="relative">
      <div className="max-w-7xl mx-auto px-5 xl:px-0 relative z-10">
        <GoBack title={'HEC Q & A'} linkClass="my-5 mb-8 w-full max-w-40" />

        {showEmptyDataMessage ? (
          <div className="max-w-3xl mx-auto mb-10">
            <div className="bg-white rounded-xl shadow-md border border-gray-100 overflow-hidden p-6">
              {/* Header with icon */}
              <div className="flex flex-col items-center text-center">
                <div className="mb-4 p-4 bg-yellow-50 rounded-full">
                  <Icon
                    icon="streamline-ultimate-color:paper-write"
                    className="w-10 h-10 text-yellow-500"
                  />
                </div>
                <h3 className="text-2xl font-semibold text-gray-800 mb-3">
                  No assignment has been assigned.
                </h3>
                <p className="text-gray-600 max-w-md">
                  Your tutor will assign tasks as part of your learning
                  activities. Once assigned, you'll be able to view instructions
                  and submit your work here.
                </p>
              </div>

              {/* Action button */}
              <div className="mt-8 flex justify-center">
                <button
                  onClick={() => router.back()}
                  className="px-6 py-2.5 bg-yellow-500 hover:bg-yellow-600 text-white font-medium rounded-lg transition-all duration-200 shadow-sm hover:shadow-md focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2"
                >
                  Go Back
                </button>
              </div>
            </div>
          </div>
        ) : (
          <div className="p-5 rounded-lg bg-[#FFF9FB] shadow-lg space-y-5 mb-10">
            <div className="p-5 bg-[#EDFDFD] w-full rounded-lg shadow-lg space-y-5 relative">
              <div className="p-5 bg-[#FCF8EF] rounded-lg [box-shadow:2px_2px_12px_0px_#F5D1B066_inset,_-2px_-2px_12px_0px_#F5D1B066_inset]  flex items-start justify-between max-sm:flex-col max-sm:flex-col-reverse">
                <div className="space-y-3">
                  <div className="text-xl text-yellow-800 font-semibold">
                    Questions:
                    {data?.assignments?.map((item, index) => (
                      <div key={index}>
                        <h1 className="text-xl pl-4 text-yellow-800">
                          <span className="text-lg">{index + 1})</span>{' '}
                          {item?.question?.question}
                        </h1>
                      </div>
                    ))}
                  </div>
                  <div>
                    <p className="text-xl text-yellow-800 font-semibold">
                      Instruction:
                    </p>
                    <EditorViewer
                      data={data?.instructions || data?.description}
                    />
                  </div>
                </div>

                <div className="relative">
                  <Image
                    src={'/assets/images/all-img/boardFrame.png'}
                    alt={'block-play'}
                    width={500}
                    height={500}
                    className="max-w-52 max-sm:max-w-32 max-sm:max-h-20 max-h-32"
                  />

                  <ul className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 max-sm:left-2/3 max-sm:text-xs text-sm min-w-32">
                    <li>Total Score: {data?.assignmentScore || 0}</li>
                    {data?.submissions[0]?.score && (
                      <li>Gained Score: {data?.submissions[0]?.score}</li>
                    )}
                  </ul>
                </div>
              </div>
            </div>

            {showPreviewMode ? (
              <div className="space-y-3">
                <div className="p-5 shadow-lg rounded-lg border relative bg-white">
                  <h1 className="text-xl text-yellow-800 font-semibold">
                    My Answer
                  </h1>
                  <EditorViewer
                    data={
                      (
                        data?.submissions
                          ?.map((item) => item?.answer)
                          .join(' ') || ''
                      ).length > 200
                        ? data?.submissions
                            ?.map((item) => item?.answer)
                            .join(' ')
                            .slice(0, 400) + '...'
                        : data?.submissions
                            ?.map((item) => item?.answer)
                            .join(' ')
                    }
                  />

                  {!(data?.submissions[0]?.status === 'submitted') && (
                    <div className="absolute right-2 top-2">
                      <ButtonIcon
                        icon={'ri:edit-2-fill'}
                        innerBtnCls={'h-10 w-10'}
                        btnIconCls={'h-5 w-5'}
                        onClick={() => {
                          setIsSubmitted(true);
                          setShowPreviewMode(false);
                        }}
                      />
                    </div>
                  )}
                </div>

                {showFeedback && (
                  <div className="p-5 shadow-lg rounded-lg border relative bg-white">
                    <h1 className="text-xl text-yellow-800 font-semibold">
                      Tutor Correction Zone
                    </h1>

                    <>
                      {data?.submissions[data?.submissions.length - 1]
                        ?.corrections?.length > 0 && (
                        <div className="rounded-md">
                          <ul className=" text-gray-800">
                            <EditorViewer
                              data={
                                data?.submissions[data?.submissions.length - 1]
                                  ?.corrections
                              }
                            />
                          </ul>
                        </div>
                      )}

                      <div className="absolute right-2 top-2">
                        <ButtonIcon
                          icon={'arcticons:feedback-2'}
                          innerBtnCls={'h-10 w-10'}
                          btnIconCls={'h-4 w-4'}
                          onClick={() => setShowDetailsModal(true)}
                        />
                      </div>
                    </>

                    <p
                      className={`${
                        !data?.submissions[data?.submissions.length - 1]
                          ?.corrections && 'text-red-600'
                      } text-center mt-2`}
                    >
                      {!data?.submissions[data?.submissions.length - 1]
                        ?.corrections && 'Not Reviewed yet'}
                    </p>
                  </div>
                )}
              </div>
            ) : (
              <Formik
                initialValues={{
                  assignmentId: data?.id,
                  answer:
                    value ||
                    data?.submissions?.map((item) => item?.answer).join(' ') ||
                    '',
                }}
                onSubmit={handleSubmit}
                enableReinitialize
              >
                {() => (
                  <Form>
                    <SimpleTiptapEditor
                      name="answer"
                      editorRef={editorRef}
                      initialValue={
                        data?.submissions
                          ?.map((item) => item?.answer)
                          .join(' ') || ''
                      }
                      onAutoSave={(answer) => {
                        setValue(answer);
                        const newWordCount = countWords(
                          answer?.answer || answer
                        );
                        setWordCount(newWordCount);
                        // Set new timeout for auto-save
                        setTimeout(() => {
                          handleUpdate({
                            answer: answer,
                            _autoSave: true,
                          });
                        }, 300); // 0.3 second delay after typing stops
                      }}
                      setValue={setValue}
                      minWords={minimumWords}
                    />

                    <div className="text-right text-sm space-y-1 flex items-center justify-between">
                      <p className="text-gray-600">
                        Current words:{' '}
                        <span
                          className={`font-medium ${
                            wordCount < minimumWords
                              ? 'text-red-500'
                              : 'text-green-600'
                          }`}
                        >
                          {wordCount}
                        </span>
                      </p>
                      {wordCount < minimumWords && (
                        <p className="text-red-500">
                          Minimum {minimumWords} words required to submit.
                        </p>
                      )}
                    </div>

                    <div className="flex justify-center mt-14 gap-3">
                      <Button
                        buttonText="Cancel"
                        type="button"
                        onClick={() => (
                          setIsSubmitted(false), setShowPreviewMode(true)
                        )}
                      />
                      <Button
                        buttonText={'Submit'}
                        type="submit"
                        disabled={wordCount < minimumWords}
                      />
                    </div>
                  </Form>
                )}
              </Formik>
            )}
          </div>
        )}
      </div>

      <EssayFeedBackModal
        isOpen={showDetailsModal}
        onClose={() => setShowDetailsModal(false)}
        data={data?.submissions[data?.submissions?.length - 1]?.feedback}
        title="Teacher's Feedback"
      />
    </div>
  );
};

export default HecLatestAnswer;
