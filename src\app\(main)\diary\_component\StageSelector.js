'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import useDataFetch from '@/hooks/useDataFetch';
import { Icon } from '@iconify/react';

const StageSelector = ({ onStageChange, selectedTemplateId, disabled = false }) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef(null);

  const { data, isLoading, error } = useDataFetch({
    queryKey: ['diary-settings'],
    endPoint: '/diary/settings',
  });

  // Define handleStageSelect first to avoid the "Cannot access before initialization" error
  const handleStageSelect = useCallback((stage) => {
    setIsOpen(false);
    if (onStageChange) onStageChange(stage);
  }, [onStageChange]);

  // Derive selected stage from props and data
  const selectedStage = selectedTemplateId
    ? data?.items?.find(item => item.id === selectedTemplateId)
    : data?.items?.[0];

  // When data loads or changes, ensure we have a selected stage
  useEffect(() => {
    if (data?.items?.length && !selectedTemplateId) {
      // If no stage is selected yet, select the first one
      handleStageSelect(data.items[0]);
    }
  }, [data, selectedTemplateId, handleStageSelect]);

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event) => {
      // Check if the dropdown ref exists and if the click is outside
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    // Only add listener when dropdown is open
    if (isOpen) {
      // Add a small delay to prevent immediate closure when opening
      const timeoutId = setTimeout(() => {
        document.addEventListener('mousedown', handleClickOutside, true);
      }, 100);

      return () => {
        clearTimeout(timeoutId);
        document.removeEventListener('mousedown', handleClickOutside, true);
      };
    }
  }, [isOpen]);

  if (isLoading) return <div className="animate-pulse bg-gray-200 h-10 rounded-md w-32"></div>;
  if (error) return <div className="text-red-500 text-sm">Failed to load stages</div>;
  if (!data?.items?.length) return <div className="text-gray-500 text-sm">No stages available</div>;

  return (
    <div className="flex items-center gap-2">
      <div className="relative" ref={dropdownRef}>
        <button
          onClick={(e) => {
            e.stopPropagation();
            if (!disabled) {
              setIsOpen(!isOpen);
            }
          }}
          disabled={disabled}
          className={`flex items-center gap-2 max-sm:py-1 max-sm:px-2 px-3 py-2 rounded-md ${
            disabled
              ? 'bg-gray-400 text-gray-600 cursor-not-allowed'
              : 'bg-yellow-500 hover:bg-yellow-600 text-white cursor-pointer'
          }`}
        >
          <span className='max-sm:text-xs'>Stage {selectedStage?.level || 1}</span>
          <Icon icon={disabled ? "mdi:lock" : (isOpen ? "mdi:chevron-up" : "mdi:chevron-down")} />
        </button>

        {isOpen && !disabled && (
          <div className="absolute top-full left-0 mt-1 bg-white shadow-lg rounded-md z-10 w-64 max-sm:max-h-64 overflow-y-auto">
            {data.items.map((stage) => (
              <div
                key={stage.id}
                className={`p-3 hover:bg-yellow-50 cursor-pointer ${
                  selectedStage?.id === stage.id ? 'bg-yellow-100' : ''
                }`}
                onClick={(e) => {
                  e.stopPropagation();
                  handleStageSelect(stage);
                }}
              >
                <div className="font-medium max-sm:text-xs">Stage {stage.level}: {stage.title}</div>
                <div className="text-sm text-gray-600">{stage.description}</div>
              </div>
            ))}
          </div>
        )}
      </div>

      <div className={`px-3 py-2 max-sm:py-1 max-sm:px-2 max-sm:text-xs rounded-md ${
        disabled
          ? 'bg-gray-100 text-gray-600'
          : 'bg-yellow-100 text-yellow-800'
      }`}>
        {selectedStage?.wordLimit || 50} Words
      </div>
    </div>
  );
};

export default StageSelector;