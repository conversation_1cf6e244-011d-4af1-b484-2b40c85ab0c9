'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import { toast } from 'sonner';
import api from '@/lib/api';
import useDataFetch from '@/hooks/useDataFetch';
import HecSubmissionLayout from '@/components/layouts/HecSubmissionLayout';
import { Icon } from '@iconify/react';
import { formatDate } from '@/utils/dateFormatter';
import FeedbackModal from '../../../../hec-diary/review/_components/FeedbackModal';
import EditorViewer from '@/components/EditorViewer';
import { ButtonIcon } from '@/components/Button';
import SkinPreview from '@/components/skin/SkinPreview';
import dynamic from 'next/dynamic';
import EssayFeedbackModal from '../../_components/EssayFeedbackModal';
import SimpleTiptapEditor from '@/components/form/SimpleTiptapEditor';

const MissionEssayReviewPage = () => {
  const { id } = useParams();
  const router = useRouter();
  const [correctionText, setCorrection] = useState('');
  const [score, setScore] = useState('');
  const [taskRemarks, setTaskRemarks] = useState('');
  const [submissionFeedback, setSubmissionFeedback] = useState('');
  const searchParams = useSearchParams();
  const tabParam = searchParams.get('tab');
  const [activeTab, setActiveTab] = useState(tabParam || 'missionEssay');
  const [isFeedbackModalOpen, setIsFeedbackModalOpen] = useState(false);
  const editorRef = useRef(null);
  const [isPostMethod, setIsPostMethod] = useState(true);
  const [isSubmittingReview, setIsSubmittingReview] = useState(false);

  useEffect(() => {
    if (tabParam) {
      setActiveTab(tabParam);
    }
  }, [tabParam]);

  // Define tabs for the layout
  const tabs = [
    { name: 'Essay Submissions', value: 'essaySubmissions' },
    { name: 'Mission Essay', value: 'missionEssay' },
  ];

  // Fetch mission essay entry data
  const { data, isLoading, error, refetch } = useDataFetch({
    queryKey: ['mission-essay-entry-review', id],
    endPoint: `/tutor-essay/${id}`,
    enabled: !!id,
  });

  // Initialize correction text with original content when data is loaded
  useEffect(() => {
    if (data?.content) {
      setCorrection(data.content);
    }

    // Initialize feedback fields from API response
    if (data?.submissionHistory?.length > 0) {
      const latestSubmission = data.submissionHistory[0];
      setTaskRemarks(latestSubmission?.content || '');
      if (latestSubmission?.submissionMark) {
        setIsPostMethod(false);
        setScore(latestSubmission.submissionMark.points || '');
        setSubmissionFeedback(
          latestSubmission.submissionMark.submissionFeedback || ''
        );
        setTaskRemarks(latestSubmission?.submissionMark?.taskRemarks || '');
      }
    }
  }, [data]);

  // Submit review function
  const submitReview = async () => {
    if (!score) {
      toast.error('Please provide a score');
      return;
    }

    if (!correctionText.trim()) {
      toast.error('Please provide corrections if needed.');
      return;
    }

    try {
      setIsSubmittingReview(true);
      const response = await api[isPostMethod ? 'post' : 'patch'](
        `/tutor-essay/markEssay`,
        {
          submissionId: data?.id,
          taskRemarks: correctionText,
          points: parseInt(score),
        }
      );

      if (response.success) {
        router.push(
          '/dashboard/submission-management/hec-essay?tab=missionEssay'
        );
      }
    } catch (err) {
      console.log(err?.message || 'Failed to submit review');
    } finally {
      setIsSubmittingReview(false);
    }
  };

  if (isLoading) {
    return (
      <HecSubmissionLayout
        activeTab={activeTab}
        tabs={tabs}
        title="HEC Essay"
        basePath="/dashboard/submission-management/hec-essay"
      >
        <div className="flex items-center justify-center h-96">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-yellow-500"></div>
        </div>
      </HecSubmissionLayout>
    );
  }

  if (error) {
    return (
      <HecSubmissionLayout
        activeTab={activeTab}
        tabs={tabs}
        title="HEC Essay"
        basePath="/dashboard/submission-management/hec-essay"
      >
        <div className="bg-red-100 text-red-700 p-4 rounded-md">
          Error loading mission essay entry: {error.message}
        </div>
      </HecSubmissionLayout>
    );
  }

  return (
    <HecSubmissionLayout
      activeTab={activeTab}
      tabs={tabs}
      title="HEC Essay"
      basePath="/dashboard/submission-management/hec-essay"
    >
      <div className="grid items-center bg-[#FFF9FB] gap-2 p-4 shadow-xl border rounded-lg space-y-3">
        {/* Diary Canvas */}
        <div className="p-5 bg-[#FCF8EF] rounded-lg [box-shadow:2px_2px_12px_0px_#F5D1B066_inset,_-2px_-2px_12px_0px_#F5D1B066_inset]  flex items-center justify-between">
          <div>
            <h1 className="text-2xl text-yellow-800 font-semibold">
              {data?.task?.title}
            </h1>
            <p>Instructions:</p>
            <EditorViewer data={data?.task?.instructions} />
          </div>

          <h2 className="text-3xl font-semibold text-yellow-600 font-serif">
            Mission Diary
          </h2>
        </div>

        {/* Original Content */}
        <div>
          <div className="flex justify-between items-center">
            <h3 className="text-xl text-yellow-800 font-semibold mb-2">
              Student Submission
            </h3>
            {/* <button
              className="bg-[#FFF9E6] border border-[#D4A574] text-[#8B4513] hover:bg-[#FFF5D6] hover:border-[#C19A5B] text-xs font-medium px-4 py-1 rounded-full transition-colors duration-200 shadow"
              onClick={() => setShowHistory(true)}
              aria-label="View History"
            >
              View History
            </button> */}
          </div>
          <div className="rounded-md shadow border p-4 min-h-32 max-h-72 overflow-y-auto bg-white">
            <EditorViewer
              data={data?.submissionHistory?.[0]?.content || ''}
              className="whitespace-pre-wrap text-sm text-[#314158]"
            />
          </div>
        </div>

        {/* Correction Section */}
        <div className="overflow-y-auto">
          <div className="flex justify-between items-center">
            <h3 className="text-xl text-yellow-800 font-semibold mb-2">
              Tutor Correction Zone
            </h3>
          </div>

          {data?.status === 'reviewed' || data?.status === 'resubmitted' ? (
            <div
              className="h-[200px] overflow-y-auto bg-white p-4 rounded-lg border shadow"
              dangerouslySetInnerHTML={{
                __html: taskRemarks || '',
              }}
            ></div>
          ) : (
            <SimpleTiptapEditor
              editorRef={editorRef}
              initialValue={
                taskRemarks || data?.submissionMark?.taskRemarks || ''
              }
              setValue={setCorrection}
              height={400}
            />
          )}

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-5 mt-4">
              <ButtonIcon
                icon={'arcticons:feedback-2'}
                innerBtnCls={'h-12 w-12'}
                btnIconCls={'h-5 w-5'}
                onClick={() => setIsFeedbackModalOpen(true)}
              />
              <div className="flex items-center">
                <label className="mr-2">Score:</label>
                {data?.status === 'reviewed' ||
                data?.status === 'resubmitted' ? (
                  <div className="text-gray-800">{score}</div>
                ) : (
                  <input
                    type="number"
                    value={score}
                    onChange={(e) => setScore(e.target.value)}
                    className="w-20 border border-gray-300 rounded px-2 py-1 text-sm"
                    min="0"
                    max="100"
                    placeholder="0-100"
                  />
                )}
              </div>
            </div>

            {!(
              data?.status === 'reviewed' || data?.status === 'resubmitted'
            ) && (
              <div className="flex items-center gap-4">
                <button
                  onClick={() => handleBack()}
                  className="px-4 py-2 bg-gray-400 text-white rounded-md"
                >
                  Cancel
                </button>
                {(data?.status !== 'reviewed' ||
                  data?.status !== 'resubmitted') && (
                  <button
                    onClick={submitReview}
                    disabled={isSubmittingReview || !score}
                    className="px-4 py-2 bg-yellow-500 text-white rounded-md hover:bg-yellow-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
                  >
                    {isSubmittingReview ? 'Submitting...' : 'Submit'}
                  </button>
                )}
              </div>
            )}
          </div>
        </div>
      </div>

      <EssayFeedbackModal
        isOpen={isFeedbackModalOpen}
        feedbacks={submissionFeedback || ''}
        onClose={() => setIsFeedbackModalOpen(false)}
        onSuccess={() => refetch}
        submissionId={data?.id}
        isPostMethod={isPostMethod}
      />
    </HecSubmissionLayout>
  );
};

export default MissionEssayReviewPage;
