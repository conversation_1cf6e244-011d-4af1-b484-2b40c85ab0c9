'use client';

import React, { useState, useEffect } from 'react';
import useDataFetch from '@/hooks/useDataFetch';
import { useRouter } from 'next/navigation';
import NewTablePage from '@/components/form/NewTablePage';
import { formatDate } from '@/utils/dateFormatter';

const EssayList = () => {
  const router = useRouter();

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalPages, setTotalPages] = useState(1);
  const [processedEssays, setProcessedEssays] = useState([]);

  const { data: essays, isLoading } = useDataFetch({
    queryKey: ['my-essays', currentPage, rowsPerPage],
    endPoint: `/student-essay/myEssays?page=${currentPage}&limit=${rowsPerPage}`,
  });

  const handleViewEssay = (essayId) => {
    // Navigate to essay details or edit page
    router.push(`/essay/list/${essayId}`);
  };

  // Process essays data when it changes
  useEffect(() => {
    if (essays?.items) {
      const essayItems = essays.items;
      const formattedEssays = essayItems.map((essay, index) => ({
        id: essay.id,
        submissionDate: formatDate(
          essay.submissionHistory?.[0]?.submissionDate ||
            essay.updatedAt ||
            essay.createdAt
        ),
        statusDisplay: essay.status || 'draft',
        title: essay.title || 'My HEC Essay Title',
      }));

      setProcessedEssays(formattedEssays);

      // Use pagination data from API response
      setTotalPages(essays.totalPages || 1);
    }
  }, [essays, currentPage, rowsPerPage]);


  // Define table columns
  const columns = [
    {
      field: 'title',
      label: 'My Essay Writing',
    },
    {
      field: 'submissionDate',
      label: 'Submitted Date',
      renderCellContent: (value) => value || 'Not submitted',
    },
  ];

  // Define table actions
  const actions = [
    {
      label: 'View',
      icon: 'mdi:eye',
      onClick: (row) => handleViewEssay(row.id),
      className: 'text-blue-600 hover:text-blue-800 cursor-pointer',
    },
  ];

  // Pagination handlers
  const handlePageChange = (newPage) => {
    setCurrentPage(newPage);
  };

  const handleRowsPerPageChange = (newRowsPerPage) => {
    setRowsPerPage(newRowsPerPage);
    setCurrentPage(1); // Reset to first page
  };

  return (
    <div className="max-w-7xl mx-auto p-5 bg-white my-10 rounded-lg">
      <NewTablePage
        title="My HEC Essay List"
        columns={columns}
        data={processedEssays}
        actions={actions}
        loading={isLoading}
        currentPage={currentPage}
        totalPages={totalPages}
        changePage={handlePageChange}
        totalItems={
          essays?.totalCount || essays?.totalItems || processedEssays?.length
        }
        rowsPerPage={rowsPerPage}
        setRowsPerPage={handleRowsPerPageChange}
        showCheckboxes={false}
        showSearch={false}
        showNameFilter={false}
        showSortFilter={false}
        showCreateButton={false}
      />
    </div>
  );
};

export default EssayList;
