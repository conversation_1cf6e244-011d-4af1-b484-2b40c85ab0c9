'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import api from '@/lib/api';
import { toast } from 'react-toastify';
import { useQuery } from '@tanstack/react-query';
import HecQALayout from '../_components/HecQALayout';
import EditorViewer from '@/components/EditorViewer';
import FeedbackModal from '../_components/FeedbackModal';
import { ButtonIcon } from '@/components/Button';
import SimpleTiptapEditor from '@/components/form/SimpleTiptapEditor';

const QASubmissionViewPage = () => {
  const params = useParams();
  const router = useRouter();
  const submissionId = params.id;

  const searchParams = useSearchParams();
  const tabParam = searchParams.get('tab');

  // Map tab parameters to the correct tab for this review page
  const getActiveTabForReview = (tabParam) => {
    // Both questionSubmission and missionQAList should show questionSubmission tab in review
    if (tabParam === 'missionQAList' || tabParam === 'questionSubmission') {
      return 'questionSubmission';
    }
    return 'questionSubmission'; // default
  };

  const [activeTab, setActiveTab] = useState(getActiveTabForReview(tabParam));

  useEffect(() => {
    if (tabParam) {
      setActiveTab(getActiveTabForReview(tabParam));
    }
  }, [tabParam]);
  // Editor ref for TinyMCE
  const editorRef = useRef(null);

  // Tutor marking states
  const [score, setScore] = useState('');
  const [isSubmittingReview, setIsSubmittingReview] = useState(false);
  const [correctionText, setCorrection] = useState('');
  const [isFeedbackModalOpen, setIsFeedbackModalOpen] = useState(false);

  // Fetch submission details
  const fetchSubmissionDetails = async () => {
    try {
      const response = await api.get(
        `/tutor/qa/assignment-submission/${submissionId}`
      );

      if (response?.success) {
        return response.data;
      } else {
        throw new Error(
          response?.message || 'Failed to fetch submission details'
        );
      }
    } catch (error) {
      console.error('Error fetching submission details:', error);
      toast.error(error.message || 'Error fetching submission details');
      throw error;
    }
  };

  // Use React Query for data fetching
  const {
    data: submissionData,
    isLoading,
    isError,
    error,
    refetch,
  } = useQuery({
    queryKey: ['qaSubmissionDetails', submissionId],
    queryFn: fetchSubmissionDetails,
    enabled: !!submissionId,
    retry: 1,
    onSuccess: (data) => {
      // Pre-populate score if it exists
      if (data?.assignmentSubmission?.score) {
        setScore(data.assignmentSubmission.score);
      }
    },
  });

  // Initialize correction text with existing corrections or student's answer
  useEffect(() => {
    if (submissionData?.assignmentSubmission?.corrections) {
      // Show the corrections content if it exists
      setCorrection(submissionData.assignmentSubmission.corrections);
    } else if (submissionData?.assignmentSubmission?.answer) {
      // Otherwise show the original student answer
      setCorrection(submissionData.assignmentSubmission.answer);
    }

    if (submissionData?.assignmentSubmission?.score) {
      setScore(submissionData.assignmentSubmission.score);
    }
  }, [submissionData]);

  // Handle back navigation
  const handleBack = () => {
    router.back();
  };

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // Handle marking submission
  const handleSubmitReview = async () => {
    // Check if score exists - if it does, don't require it in validation
    const scoreExists =
      submissionData?.assignmentSubmission?.score !== null &&
      submissionData?.assignmentSubmission?.score !== undefined;

    if (!correctionText.trim() || (!scoreExists && !score)) {
      toast.error(
        scoreExists
          ? 'Please provide correction'
          : 'Please provide both correction and score'
      );
      return;
    }

    setIsSubmittingReview(true);
    try {
      const actualSubmissionId =
        submissionData?.assignmentSubmission?.id || submissionId;

      // Format corrections as expected by the API
      const corrections = correctionText.trim();

      // Prepare the payload - only include score if it doesn't already exist
      const payload = {
        corrections,
      };

      // Only add score to payload if it doesn't already exist
      if (!scoreExists) {
        payload.score = parseInt(score);
      }

      console.log('Submitting review:', {
        submissionId: actualSubmissionId,
        payload,
        scoreExists,
      });

      // Use the PUT endpoint with the correct structure
      const response = await api.put(
        `/tutor/qa/submissions/${actualSubmissionId}/review`,
        payload
      );

      console.log('API Response:', response);

      if (response?.success) { 
        // Refresh the data after successful submission
        refetch();
      } else {
        throw new Error(response?.message || 'Failed to submit review');
      }
    } catch (error) {
      console.error('Error submitting review:', error);
      console.error('Full error response:', error.response?.data);
    } finally {
      setIsSubmittingReview(false);
    }
  };

  // Calculate actualSubmissionId for FeedbackModal
  const actualSubmissionId =
    submissionData?.assignmentSubmission?.id || submissionId;

  // Loading state
  if (isLoading) {
    return (
      <HecQALayout activeTab={activeTab}>
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading submission details...</p>
          </div>
        </div>
      </HecQALayout>
    );
  }

  // Error state
  if (isError) {
    return (
      <HecQALayout activeTab={activeTab}>
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <div className="text-red-600 text-xl mb-4">⚠️</div>
            <p className="text-gray-600 mb-4">
              {error?.message || 'Failed to load submission'}
            </p>
            <button
              onClick={handleBack}
              className="bg-orange-600 text-white px-4 py-2 rounded-md hover:bg-orange-700"
            >
              Go Back
            </button>
          </div>
        </div>
      </HecQALayout>
    );
  }

  const { assignmentSubmission, assignmentSet, question } =
    submissionData || {};

  return (
    <HecQALayout activeTab={activeTab}>
      <div className="flex justify-between items-center mb-2">
        <div className="flex gap-4 items-center">
          <h6 className="text-lg text-gray-700 font-medium">QA Submission</h6>
        </div>
      </div>

      <div className="grid items-center bg-[#FFF9FB] gap-2 p-4 shadow-xl border rounded-lg space-y-3">
        {/* Task Header with Instructions */}
        <div className="p-5 bg-[#FCF8EF] rounded-lg [box-shadow:2px_2px_12px_0px_#F5D1B066_inset,_-2px_-2px_12px_0px_#F5D1B066_inset] flex items-center justify-between">
          <div className="flex-1">
            <h1 className="text-2xl text-yellow-800 font-semibold mb-3">
              Assignment Set #{assignmentSet?.setSequence}
            </h1>
            <div className="mb-4">
              <p className="font-medium text-gray-700 mb-2">Instructions:</p>
              <EditorViewer data={assignmentSet?.instruction} />
            </div>
          </div>
          <h2 className="text-3xl font-semibold text-yellow-600 font-serif">
            Mission Q&A
            <div className="text-gray-800 font-semibold">
              Score : {assignmentSet.score}
            </div>
          </h2>
        </div>

        {/* Questions Section */}
        {question && question.length > 0 && (
          <div className="bg-white p-4 rounded-lg border shadow">
            <h3 className="text-xl text-yellow-800 font-semibold mb-3">
              Questions
            </h3>
            <div className="space-y-3">
              {question.map((questionSet, index) => (
                <div
                  key={questionSet.id}
                  className="border-l-4 border-yellow-400 pl-4"
                >
                  <div className="text-sm text-gray-600 mb-2">
                    Assigned: {formatDate(questionSet.assignedDate)}
                  </div>
                  {questionSet.question &&
                    questionSet.question.map((q, qIndex) => (
                      <div key={q.id} className="mb-2">
                        <p className="font-medium text-gray-800">
                          Q{index + 1}. {q.question}
                        </p>
                      </div>
                    ))}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Student Submission Section */}
        <div className="bg-white p-4 rounded-lg border shadow">
          <div className="flex justify-between items-center mb-3">
            <h3 className="text-xl text-yellow-800 font-semibold">
              Student Submission
            </h3>
          </div>
          <div className="rounded-md shadow border p-4 min-h-32 max-h-72 overflow-y-auto bg-gray-50">
            <div
              className="whitespace-pre-wrap text-sm text-[#314158]"
              dangerouslySetInnerHTML={{
                __html: assignmentSubmission?.answer || 'No answer provided',
              }}
            />
          </div>
        </div>

        {/* Tutor Correction Zone */}
        <div className="bg-white p-4 rounded-lg border shadow">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-xl text-yellow-800 font-semibold">
              Tutor Correction Zone
            </h3>
          </div>

          <SimpleTiptapEditor
            editorRef={editorRef}
            initialValue={
              submissionData?.assignmentSubmission?.corrections
                ? submissionData.assignmentSubmission.corrections
                : submissionData?.assignmentSubmission?.answer || ''
            }
            setValue={setCorrection}
            height={400}
          />

          <div className="flex items-center justify-between mt-2">
            <div className="flex items-center gap-5">
              <ButtonIcon
                icon={'arcticons:feedback-2'}
                innerBtnCls={'h-12 w-12'}
                btnIconCls={'h-5 w-5'}
                onClick={() => setIsFeedbackModalOpen(true)}
              />

              <div className="flex items-center">
                <label className="mr-2 font-medium">Score:</label>
                {assignmentSubmission?.score !== null &&
                assignmentSubmission?.score !== undefined ? (
                  // Score already exists - show as read-only
                  <div className="text-gray-800 font-semibold">
                    {assignmentSubmission.score}
                    {/* / {assignmentSet.score || 10} */}
                  </div>
                ) : (
                  // No score exists - show as editable
                  <>
                    <input
                      type="number"
                      value={score}
                      onChange={(e) => {
                        const newScore = Number(e.target.value);
                        const maxScore = assignmentSet.score || 10;
                        if (newScore < 0 || newScore > maxScore) {
                          toast.error(`Score must be between 0 to ${maxScore}`);
                          return;
                        }
                        setScore(e.target.value);
                      }}
                      className="w-24 border border-gray-300 rounded px-2 py-1 text-center"
                      min="0"
                      max={assignmentSet.score || 10}
                      placeholder={`0-${assignmentSet.score || 10}`}
                    />
                    <span className="ml-1 text-gray-600">
                      / {assignmentSet.score || 10}
                    </span>
                  </>
                )}
              </div>
            </div>

            {!(assignmentSubmission?.status === 'reviewed') && <div className="flex items-center gap-4">
              <button
                onClick={handleBack}
                className="px-4 py-2 bg-gray-400 text-white rounded-md hover:bg-gray-500"
              >
                Cancel
              </button>
              <button
                onClick={handleSubmitReview}
                disabled={
                  !correctionText.trim() ||
                  ((assignmentSubmission?.score === null ||
                    assignmentSubmission?.score === undefined) &&
                    !score) ||
                  isSubmittingReview
                }
                className="px-4 py-2 bg-yellow-500 text-white rounded-md hover:bg-yellow-600 disabled:bg-gray-300 disabled:cursor-not-allowed flex items-center gap-2"
              >
                {isSubmittingReview ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    Submitting...
                  </>
                ) : assignmentSubmission?.corrections ? (
                  'Update'
                ) : (
                  'Submit'
                )}
              </button>
            </div>}
          </div>
        </div>
      </div>

      {/* Feedback Modal */}
      <FeedbackModal
        isOpen={isFeedbackModalOpen}
        feedbacks={submissionData?.feedbacks || []}
        onClose={() => setIsFeedbackModalOpen(false)}
        refetch={refetch}
        entryId={actualSubmissionId}
        marking={assignmentSubmission}
        submissionHistory={assignmentSubmission}
      />
    </HecQALayout>
  );
};

export default QASubmissionViewPage;
