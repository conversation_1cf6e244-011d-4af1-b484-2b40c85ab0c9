import React, { useMemo, useRef, useEffect, useState } from 'react';
import Image from 'next/image';

const SkinPreview = ({ skin = '{}', contentData = {}, onScaleChange }) => {
  const containerRef = useRef(null);
  const [scale, setScale] = useState(1);

  let parsedSkin = {};
  try {
    parsedSkin = JSON.parse(skin);
  } catch (error) {
    console.error('Failed to parse skin JSON:', error);
  }

  const { items = [], width = 0, height = 0, background = '#fff' } = parsedSkin;

  const mergedItems = useMemo(
    () =>
      items.map((item) => {
        if (contentData.hasOwnProperty(item.id)) {
          if (item.id === 'subject' || item.id === 'body') {
            return {
              ...item,
              content: contentData[item.id],
            };
          }
          if (contentData[item.id]) {
            return {
              ...item,
              content: contentData[item.id],
            };
          }
        }
        return item;
      }),
    [items, contentData]
  );

  useEffect(() => {
    const resizeObserver = new ResizeObserver(([entry]) => {
      const parentWidth = entry.contentRect.width;
      if (width) {
        const newScale = parentWidth / width;
        setScale(newScale);
        if (onScaleChange) {
          onScaleChange(newScale);
        }
      }
    });

    if (containerRef.current?.parentElement) {
      resizeObserver.observe(containerRef.current.parentElement);
    }

    return () => resizeObserver.disconnect();
  }, [width, onScaleChange]);

  return (
    <div
      ref={containerRef}
      style={{
        width: '100%',
        aspectRatio: width && height ? `${width} / ${height}` : '1 / 1',
        position: 'relative',
        overflow: 'hidden',
      }}
    >
      <div
        style={{
          transform: `scale(${scale})`,
          transformOrigin: 'top left',
          width: width,
          height: height,
          background,
          position: 'relative',
          boxShadow: '0 4px 8px rgba(0,0,0,0.1)',
          border: '1px solid #e5e7eb',
        }}
      >
        {mergedItems.map((item) => (
          <div
            key={item.id}
            style={{
              position: 'absolute',
              left: item.styles?.x,
              top: item.styles?.y,
              width: item.styles?.width,
              height: item.styles?.height,
              zIndex: item.zIndex,
            }}
          >
            {item.type === 'image' ? (
              <div
                style={{ position: 'relative', width: '100%', height: '100%' }}
              >
                {item.image ? (
                  <Image
                    src={item.image}
                    alt="Skin element"
                    fill
                    sizes="(max-width: 200px) 100vw"
                    style={{ objectFit: 'contain' }}
                    onError={(e) => {
                      console.error('Image failed to load:', item.image);
                      e.target.style.display = 'none';
                    }}
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center bg-gray-100 text-gray-400 text-xs">
                    Image not available
                  </div>
                )}
              </div>
            ) : (
              <div
                className={`w-full h-full ${
                  item.textType === 'body' ? 'custom-scrollbar' : ''
                }`}
                style={{
                  color: item.styles?.color || '#000000',
                  fontFamily: item.styles?.fontFamily || 'Roboto',
                  fontSize: item.styles?.fontSize || '1rem',
                  textAlign: item.styles?.textAlign || 'left',
                  display: 'block',
                  padding: '8px',
                  ...(item.textType === 'body' && {
                    maxHeight: '100%',
                    overflowY: 'auto',
                    minHeight: '200px',
                    // Fix scrolling in scaled containers
                    transform: 'translateZ(0)', // Force hardware acceleration
                    willChange: 'scroll-position', // Optimize for scrolling
                    // Ensure proper scrolling behavior
                    overflowAnchor: 'none',
                    scrollBehavior: 'auto',
                    // Ensure pointer events work for scrolling even when parent has pointer-events: none
                    pointerEvents: 'auto',
                  }),
                }}
              >
                {item.content}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default SkinPreview;
