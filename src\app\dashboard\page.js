'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Icon } from '@iconify/react';
import { useSelector } from 'react-redux';
import StatsCard from '@/components/dashboard/StatsCard';
import SubmissionCard from '@/components/dashboard/SubmissionCard';
import PerformanceChart from '@/components/dashboard/PerformanceChart';
import StudentsTable from '@/components/dashboard/StudentsTable';
import useDataFetch from '@/hooks/useDataFetch';
import TutorDashboard from './_components/TutorDashboard';

const DashboardHome = () => {
  const { user } = useSelector((state) => state.auth);
  const isTutor = user?.type === 'tutor';

  const { data: studentCount, isLoading: isStudentLoading } = useDataFetch({
    queryKey: 'dashboard-student-stats',
    endPoint: isTutor
      ? '/tutor/dashboard/student-count'
      : '/admin/dashboard/student-count',
  });

  const { data: tutorCount, isLoading: isTutorLoading } = useDataFetch({
    queryKey: 'dashboard-tutor-stats',
    endPoint: '/admin/dashboard/tutor-count',
    enabled: !isTutor,
  });

  const { data: todayAttendance, isLoading: isAttendanceLoading } =
    useDataFetch({
      queryKey: 'dashboard-today-stats',
      endPoint: isTutor
        ? '/tutor/dashboard/attendance/today'
        : '/admin/dashboard/attendance/today',
    });

  const { data: moduleCompletion, isLoading: isModuleLoading } = useDataFetch({
    queryKey: 'dashboard-module-stats',
    endPoint: '/admin/dashboard/completion-rates',
    enabled: !isTutor,
  });

  const { data: totalSubmissions, isLoading: isSubmissionLoading } =
    useDataFetch({
      queryKey: 'dashboard-submission-stats',
      endPoint: '/admin/dashboard/submissions/total',
      enabled: !isTutor,
    });

  const { data: pendingSubmissions, isLoading: isPendingLoading } =
    useDataFetch({
      queryKey: 'dashboard-pending-stats',
      endPoint: '/admin/dashboard/submissions/pending',
      enabled: !isTutor,
    });

  // Sample data - replace with actual API data
  const statsData = [
    {
      title: isTutor ? 'My Students' : 'Total Subscribed Students',
      value: isTutor
        ? studentCount?.totalAssignedStudents || 0
        : studentCount?.totalActiveStudents || 0,
      bgColor: '#FFF7ED',
      textColor: '#F97316CC',
      firstShapeColor: '#FED7AA',
      isLoading: isStudentLoading,
    },
    isTutor
      ? {
          title: 'Upcoming...',
          value: 0,
          bgColor: '#F0FDF4',
          textColor: '#22C55ECC',
          firstShapeColor: '#BBF7D0',
          isLoading: isTutorLoading,
        }
      : {
          title: 'Total Tutors',
          value: tutorCount?.activeTutors || 0,
          bgColor: '#F0FDF4',
          textColor: '#22C55ECC',
          firstShapeColor: '#BBF7D0',
          isLoading: isTutorLoading,
        },
    {
      title: "Today's Attendance",
      value: {
        present: {
          count: todayAttendance?.presentCount || 0,
          subtitle: 'Present',
        },
        absent: {
          count: todayAttendance?.absentCount || 0,
          subtitle: 'Absent',
        },
      },
      bgColor: '#ECFEFF',
      textColor: '#06B6D4',
      firstShapeColor: '#A5F3FC',
      isLoading: isAttendanceLoading,
    },
    isTutor
      ? {
          title: 'Upcoming...',
          value: 0,
          bgColor: '#F2EFFF',
          textColor: '#846CF980',
          firstShapeColor: '#846CF980',
          isLoading: isModuleLoading,
        }
      : {
          title: 'Module Completion',
          value: moduleCompletion?.totalCompletionRate || 0,
          bgColor: '#F2EFFF',
          textColor: '#846CF980',
          firstShapeColor: '#846CF980',
          isLoading: isModuleLoading,
        },
  ];

  return (
    <div className="min-h-screen">
      <div className="w-full px-5 xl:px-0 space-y-6 pb-5">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-2xl md:text-3xl font-bold text-gray-900 mb-2">
            Dashboard
          </h1>
          <p className="text-gray-600">Analytical insights</p>
        </div>

        {isTutor ? (
          <TutorDashboard
            statsData={statsData}
            totalSubmissions={totalSubmissions}
            pendingSubmissions={pendingSubmissions}
          />
        ) : (
          <div>
            <div className="grid grid-cols-1 lg:grid-cols-3 2xl:grid-cols-5 gap-6 mb-6">
              <div className="lg:col-span-2 2xl:col-span-3 space-y-6">
                {/* Top Stats Grid */}
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 md:gap-6 w-full">
                  {statsData.map((stat, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                      style={{ backgroundColor: stat?.bgColor }}
                      className=' shadow-sm border border-gray-200 rounded-lg overflow-hidden'
                    >
                      <StatsCard {...stat} />
                    </motion.div>
                  ))}
                </div>

                {/* Performance Chart and Submissions */}
                <div className="">
                  {/* Performance Chart */}
                  <motion.div
                    className="w-full"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.3 }}
                  >
                    <PerformanceChart />
                  </motion.div>
                </div>
              </div>

              {/* Submission Stats */}
              <motion.div
                className="lg:col-span-1 2xl:col-span-2 space-y-6 border rounded-lg p-3 shadow"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.4 }}
              >
                {/* Total Submissions */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">
                    Total Submission Across All Modules.
                  </h3>
                  <h1 className="text-3xl text-orange-400 font-semibold  mb-4">
                    {totalSubmissions?.totalSubmissions || 0}
                  </h1>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    {totalSubmissions?.moduleBreakdown?.map((item, index) => (
                      <SubmissionCard
                        key={index}
                        title={item?.moduleName}
                        countNumber={item?.totalSubmissions || 0}
                        bgColor={'bg-yellow-50'}
                      />
                    ))}
                  </div>
                </div>
              </motion.div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 2xl:grid-cols-5 gap-6">
              {/* Pending Submissions */}
              <div className="lg:col-span-1 2xl:col-span-2 border rounded-lg p-3 shadow-lg">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Total Pending Submission Across All Modules.
                </h3>
                <h1 className="text-3xl text-orange-400 font-semibold  mb-4">
                  {totalSubmissions?.totalPendingSubmissions || 0}
                </h1>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  {pendingSubmissions?.moduleBreakdown?.map((item, index) => (
                    <SubmissionCard
                      key={index}
                      title={item?.moduleName}
                      countNumber={item?.pendingSubmissions || 0}
                      bgColor={'bg-red-50'}
                    />
                  ))}
                </div>
              </div>

              {/* Students Table */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5 }}
                className="lg:col-span-2 2xl:col-span-3"
              >
                <StudentsTable />
              </motion.div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default DashboardHome;
