'use client';
import { useState, useRef } from 'react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import Tooltip from '@/components/Tooltip';
import ShareModalContent from './modalContents/ShareModalContent';
import EmojiSelectorModal from './EmojiSelectorModal';
import { useDispatch } from 'react-redux';
const ActionDrawer = ({ isOpen, entry, onFeedback, onClose }) => {
  const router = useRouter();

  const [showShareModal, setShowShareModal] = useState(false);

  const handleLike = () => {};

  const items = [
    {
      icon: '/assets/images/all-img/friends.svg',
      label: 'Friends',
      onClick: () => router.push('/find-friends'),
    },
    // Decoration functionality moved to paint icon in DiaryIconsSidebar
    {
      icon: '/assets/images/all-img/like.svg',
      label: 'Like',
      onClick: handleLike,
      count: entry?.likeCount ?? 0,
    },
    {
      icon: '/assets/images/all-img/share.svg',
      label: 'Share',
      onClick: () => setShowShareModal(true),
    },
    {
      icon: '/assets/images/all-img/message.svg',
      label: 'Diary Follow Request',
      onClick: () => router.push('/invite-friends'),
    },
    {
      icon: '/assets/images/all-img/edit-button.svg',
      label: 'Edit Diary',
      onClick: () => {
        // Navigate to diary my item page with entry ID
        if (entry?.id) {
          router.push(`/diary/my/item?entryId=${entry.id}`);
        }
      },
    },
  ];

  return (
    <>
      <div
        className={`absolute bottom-[12px] left-2 flex items-center gap-2 px-4 py-1 rounded-full shadow-lg transition-all duration-500 ease-in-out ${
          isOpen
            ? 'translate-x-0 opacity-100'
            : '-translate-x-60 opacity-0 pointer-events-none'
        }`}
        style={{
          background: 'linear-gradient(180deg, #FBD53F 0%, #DFA209 100%)',
        }}
      >
        <div className="flex items-center gap-2 ml-10">
          {items.map(({ icon, label, onClick, count, ref }, index) => {
            const tooltip =
              count !== undefined && count !== null
                ? `${count}${label.toLowerCase()}`
                : label;
            return (
              <Tooltip key={index} content={tooltip}>
                <button
                  ref={ref ? ref : null}
                  onClick={onClick}
                  className="w-10 h-10 flex items-center justify-center"
                >
                  <Image src={icon} alt={label} width={50} height={50} />
                </button>
              </Tooltip>
            );
          })}
        </div>
      </div>

      <AnimatePresence>
        {showShareModal && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="fixed inset-0 z-50 flex items-center justify-center bg-black/50"
            onClick={() => setShowShareModal(false)}
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.9, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.9, y: 20 }}
              transition={{ duration: 0.2, ease: 'easeOut' }}
              className="bg-white w-full max-w-lg rounded-xl overflow-auto max-h-[90vh]"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex justify-between items-center p-4 border-b">
                <h2 className="text-xl font-semibold">Share Diary</h2>

                <button
                  className="hover:opacity-80 transition-opacity"
                  onClick={() => setShowShareModal(false)}
                  aria-label="Close greeting modal"
                >
                  <Image
                    src="/assets/images/all-img/cross-bg.png"
                    alt="Close"
                    width={30}
                    height={30}
                  />
                </button>
              </div>
              <ShareModalContent entryId={entry?.id} />
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default ActionDrawer;
