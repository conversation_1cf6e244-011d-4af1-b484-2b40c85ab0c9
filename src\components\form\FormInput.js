import { useField } from 'formik';
import { useRef, useEffect } from 'react';

const FormInput = ({ label, isTextarea, required, defaultValue, autoResize, ...props }) => {
  const [field, meta] = useField(props);
  const textareaRef = useRef(null);

  // Auto-resize function for textarea
  const adjustTextareaHeight = (isFocused = false) => {
    if (textareaRef.current && autoResize) {
      const textarea = textareaRef.current;
      const minHeight = 44; // Same as input field height (2.75rem = 44px)
      
      textarea.style.height = `${minHeight}px`;
      const scrollHeight = textarea.scrollHeight;
      
      // Only expand if content requires more space than minimum height AND textarea is focused
      if (scrollHeight > minHeight && isFocused) {
        textarea.style.height = `${scrollHeight}px`;
      }
    }
  };

  // Adjust height when field value changes
  useEffect(() => {
    if (autoResize && isTextarea) {
      adjustTextareaHeight();
    }
  }, [field.value, autoResize, isTextarea]);

  // Handle input change with auto-resize
  const handleChange = (e) => {
    field.onChange(e);
    if (autoResize && isTextarea) {
      setTimeout(() => adjustTextareaHeight(true), 0);
    }
  };

  // Handle focus to expand textarea
  const handleFocus = (e) => {
    if (props.onFocus) props.onFocus(e);
    if (autoResize && isTextarea) {
      setTimeout(() => adjustTextareaHeight(true), 0);
    }
  };

  // Handle blur to shrink textarea
  const handleBlur = (e) => {
    field.onBlur(e);
    if (props.onBlur) props.onBlur(e);
    if (autoResize && isTextarea) {
      setTimeout(() => adjustTextareaHeight(false), 0);
    }
  };

  return (
    <div className="">
      <label className="block text-sm sm:text-base font-[500] mb-1">
        {label} {required && <span className="text-red-500">*</span>}
      </label>
      {!isTextarea ? (
        <input
          {...field}
          {...props}
          className={`w-full px-3 py-2 border rounded-lg focus:ring-none focus:outline-1 focus:outline-yellow-300 ${
            meta.touched && meta.error ? 'border-red-500' : 'border-gray-300'
          }`}
        />
      ) : (
        <textarea
          {...field}
          {...props}
          ref={textareaRef}
          onChange={handleChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          rows={1}
          className={`w-full px-3 pt-3 pb-2 border rounded-lg focus:ring-none focus:outline-yellow-300 resize-none overflow-hidden transition-all duration-200 ${
            autoResize ? 'min-h-[2.75rem]' : 'min-h-28'
          } ${
            meta.touched && meta.error ? 'border-red-500' : 'border-gray-300'
          }`}
          style={autoResize ? { height: '2.75rem' } : {}}
        />
      )}
      {meta.touched && meta.error && (
        <div className="text-red-500 text-xs mt-1">{meta.error}</div>
      )}
    </div>
  );
};

export default FormInput;