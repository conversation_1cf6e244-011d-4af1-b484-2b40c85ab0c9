'use client';
import React from 'react';
import Image from 'next/image';

const ImageRenderer = ({ item }) => {
  return (
    <Image
      src={item.src}
      alt={item.title || "Decoration"}
      fill
      sizes="(max-width: 200px) 100vw"
      style={{ objectFit: 'contain' }}
      className="pointer-events-none select-none"
      onError={(e) => {
        console.error('Decoration image failed to load:', item.src);
        e.target.src = '/assets/images/all-img/decoration.svg';
      }}
    />
  );
};

export default ImageRenderer;