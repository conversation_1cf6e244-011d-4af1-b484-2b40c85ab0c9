'use client';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import NewTablePage from '@/components/form/NewTablePage';
import useDataFetch from '@/hooks/useDataFetch';

const DiaryEntriesList = () => {
  const router = useRouter();
  const [filteredEntries, setFilteredEntries] = useState([]);
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [totalPages, setTotalPages] = useState(1);

  // Use the provided useDataFetch hook for diary entries
  const { data, isLoading, refetch } = useDataFetch({
    queryKey: ['/admin/diary/entries', page, limit],
    endPoint: '/admin/diary/entries',
    params: { page, limit },
    enabled: true
  });

  // Process data whenever it changes
  useEffect(() => {
    if (data && data.items) {
      // Process diary entries data
      const processedEntries = data.items.map((entry, index) => {
        return {
          ...entry,
          '#': (page - 1) * limit + index + 1,
          userName: entry.diary?.userName || 'Unknown User',
          status : entry.status,
          tutorName : entry.correction?.tutorName || 'No one hooked',
          entryTitle: entry.title || 'Untitled Entry',
          entryDate: entry.entryDate ? 
            new Date(entry.entryDate).toLocaleDateString() : 
            new Date(entry.createdAt).toLocaleDateString(),
          statusDisplay: entry.status === 'new' ? 'New' : 
                       entry.status === 'reviewed' ? 'Reviewed' : 
                       entry.status === 'evaluated' ? 'Evaluated' : 
                       entry.status,
          skinName: entry.skin?.name || 'No Skin',
          level: entry.settings?.title || 'No Level',
          wordLimit: entry.settings?.wordLimit || 'N/A',
          score: entry.score || 'Not Scored',
          evaluatedAt: entry.evaluatedAt ? 
            new Date(entry.evaluatedAt).toLocaleDateString() : '',
          likeCount: entry.likeCount || 0,
          feedbackCount: entry.feedbacks?.length || 0,
          isPrivate: entry.isPrivate || false
        };
      });
      
      setTotalPages(Math.ceil(data.totalCount / limit));
      setFilteredEntries(processedEntries);
    }
  }, [data, limit, page]);

  // Pagination handler
  const changePage = (newPage) => {
    setPage(newPage);
  };

  // Handler for rows per page change
  const handleRowsPerPageChange = (newLimit) => {
    setLimit(newLimit);
    setPage(1);
  };

  // Navigate to diary entry details page
  const viewEntryDetails = (entry) => {
    router.push(`/dashboard/module-management/hec-diary/diary-details/${entry?.id}`);
  };

  // User Avatar component
 const UserAvatar = ({ name }) => {
    return (
      <div className="flex items-center justify-center h-8 w-8 rounded-full bg-amber-100 text-amber-800">
        <svg 
          className="h-5 w-5" 
          xmlns="http://www.w3.org/2000/svg" 
          viewBox="0 0 24 24" 
          fill="currentColor"
        >
          <path 
            fillRule="evenodd" 
            d="M7.5 6a4.5 4.5 0 119 0 4.5 4.5 0 01-9 0zM3.751 20.105a8.25 8.25 0 0116.498 0 .75.75 0 01-.437.695A18.683 18.683 0 0112 22.5c-2.786 0-5.433-.608-7.812-1.7a.75.75 0 01-.437-.695z" 
            clipRule="evenodd" 
          />
        </svg>
      </div>
    );
  };

  // Status Badge component
  const StatusBadge = ({ status }) => {
    const getStatusColor = () => {
      switch (status.toLowerCase()) {
        case 'new':
          return 'bg-blue-50 text-blue-700';
        case 'reviewed':
          return 'bg-green-50 text-green-700';
        case 'evaluated':
          return 'bg-purple-50 text-purple-700';
        default:
          return 'bg-gray-50 text-gray-700';
      }
    };

    return (
      <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor()}`}>
        {status}
      </span>
    );
  };

  // Privacy Badge component
  const PrivacyBadge = ({ isPrivate }) => {
    return isPrivate ? (
      <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-red-50 text-red-700">
        Private
      </span>
    ) : (
      <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-green-50 text-green-700">
        Public
      </span>
    );
  };

  // Define table columns
  const columns = [
    { 
      field: 'userName', 
      label: 'USER NAME', 
      
      cellRenderer: (value, row) => (
        <div className="flex items-center space-x-2">
          <UserAvatar name={value} />
          <span className="font-medium text-gray-900">{value}</span>
        </div>
      )
    },

    { 
      field: 'statusDisplay', 
      label: ' REVIEW STATUS', 
     
      cellRenderer: (value) => <StatusBadge status={value} />
    },

    { 
      field: 'tutorName',
      label: 'REVIEWED BY',
      cellRenderer: (value) => (
        <div className="flex items-center space-x-2">
          {value === 'No one hooked' && (
            <div className="flex items-center justify-center w-4 h-4 rounded-full bg-red-100">
              <svg className="w-3 h-3 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 12h14" />
              </svg>
            </div>
          )}
          <span className={value === 'No one hooked' ? 'text-gray-500 ' : 'text-gray-900'}>
            {value}
          </span>
        </div>
      )
    },
    
    {
      field: 'action',
      label: 'ACTION',
      sortable: false,
      cellRenderer: (value, row) => (
        <div className="flex items-center space-x-2">
          <button
            onClick={() => viewEntryDetails(row)}
            className="inline-flex items-center justify-center p-2 rounded-full bg-blue-50 text-blue-600 hover:bg-blue-100 transition-colors"
            aria-label="View entry details"
          >
            <svg className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
              <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
              <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
            </svg>
          </button>
        </div>
      )
    }
  ];

  return (
    <div className="bg-white p-6 rounded-lg">
      <div className="flex justify-between items-center">
        <h1 className="text-xl font-semibold">HEC Diary</h1> 
      </div>
      
      <NewTablePage
        title=""
        createButton="Create Entry"
        createBtnLink="/diary/create"
        columns={columns}
        data={filteredEntries}
        currentPage={page}
        totalPages={totalPages}
        changePage={changePage}
        loading={isLoading}
        totalItems={data?.totalCount || 0}
        rowsPerPage={limit}
        setRowsPerPage={handleRowsPerPageChange}
        showCheckboxes={false}
        showSearch={false}
        searchPlaceholder="Search entries..."
        showNameFilter={false}
        showSortFilter={false}
        showCreateButton={false}
        paginationLabel="Entries per page:"
      />
    </div>
  );
};

export default DiaryEntriesList;