/* We're using Next.js font loading system instead of direct imports */

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #ffffff;
  --foreground: #171717;
  --font-open-sans-fallback: 'Open Sans', Arial, sans-serif;
}

/* @media (prefers-color-scheme: light) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
} */

body {
  color: var(--foreground);
  background: var(--background);
  font-family: var(--font-open-sans), var(--font-open-sans-fallback), sans-serif;
}

/* Disable spell checking and grammar checking globally for English learning platform */
input, textarea, [contenteditable] {
  -webkit-spellcheck: false !important;
  -moz-spellcheck: false !important;
  spellcheck: false !important;
}

/* Disable Grammarly and other extensions globally */
input[data-gramm="false"],
textarea[data-gramm="false"],
[contenteditable][data-gramm="false"] {
  outline: none !important;
}

/* Hide Grammarly and other extension overlays */
grammarly-extension,
grammarly-popups,
[data-grammarly-shadow-root],
.__grammarly_editor_wrapper {
  display: none !important;
  visibility: hidden !important;
}

/* FullCalendar Custom Styles */
.fc .fc-toolbar-title {
  font-size: 1.25rem !important;
  font-weight: 600;
}

.fc .fc-button {
  background-color: #4f46e5 !important;
  border-color: #4f46e5 !important;
  box-shadow: none !important;
  padding: 0.375rem 0.75rem !important;
  font-size: 0.875rem !important;
}

.fc .fc-button:hover {
  background-color: #4338ca !important;
  border-color: #4338ca !important;
}

.fc .fc-button-primary:not(:disabled).fc-button-active,
.fc .fc-button-primary:not(:disabled):active {
  background-color: #3730a3 !important;
  border-color: #3730a3 !important;
}

.fc .fc-daygrid-day.fc-day-today {
  background-color: rgba(79, 70, 229, 0.1) !important;
}

/* Square date cells */
.fc .fc-daygrid-day {
  height: 50px !important;
  min-height: 50px !important;
  position: relative;
}

.fc .fc-daygrid-day-frame {
  height: 100% !important;
  min-height: 50px !important;
  display: flex !important;
  flex-direction: column !important;
}

.fc .fc-daygrid-day-top {
  flex-shrink: 0 !important;
}

.fc .fc-daygrid-day-events {
  flex-grow: 1 !important;
  overflow: hidden !important;
  display: none;
}

.fc .fc-daygrid-day-number {
  font-size: 0.875rem;
  padding: 0.25rem !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100% !important;
  height: 100% !important;
  min-height: 24px !important;
}

.fc .fc-col-header-cell-cushion {
  font-weight: 600;
  color: #4b5563;
  padding: 0.5rem 0 !important;
}

.fc-theme-standard td,
.fc-theme-standard th,
.fc-theme-standard .fc-scrollgrid {
  border-color: #e5e7eb !important;
}

.fc .fc-highlight {
  background-color: rgba(79, 70, 229, 0.2) !important;
}

/* Ensure consistent width for all day cells */
.fc .fc-daygrid-day {
  width: calc(100% / 7) !important;
}

/* Make sure the calendar table takes full width */
.fc .fc-scrollgrid-sync-table {
  width: 100% !important;
}

.fc .fc-scrollgrid-sync-table td {
  width: calc(100% / 7) !important;
}

/* Calendar container specific styles for square dates */
.calendar-container .fc-daygrid-day {
  aspect-ratio: 1 / 1 !important;
  height: auto !important;
  min-height: 45px !important;
  max-height: 60px !important;
}

/* Force square shape for calendar cells */
.calendar-container .fc-daygrid-day-frame {
  height: 100% !important;
  width: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  justify-content: flex-start !important;
  align-items: center !important;
}

/* Responsive square sizing */
@media (max-width: 640px) {
  .calendar-container .fc-daygrid-day {
    min-height: 35px !important;
  }

  .fc .fc-daygrid-day-number {
    font-size: 0.75rem !important;
    padding: 0.125rem !important;
  }
}

/* Event styling within square cells */
.fc .fc-daygrid-event {
  font-size: 0.625rem !important;
  padding: 1px 2px !important;
  margin: 1px 0 !important;
  border-radius: 2px !important;
}

.fc .fc-daygrid-event-harness {
  margin: 1px 0 !important;
}

/* 3D Perspective for diary opening animation */
.perspective-1000 {
  perspective: 1000px;
}

.preserve-3d {
  transform-style: preserve-3d;
}

/* Smooth diary opening animation */
.diary-opening {
  transition: transform 1s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Disable browser autocomplete and suggestions */
input, textarea {
  autocomplete: off !important;
  autocorrect: off !important;
  autocapitalize: off !important;
}

/* Default font class */
.font-open-sans {
  font-family: var(--font-open-sans), var(--font-open-sans-fallback), sans-serif;
}

.swiper-button-prev,
.swiper-button-next {
  width: 40px !important; /* Adjust button size */
  height: 40px !important;
  background-color: white !important; /* Set background */
  border-radius: 8px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.swiper-button-prev::after,
.swiper-button-next::after {
  font-size: 24px !important;
  color: gray !important;
}

/* Hide number input spinner for all browsers */
.no-spinner::-webkit-outer-spin-button,
.no-spinner::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.no-spinner {
  -webkit-appearance: textfield; /* Chrome, Safari, newer versions of Opera */
  -moz-appearance: textfield; /* Firefox */
  appearance: textfield; /* Standard */
}
/* Common styles */
.loader-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}

.loader-text {
  margin-top: 1rem;
  color: #666;
  font-size: 0.9rem;
}

/* Spinning Book */
.book-loader {
  perspective: 1000px;
}

.book {
  width: 60px;
  height: 80px;
  position: relative;
  transform-style: preserve-3d;
  animation: rotate 2s infinite linear;
}

.book-page {
  position: absolute;
  width: 100%;
  height: 100%;
  background: #f0f0f0;
  border: 2px solid #ddd;
  transform-origin: left;
  animation: flip 1.5s infinite ease-in-out;
}

/* Pulsing Pages */
.pulsing-pages {
  display: flex;
  gap: 4px;
}

.pulsing-pages .page {
  width: 20px;
  height: 30px;
  background: #4a90e2;
  animation: pulse 1.5s infinite ease-in-out;
}

/* Circular Progress */
.circular-loader {
  width: 50px;
  height: 50px;
}

.circular-loader circle {
  stroke: #4a90e2;
  stroke-linecap: round;
  animation: circle-progress 2s infinite linear;
}

/* Flipping Pages */
.flipping-pages {
  position: relative;
  width: 40px;
  height: 40px;
}

.flip-page {
  position: absolute;
  width: 100%;
  height: 100%;
  background: #4a90e2;
  animation: flip-page 1.2s infinite ease-in-out;
}

/* Dots Wave */
.dots-wave {
  display: flex;
  gap: 6px;
}

.dot {
  width: 8px;
  height: 8px;
  background: #4a90e2;
  border-radius: 50%;
  animation: wave 1s infinite ease-in-out;
}

/* Bookshelf */
.bookshelf {
  display: flex;
  gap: 8px;
  padding: 10px;
  background: #f5f5f5;
  border-radius: 4px;
}

.book-item {
  width: 20px;
  height: 60px;
  background: #4a90e2;
  animation: bounce 0.6s infinite alternate;
}

/* Text Scramble */
.text-scramble {
  display: flex;
  gap: 2px;
}

.letter {
  font-size: 1.2rem;
  font-weight: bold;
  color: #4a90e2;
  animation: scramble 2s infinite;
}

/* Progress Bar */
.progress-bar {
  width: 200px;
  height: 4px;
  background: #f0f0f0;
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  width: 100%;
  height: 100%;
  background: #4a90e2;
  animation: progress 2s infinite;
}

/* Ink Drop */
.ink-drop {
  position: relative;
  width: 60px;
  height: 60px;
}

.drop {
  width: 20px;
  height: 20px;
  background: #4a90e2;
  border-radius: 50%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation: drop 2s infinite;
}

/* Minimalist Spinner */
.minimalist-spinner {
  width: 40px;
  height: 40px;
  position: relative;
}

.spinner-ring {
  width: 100%;
  height: 100%;
  border: 3px solid #f0f0f0;
  border-top-color: #4a90e2;
  border-radius: 50%;
  animation: spin 1s infinite linear;
}

/* Animations */
@keyframes rotate {
  from {
    transform: rotateY(0deg);
  }
  to {
    transform: rotateY(360deg);
  }
}

@keyframes flip {
  0%,
  100% {
    transform: rotateY(0deg);
  }
  50% {
    transform: rotateY(-180deg);
  }
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(0.8);
    opacity: 0.5;
  }
}

@keyframes circle-progress {
  0% {
    stroke-dasharray: 0 100;
  }
  100% {
    stroke-dasharray: 100 100;
  }
}

@keyframes flip-page {
  0%,
  100% {
    transform: rotateX(0deg);
  }
  50% {
    transform: rotateX(180deg);
  }
}

@keyframes wave {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes bounce {
  to {
    transform: translateY(-10px);
  }
}

@keyframes scramble {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

@keyframes progress {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes drop {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(3);
    opacity: 0;
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.text-stroke {
  -webkit-text-stroke: 0.5px gray;
}

/* Topbar Styles */
.topbar-dropdown {
  transition: all 0.2s ease;
}

.topbar-button {
  transition: all 0.2s ease;
}

.topbar-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-input:focus {
  box-shadow: 0 0 0 2px rgba(255, 222, 52, 0.5);
}



.yellow-btn {
  background-image: linear-gradient(3.66deg, #DCA600 26.83%, #FFDE5B 97.36%);
  background-color: #FFFAC2;
}
.yellow-btn:hover{
  background-image: none;
}

/* Custom Scrollbar Styles */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #E6D7A9;
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #D9C88F;
}

/* Fix scrolling in scaled containers */
.custom-scrollbar {
  /* Ensure proper scrolling behavior in transformed containers */
  transform: translateZ(0);
  -webkit-overflow-scrolling: touch;
  /* Force layer creation for better performance */
  will-change: scroll-position;
  /* Ensure scrolling works even when parent has pointer-events disabled */
  pointer-events: auto !important;
  /* Fix for scaled containers */
  contain: layout style paint;
}







/* Canvas container responsive styles */
.canvas-container-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.canvas-container {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}

.canvas-wrapper {
  transform-origin: center;
  max-width: 100%;
  max-height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Scale canvas to fit container while maintaining aspect ratio */
.canvas-wrapper > div {
  transform: scale(0.95); /* Default scale reduced to account for browser zoom */
  transform-origin: center;
  max-width: 100%;
  max-height: 100%;
}

/* Responsive scaling for different screen sizes */
@media (max-width: 1600px) {
  .canvas-wrapper > div {
    transform: scale(0.65);
  }
}

@media (max-width: 1366px) {
  .canvas-wrapper > div {
    transform: scale(0.55);
  }
}

@media (max-width: 1200px) {
  .canvas-wrapper > div {
    transform: scale(0.5);
  }
}

@media (max-width: 1024px) {
  .canvas-wrapper > div {
    transform: scale(0.45);
  }
}

@media (max-width: 768px) {
  .canvas-wrapper > div {
    transform: scale(0.4);
  }
}

@media (max-width: 480px) {
  .canvas-wrapper > div {
    transform: scale(0.35);
  }
}

/* date time component styles */
.custom-calendar {
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  padding: 8px;
  font-family: var(--font-open-sans), 'Open Sans', sans-serif;
}

.react-datepicker__header {
  background: none;
  border-bottom: none;
  padding-top: 10px;
}

.react-datepicker__day-name,
.react-datepicker__day,
.react-datepicker__time-name {
  margin: 0.3rem;
  font-size: 0.85rem;
  font-weight: 500;
}

.custom-day {
  border-radius: 50%;
  width: 2rem;
  height: 2rem;
  line-height: 2rem;
  text-align: center;
}

.custom-day-selected {
  background-color: #facc15;
  color: white;
  font-weight: bold;
  border-radius: 50%;
  width: 2rem;
  height: 2rem;
  line-height: 2rem;
  text-align: center;
}

.react-datepicker__time-container {
  border-left: none;
}

.react-datepicker__time {
  border-radius: 0 0 12px 12px;
}

.react-datepicker__time-box {
  width: 100px;
}

.react-datepicker__time-list-item--selected {
  background-color: #facc15;
  color: white;
  font-weight: bold;
}
