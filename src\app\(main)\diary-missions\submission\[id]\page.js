'use client';
import DiaryFeedbackModal from '@/app/dashboard/(admin)/module-management/hec-diary/teacher-feedback/page';
import Button, { ButtonIcon } from '@/components/Button';
import EditorViewer from '@/components/EditorViewer';
import DetailsModal from '@/components/form/modal/MissionConfirmationModal';
import GoBack from '@/components/shared/GoBack';
import useDataFetch from '@/hooks/useDataFetch';
import api from '@/lib/api';
import SimpleTiptapEditor from '@/components/form/SimpleTiptapEditor';
import { Form, Formik } from 'formik';
import Image from 'next/image';
import { useParams, useRouter } from 'next/navigation';
import React, { useEffect, useRef, useState } from 'react';
import MissionFeedbackModal from '../../_components/DiaryFeedbackModal';
import HistoryModal from '@/app/(main)/novel/_components/HistoryModal';
import WordValidationMsg from '@/components/form/validation/WordValidationMsg';

const Submittion = () => {
  const { id } = useParams();
  const router = useRouter();

  const getEndPoint = `/diary/missions/entries/mission/${id}`;

  const editorRef = useRef(null);
  const [showHistory, setShowHistory] = useState(false);
  const [value, setValue] = useState('');
  const [currentWordCount, setCurrentWordCount] = useState(0);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [isTargetedWordCount, setIsTargetedWordCount] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);

  const { data, refetch } = useDataFetch({
    queryKey: [`question-info`, getEndPoint],
    endPoint: getEndPoint,
  });

  const questionDetails = data?.mission;

  const showSubmission = data?.content?.length > 0 && !isSubmitted;
  const showFeedback = data?.feedbacks?.length > 0 && data?.feedbacks;

  // Count words in HTML content
  const countWords = (html) => {
    if (!html) return 0;
    // Remove HTML tags
    const text = html.replace(/<[^>]*>/g, ' ');
    // Remove entities
    const cleanText = text.replace(/&nbsp;|&amp;|&lt;|&gt;|&quot;|&#39;/g, ' ');
    // Remove extra spaces and split by whitespace
    const words = cleanText
      .trim()
      .split(/\s+/)
      .filter((word) => word.length > 0);
    return words.length;
  };

  const handleSubmit = async (values, { setSubmitting }) => {
    try {
      setSubmitting(true);
      const response = await api.post(
        `/diary/missions/entries/${data?.id}/submit`,
        values
      );
      console.log(response);
      setIsSubmitted(true);
      refetch();
      router.push('/diary-missions');
    } catch (error) {
      console.log(error);
    } finally {
      setSubmitting(false);
    }
  };

  const handleUpdate = async (values, formikHelpers = {}) => {
    try {
      console.log('Values for update.', values);
      // Ensure we have the content value
      if (!values || !values.content) return;

      const { setSubmitting } = formikHelpers;
      if (setSubmitting) setSubmitting(true);

      // If it's an explicit submission (not auto-save), update immediately
      if (!values._autoSave) {
        const response = await api.put(
          `/diary/missions/entries/${data?.id}`,
          {
            content: values.content,
          },
          { showSuccessToast: false }
        );

        console.log('Saved:', response);
        refetch();
        setIsSubmitted(false);
        return;
      }

      // Auto-save logic
      try {
        const response = await api.put(
          `/diary/missions/entries/${data?.id}`,
          {
            content: values.content,
          },
          { showSuccessToast: false }
        );

        console.log('Auto-saved (debounced):', response);
      } catch (error) {
        console.error('Error auto-saving submission:', error);
      }
    } catch (error) {
      console.error('Error updating submission:', error);
    } finally {
      if (formikHelpers.setSubmitting) formikHelpers.setSubmitting(false);
    }
  };
  // Initialize value from API data
  useEffect(() => {
    if (data?.content) {
      setValue(data.content);
      setIsInitialized(true);
    }
  }, [data]);

  useEffect(() => {
    if (value) {
      const count = countWords(value);
      setCurrentWordCount(count);
    }
  }, [value]);

  // Debounced auto-save effect - only trigger after initialization and when user changes content
  useEffect(() => {
    // Don't auto-save if not initialized or no content
    if (!isSubmitted) return;

    const timeoutId = setTimeout(() => {
      handleUpdate({
        content: value,
        _autoSave: true,
      });
    }, 800);

    // Cleanup function to clear timeout if component unmounts or dependencies change
    return () => clearTimeout(timeoutId);
  }, [value, isInitialized]);

  return (
    <div className="relative">
      <div className="max-w-7xl mx-auto px-5 xl:px-0 relative z-10">
        <GoBack
          title={'Diary Missions'}
          linkClass="my-5 mb-8 w-full max-w-52"
        />

        <div className="p-5 rounded-lg bg-[#FFF9FB] shadow-lg space-y-5 mb-10">
          <div className="p-5 bg-[#FCF8EF] rounded-lg [box-shadow:2px_2px_12px_0px_#F5D1B066_inset,_-2px_-2px_12px_0px_#F5D1B066_inset]">
            {questionDetails?.category?.name && (
              <span className="bg-yellow-200 font-medium text-xs px-2 py-0.5 rounded">
                Category: {questionDetails?.category?.name}
              </span>
            )}
            <h1 className="text-2xl text-yellow-800 font-semibold">
              {questionDetails?.title}
            </h1>
            <div className="flex items-start gap-6">
              {questionDetails?.picture && (
                <Image
                  src={questionDetails?.picture}
                  alt={questionDetails?.title}
                  width={200}
                  height={200}
                />
              )}
              <div>
                {(questionDetails?.instructions ||
                  questionDetails?.description) && (
                  <p className="font-semibold text-lg text-gray-700">
                    Instruction:
                  </p>
                )}
              </div>
            </div>
            <EditorViewer
              data={
                questionDetails?.instructions || questionDetails?.description
              }
            />
          </div>

          {showSubmission ? (
            <div className="space-y-3">
              <div className="p-5 shadow-lg rounded-lg border relative bg-white">
                <h1 className="text-xl text-yellow-800 font-semibold">
                  My Diary Mission Entry
                </h1>
                <EditorViewer
                  data={
                    data?.content?.length > 200
                      ? data?.content?.slice(0, 400) + '...'
                      : data?.content
                  }
                />
                {(data?.status === 'reviewed' ||
                  data?.isResubmission ||
                  data?.status === 'new') && (
                  <div className="absolute right-2 top-2 flex items-center gap-2">
                    <ButtonIcon
                      icon={'ri:edit-2-fill'}
                      innerBtnCls={'h-10 w-10'}
                      btnIconCls={'h-5 w-5'}
                      onClick={() => setIsSubmitted(true)}
                    />
                  </div>
                )}
              </div>

              <div className="p-5 shadow-lg rounded-lg border relative bg-white">
                <h1 className="text-xl text-yellow-800 font-semibold">
                  Tutor Correction Zone
                </h1>
                {showFeedback && (
                  <>
                    <div className="absolute right-2 top-2">
                      <ButtonIcon
                        icon={'arcticons:feedback-2'}
                        innerBtnCls={'h-10 w-10'}
                        btnIconCls={'h-4 w-4'}
                        onClick={() => setShowDetailsModal(true)}
                      />
                    </div>
                  </>
                )}

                {data?.correction && (
                  <div
                    dangerouslySetInnerHTML={{
                      __html: data?.correction?.correctionText,
                    }}
                    className={`mt-2`}
                  />
                )}

                <p
                  className={`mt-2 ${
                    data?.status === 'reviewed' || data?.isResubmission
                      ? 'text-green-500 text-center'
                      : 'text-red-600 text-center'
                  }`}
                >
                  {data?.status === 'reviewed' || data?.isResubmission
                    ? 'Reviewed'
                    : 'Not Reviewed Yet'}
                </p>
              </div>
            </div>
          ) : (
            <Formik
              initialValues={{
                content: value || questionDetails?.submission?.answer || '',
              }}
              onSubmit={(values, formikHelpers) => {
                handleSubmit(values, formikHelpers);
              }}
              enableReinitialize
            >
              {({ isSubmitting }) => (
                <Form>
                  <div className="space-y-2">
                    <div className="relative">
                      <div className="absolute right-2 top-3 z-10">
                        <button
                          className="bg-[#FFF9E6] border border-[#D4A574] text-[#8B4513] hover:bg-[#FFF5D6] hover:border-[#C19A5B] text-xs font-medium px-4 py-1 rounded-full transition-colors duration-200 shadow"
                          onClick={() => setShowHistory(true)}
                          aria-label="View History"
                        >
                          View History
                        </button>
                      </div>

                      <SimpleTiptapEditor
                        ref={editorRef}
                        onInit={(_evt, editor) => (editorRef.current = editor)}
                        initialValue={value || '<p></p>'}
                        onEditorChange={(content) => {
                          setValue(content);
                          setCurrentWordCount(countWords(content));
                        }}
                        height={500}
                        maxWords={questionDetails?.targetMaxWordCount}
                      />

                      {/* Word count display */}
                      <div className="absolute left-2 w-[98.5%] z-20 flex justify-between items-center mt-2 text-sm">
                        <div className="font-medium text-gray-600">
                          <span>
                            {currentWordCount} /{' '}
                            {questionDetails?.targetMaxWordCount} words
                          </span>
                        </div>
                      </div>

                      <div className="h-6 rounded-b-lg w-[98.5%] bg-white absolute left-1 bottom-1 z-10"></div>
                    </div>

                    <div className="flex justify-end">
                      <WordValidationMsg
                        wordCount={currentWordCount}
                        minimumWords={questionDetails?.targetWordCount}
                        maximumWords={questionDetails?.targetMaxWordCount}
                        setIsValidWord={setIsTargetedWordCount}
                      />
                    </div>
                  </div>

                  {(data?.status === 'reviewed' ||
                    data?.isResubmission ||
                    data?.status === 'new') && (
                    <div className="flex justify-center mt-3 gap-3">
                      <Button
                        buttonText="Cancel"
                        type="button"
                        onClick={() => setIsSubmitted(false)}
                      />
                      <Button
                        disabled={isSubmitting}
                        buttonText={
                          data?.status === 'new'
                            ? isSubmitting
                              ? 'Submitting...'
                              : 'Submit'
                            : data?.status === 'reviewed'
                            ? isSubmitting
                              ? 'Updating...'
                              : 'Update'
                            : 'Update'
                        }
                        type="submit"
                        className="bg-yellow-400 hover:bg-yellow-500 text-black"
                      />
                    </div>
                  )}
                </Form>
              )}
            </Formik>
          )}
        </div>
      </div>

      <MissionFeedbackModal
        isOpen={showDetailsModal}
        onClose={() => setShowDetailsModal(false)}
        data={data?.feedbacks}
        title="Teacher's Feedback"
        link={`/answer`}
        showBtn={false}
      />

      {showHistory && (
        <HistoryModal
          isOpen={showHistory}
          onClose={() => setShowHistory(false)}
          endPoint={`/diary/missions/entries/${data?.id}/history`}
          moduleKey="diary_mission"
          mainComRefetch={refetch}
        />
      )}
    </div>
  );
};

export default Submittion;
