'use client';

import Image from 'next/image';
import React from 'react';
import { useRouter } from 'next/navigation';
import { useSelector } from 'react-redux';
import { navigateByRole } from '@/lib/navigation';
import Button from '../Button';

const HeroSection = () => {
  const router = useRouter();
  const { isAuth, user } = useSelector((state) => state.auth);

  const handleGetStarted = () => {
    navigateByRole(router, isAuth, user, '/login');
  };

  return (
    <div className="bg-yellow-100 flex items-center justify-center min-h-[89vh] overflow-hidden relative">
      <div
        className="absolute hidden md:block left-0 top-0 z-0"
      >
        <Image
          src="/assets/images/all-img/Frame1.png"
          alt="icon"
          width={1920}
          height={800}
          className='w-[100vw] min-h-[89vh] object-cover'
          priority
        />
      </div>

      <div
        className="absolute hidden md:block left-0 top-0 z-0"
      >
        <Image
          src="/assets/images/all-img/Groupp.png"
          alt="icon"
          width={964}
          height={750}
          priority
        />
      </div>

      <div className="max-w-7xl mx-auto flex flex-col lg:flex-row justify-between h-[89vh] w-full z-30 p-5 lg:p-0">
        <div className="my-auto space-y-3">
          <h2 className="text-6xl">Hello English</h2>
          <h2 className="text-6xl pb-3">Coaching</h2>
          <div className="p-px bg-white rounded-full border border-yellow-800 max-w-52">
            <button
              onClick={handleGetStarted}
              className="px-4 py-2.5 bg-[#864D0D] text-white rounded-full hover:bg-[#6A3B0D] w-full transition-colors"
            >
              Get Started
            </button>
          </div>
        </div>

        <div className="self-end">
          <Image
            src="/assets/images/all-img/introduction/header10.png"
            alt="icon"
            width={630}
            height={600}
            className="w-full h-auto xl:min-w-[600px] 2xl:min-w-[800px] relative xl:-right-20 2xl:-right-40"
            priority
          />
        </div>
      </div>
    </div>
  );
};

export default HeroSection;
