import React from 'react';
import Link from 'next/link';
import { FiChevronDown } from 'react-icons/fi';

const DesktopMenu = ({
  menuItems,
  activeMenu,
  handleMenuClick,
  handleMenuItemClick,
  isParentMenuActive,
  isSubmenuItemActive,
  currentPlanId
}) => {
  return (
    <div className="py-3 text-center bg-[#FFDE34] relative hidden lg:block">
      <div className="flex items-center justify-center space-x-8">
        {menuItems.map((item, index) => (
          <div key={index} className="relative group">
            <button
              className={`relative text-black hover:text-black menu-item flex items-center cursor-pointer ${
                activeMenu === item.name || isParentMenuActive(item)
                  ? ''
                  : ''
              }`}
              onClick={() =>
                item.submenu
                  ? handleMenuClick(item.name)
                  : handleMenuItemClick(item.path)
              }
            >
              {item.name}
              {item.submenu && (
                <FiChevronDown
                  className={`ml-1 transition-transform duration-200 ${
                    activeMenu === item.name ? 'transform rotate-180' : ''
                  }`}
                  size={16}
                />
              )}
              <span
                className={`absolute bottom-[-10px] left-0 w-full h-[4px] bg-[#cd7f32] transform ${
                  activeMenu === item.name || isParentMenuActive(item)
                    ? 'scale-x-100'
                    : 'scale-x-0'
                } group-hover:scale-x-100 transition-all duration-500 origin-right z-10`}
              ></span>
            </button>

            {/* Submenu */}
            {item.submenu && activeMenu === item.name && (
              <>
                {/* Triangle/arrow above the submenu */}
                <div className="absolute left-1/2 transform -translate-x-1/2 mt-4 w-4 h-4 bg-white border-t border-l border-gray-200 rotate-45 z-40"></div>
                <div className="absolute left-1/2 transform -translate-x-1/2 mt-6 w-48 bg-white border border-gray-200 rounded-md shadow-lg z-50 overflow-hidden submenu">
                  {item.submenu.map((subItem, subIndex) => (
                    <React.Fragment key={subIndex}>
                      <Link
                        href={subItem.path}
                        className={`block px-4 py-3 text-sm touch-manipulation ${
                          isSubmenuItemActive(subItem)
                            ? 'font-bold text-yellow-600'
                            : 'text-gray-700'
                        } hover:font-bold transition-all duration-200 transform hover:scale-105 active:bg-[#FFE34D]`}
                        onClick={() => handleMenuItemClick(subItem.path)}
                      >
                        {subItem.name}
                      </Link>
                      {subIndex < item.submenu.length - 1 && (
                        <div className="mx-2 border-b border-dashed border-gray-300"></div>
                      )}
                    </React.Fragment>
                  ))}
                </div>
              </>
            )}
          </div>
        ))}

        <div className="absolute right-28">
          <Link
            href="/pricing-plans"
            className="text-white bg-gray-800 border border-gray-100 rounded-lg text-sm px-4 py-1.5"
          >
            {currentPlanId ? 'Upgrade Now' : 'Subscribe Now'}
          </Link>
        </div>
      </div>
    </div>
  );
};

export default DesktopMenu;
