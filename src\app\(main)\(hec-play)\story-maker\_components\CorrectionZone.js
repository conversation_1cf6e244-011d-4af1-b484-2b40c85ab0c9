import React from 'react';

const CorrectionZone = ({ questionDetails }) => {
  return (
    <div className="p-5 shadow-lg rounded-lg border relative bg-white">
      <h1 className="text-xl text-yellow-800 font-semibold">
        AI Correction Zone
      </h1>
      {questionDetails?.evaluation ? (
        <>
          {questionDetails?.submission?.corrections?.grammar?.length > 0 && (
            <div className="rounded-md">
              <ul className=" text-sm text-gray-800">
                {questionDetails.submission.corrections.grammar.map(
                  (item, index) => (
                    <EditorViewer key={index} data={item} />
                  )
                )}
              </ul>
            </div>
          )}

          {/* Evaluation Data in Tag View */}
          {questionDetails?.evaluation && (
            <div className="mt-4 space-y-6">
              {/* Score Tags */}
              <div className="flex flex-wrap gap-2">
                <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                  Score: {questionDetails.evaluation.score}
                </span>
                <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800">
                  {questionDetails.evaluation.percentage}%
                </span>
              </div>

              {/* Feedback Tags */}
              {questionDetails.evaluation.feedback && (
                <div className="space-y-3">
                  {/* Main Message */}
                  <div className="flex flex-wrap gap-2">
                    <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                      📝 {questionDetails.evaluation.feedback.message}
                    </span>
                  </div>

                  {/* Encouragement */}
                  {questionDetails.evaluation.feedback.encouragement && (
                    <div className="flex flex-wrap gap-2">
                      <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-pink-100 text-pink-800">
                        ✨ {questionDetails.evaluation.feedback.encouragement}
                      </span>
                    </div>
                  )}

                  {/* Strengths */}
                  {questionDetails.evaluation.feedback.strengths?.length >
                    0 && (
                    <div className="space-y-2">
                      <h4 className="text-sm font-semibold text-green-700">
                        Strengths:
                      </h4>
                      <div className="flex flex-wrap gap-2">
                        {questionDetails.evaluation.feedback.strengths.map(
                          (strength, index) => (
                            <span
                              key={index}
                              className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800"
                            >
                              ✅ {strength}
                            </span>
                          )
                        )}
                      </div>
                    </div>
                  )}

                  {/* Suggestions */}
                  {questionDetails.evaluation.feedback.suggestions?.length >
                    0 && (
                    <div className="space-y-2">
                      <h4 className="text-sm font-semibold text-blue-700">
                        Suggestions:
                      </h4>
                      <div className="flex flex-col gap-2">
                        {questionDetails.evaluation.feedback.suggestions.map(
                          (suggestion, index) => (
                            <div key={index}>
                              <span className="px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 inline-block">
                                {suggestion}
                              </span>
                            </div>
                          )
                        )}
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Performance Details Tags */}
              {questionDetails.evaluation.details && (
                <div className="space-y-2">
                  <h4 className="text-sm font-semibold text-gray-700">
                    Performance Details:
                  </h4>
                  <div className="flex flex-wrap gap-2">
                    {Object.entries(questionDetails.evaluation.details).map(
                      ([key, value]) => (
                        <span
                          key={key}
                          className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800"
                        >
                          {key
                            .replace('_', ' ')
                            .replace(/\b\w/g, (l) => l.toUpperCase())}
                          :{' '}
                          {[
                            'sentences',
                            'word_count',
                            'participation',
                          ].includes(key)
                            ? value ? value : 0
                            : `${value ? value : 0}/5`}
                        </span>
                      )
                    )}
                  </div>
                </div>
              )}

              {/* Evaluation Date */}
              {questionDetails.evaluation.evaluated_at && (
                <div className="flex flex-wrap gap-2">
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-600">
                    📅 Evaluated:{' '}
                    {new Date(
                      questionDetails.evaluation.evaluated_at
                    ).toLocaleDateString()}
                  </span>
                </div>
              )}
            </div>
          )}
        </>
      ) : (
        <p
          className={`text-red-600
          text-center mt-2`}
        >
          Not Confirmed yet
        </p>
      )}
    </div>
  );
};

export default CorrectionZone;
