'use client';
import { useState, useMemo, useCallback, useEffect } from 'react';
import { ButtonIcon } from '@/components/Button';
import useDataFetch from '@/hooks/useDataFetch';
import DiaryCover from '../_component/DiaryCover';
import DiaryContent from '../_component/DiaryContent';
import DiaryPagination from '../_component/DiaryPagination';
import DiaryIconsSidebar from '../_component/DiaryIconsSidebar';
import CalendarFilter from '../_component/modalContents/share/CalendarFilter';

export default function MyDiary() {
  // Initialize state from localStorage if available
  const [isOpen, setIsOpen] = useState(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('diary-is-open');
      return saved ? JSON.parse(saved) : false;
    }
    return false;
  });

  const [currentIndex, setCurrentIndex] = useState(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('diary-current-index');
      return saved ? parseInt(saved, 10) : 0;
    }
    return 0;
  });

  const [coverPhotoUrl, setCoverPhotoUrl] = useState(null);
  const [showCalendar, setShowCalendar] = useState(false);

  const { data, isLoading, error } = useDataFetch({
    queryKey: 'diary-entries',
    endPoint: 'diary/entries',
  });

  const entries = data?.items ?? [];
  const hasEntries = entries.length > 0;
  const isSinglePage = isOpen && currentIndex + 1 >= entries.length;

  // Persist diary state to localStorage
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('diary-is-open', JSON.stringify(isOpen));
    }
  }, [isOpen]);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('diary-current-index', currentIndex.toString());
    }
  }, [currentIndex]);

  // Validate and adjust currentIndex when entries change
  useEffect(() => {
    if (entries.length > 0 && currentIndex >= entries.length) {
      // If current index is beyond available entries, reset to last valid index
      const maxIndex = Math.max(0, entries.length - 1);
      setCurrentIndex(maxIndex);
    }
  }, [entries.length, currentIndex]);

  // Handle state restoration after data is loaded
  useEffect(() => {
    if (entries.length > 0 && !isLoading) {
      // Validate that the saved state is still valid
      const savedIsOpen = typeof window !== 'undefined' ?
        JSON.parse(localStorage.getItem('diary-is-open') || 'false') : false;
      const savedIndex = typeof window !== 'undefined' ?
        parseInt(localStorage.getItem('diary-current-index') || '0', 10) : 0;

      // Ensure the saved index is within bounds
      if (savedIndex >= entries.length) {
        setCurrentIndex(0);
        if (typeof window !== 'undefined') {
          localStorage.setItem('diary-current-index', '0');
        }
      }

      console.log('Diary state restored:', {
        isOpen: savedIsOpen,
        currentIndex: savedIndex,
        entriesCount: entries.length,
        isValidIndex: savedIndex < entries.length
      });
    }
  }, [entries.length, isLoading]);

  // Cleanup localStorage on component unmount
  useEffect(() => {
    return () => {
      // Optional: Clear localStorage on unmount if you want to reset on navigation away
      // Uncomment the lines below if you want to clear state when navigating away from the diary page
      // if (typeof window !== 'undefined') {
      //   localStorage.removeItem('diary-is-open');
      //   localStorage.removeItem('diary-current-index');
      // }
    };
  }, []);

  const openDiary = useCallback(() => {
    if (hasEntries) {
      // Only reset to 0 if this is a fresh open (not from localStorage)
      if (!isOpen) {
        setCurrentIndex(0);
      }
      setIsOpen(true);
    }
  }, [hasEntries, isOpen]);

  const closeDiary = () => {
    setIsOpen(false);
    // Clear localStorage when diary is explicitly closed
    if (typeof window !== 'undefined') {
      localStorage.removeItem('diary-is-open');
      localStorage.removeItem('diary-current-index');
    }
  };

  const goLeft = () => {
    setCurrentIndex((i) => Math.max(i - 2, 0));
  };

  const goRight = () => {
    if (!isOpen && hasEntries) {
      openDiary();
      return;
    }
    setCurrentIndex((i) => (i + 2 < entries.length ? i + 2 : i));
  };

  // Function to navigate to a specific page (useful for direct navigation)
  const goToPage = useCallback((index) => {
    if (index >= 0 && index < entries.length) {
      setCurrentIndex(index);
      if (!isOpen && hasEntries) {
        setIsOpen(true);
      }
    }
  }, [entries.length, hasEntries, isOpen]);

  const handleCoverPhotoChange = useCallback((url) => {
    setCoverPhotoUrl(url);
  }, []);

  const backgroundStyle = useMemo(
    () =>
      coverPhotoUrl
        ? {
            backgroundImage: `url(${coverPhotoUrl})`,
            backgroundSize: 'contain',
            backgroundPosition: 'center',
            backgroundRepeat: 'no-repeat',
          }
        : {},
    [coverPhotoUrl]
  );

  if (isLoading) {
    return (
      <div className="flex justify-center min-h-[calc(100vh-180px)] max-sm:min-h-screen items-center">
        <p className="text-lg">Loading diary entries...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex justify-center min-h-[calc(100vh-180px)] max-sm:min-h-screen items-center">
        <div className="text-center text-red-500">
          <p className="text-lg">Error loading diary entries</p>
          <p className="text-sm">{error.message}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex justify-center min-h-[calc(100vh-180px)] max-sm:min-h-screen p-6">
      <div
        className={`relative ${
          isOpen ? 'w-full max-w-[900px]' : 'w-full max-w-[647px]'
        } ${
          isSinglePage
            ? 'max-h-[600px] min-h-[858px]'
            : 'max-h-[600px] min-h-[658px]'
        } ${
          coverPhotoUrl ? '' : 'bg-[#FDE7E9]'
        } rounded-lg shadow-lg border bg-yellow-100 border-gray-300 transition-all duration-300`}
        style={backgroundStyle}
      >
        {isOpen ? (
          <>
            <DiaryContent entries={entries} currentIndex={currentIndex} />
            <div className="absolute lg:right-[-60px] right-0 -top-16 lg:top-4 z-10 flex flex-col max-sm:flex-row-reverse items-center">
              <ButtonIcon
                icon="mdi:close"
                innerBtnCls="h-10 w-10 cursor-pointer"
                btnIconCls="h-5 w-5"
                aria-label="close diary"
                onClick={closeDiary}
              />
              {/* Calendar button for open diary view - below close button */}
              <div className="lg:mt-2 relative">
                <ButtonIcon
                  icon="material-symbols:calendar-month-outline"
                  innerBtnCls="max-sm:h-10 max-sm:w-10 h-14 w-14 cursor-pointer"
                  btnIconCls="h-5 max-sm:h-4 w-5 max-sm:w-4"
                  aria-label="open calendar"
                  onClick={() => setShowCalendar(!showCalendar)}
                />

                {/* Calendar Filter Modal */}
                {showCalendar && (
                  <div
                    className={
                      showCalendar
                        ? 'block absolute max-w-xl min-w-80 max-h-[400px] max-sm:max-h-[300px] overflow-y-auto bg-white border rounded-lg shadow-lg z-20 mt-8 top-0 right-0'
                        : 'hidden'
                    }
                  >
                    <CalendarFilter
                      onDateSelect={() => {
                        // The CalendarFilter component will update the Redux store
                        // This callback is for any additional actions needed
                        if (fetchTodayEntry) {
                          fetchTodayEntry();
                        }
                      }}
                      onClose={() => setShowCalendar(false)}
                    />
                  </div>
                )}
              </div>
              {/* <DiaryIconsSidebar className="mt-20" showSkin={false} /> */}
            </div>
          </>
        ) : (
          <>
            <DiaryCover
              hasEntries={hasEntries}
              onOpen={openDiary}
              onCoverPhotoChange={handleCoverPhotoChange}
            />
          </>
        )}
        <DiaryPagination
          hasEntries={hasEntries}
          isOpen={isOpen}
          currentIndex={currentIndex}
          totalEntries={entries.length}
          onLeftClick={goLeft}
          onRightClick={goRight}
        />
      </div>
    </div>
  );
}
