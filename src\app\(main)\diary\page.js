'use client';

import { useEffect, useCallback, useState, useRef } from 'react';
import { format } from 'date-fns';
import { toast } from 'sonner';
import { useRouter, useSearchParams } from 'next/navigation';
import { useDispatch, useSelector } from 'react-redux';
import * as yup from 'yup';

import api from '@/lib/api';
import DiaryIconsSidebar from './_component/DiaryIconsSidebar';
import SelectSkinModal from './_component/SelectSkinModal';
import StageSelector from './_component/StageSelector';
import DiaryForm from './_component/DiaryForm';

import {
  setPreviewMode,
  updateCanvasItem,
  changeBackground,
  addImageToCanvas,
  resetCanvas,
  selectCanvasItems,
  selectCanvasBackground,
  setSelectedId,
} from '@/store/features/canvasSlice';

import {
  setSubject,
  setMessage,
  setSelectedSkin,
  setIsSaving,
  setIsLoading,
  setTodayEntry,
  setIsSkinModalOpen,
  setLayoutBackground,
  selectDiarySubject,
  selectDiaryMessage,
  selectSelectedSkin,
  selectIsSaving,
  selectIsLoading,
  selectTodayEntry,
  selectIsSkinModalOpen,
  selectLayoutBackground,
  selectIsDecorating,
  setIsTodayDiaryOpen,
  selectDecorationItems,
  setDecorationItems,
  setIsSubmittingDecoration,
  setDecorationSubmissionError,
  setDecorationSubmissionSuccess,
  resetDecorationSubmissionState,
  setIsDecorating,
  clearDecorations,
  setSelectedDecorationId,
} from '@/store/features/diarySlice';
import DiaryWithDecorations from '@/components/diary/DiaryWithDecorations';
import MessageModal from './_component/modalContents/MessageModal';
import { Icon } from '@iconify/react';
import { ButtonIcon } from '@/components/Button';
import DiarySkin from './_component/DiarySkin';

// Yup validation schema for diary form
const diaryValidationSchema = yup.object().shape({
  subject: yup
    .string()
    .trim()
    .required('Subject is required')
    .min(1, 'Subject cannot be empty'),
  message: yup
    .string()
    .trim()
    .required('Content is required')
    .min(1, 'Content cannot be empty'),
});

export default function WriteDiary() {
  const router = useRouter();
  const dispatch = useDispatch();
  const isInitialMount = useRef(true);
  const { isTodayDiaryOpen } = useSelector((state) => state.diary);

  // Add state for selected stage template ID
  const [selectedStageTemplateId, setSelectedStageTemplateId] = useState(null);
  // const [isEmojiSelectorOpen, setIsEmojiSelectorOpen] = useState(false);
  const [selectedStage, setSelectedStage] = useState(null);
  const [wordCount, setWordCount] = useState(0);
  const [nextStage, setNextStage] = useState(null);
  const { date } = useSelector((state) => state.diary);
  const canvasItems = useSelector(selectCanvasItems);
  const isDecorating = useSelector(selectIsDecorating);
  const decorationItems = useSelector(selectDecorationItems);

  // Diary state
  const subject = useSelector(selectDiarySubject);
  const message = useSelector(selectDiaryMessage);
  const selectedSkin = useSelector(selectSelectedSkin);
  const isSaving = useSelector(selectIsSaving);
  const isLoading = useSelector(selectIsLoading);
  const todayEntry = useSelector(selectTodayEntry);
  const isSkinModalOpen = useSelector(selectIsSkinModalOpen);
  const layoutBackground = useSelector(selectLayoutBackground);
  const [zoomLevel, setZoomLevel] = useState(1);
  const [isUpdating, setUpdating] = useState(false);
  const [hasTutorGreeting, setHasTutorGreeting] = useState(
    todayEntry?.hasGreeting === false
  );
  const [validationErrors, setValidationErrors] = useState({});
  const [isEdit, setIsEdit] = useState(false);
  const [showSideIcons, setShowSideIcons] = useState(false);
  // Selectors
  const today = format(new Date(), 'dd MMM yyyy');

  // On mount, check localStorage
  // useEffect(() => {
  //   const diaryStatus = localStorage.getItem('todaysDiaryOpen');
  //   if (diaryStatus === 'true') {
  //     setIsOpen(true);
  //   }
  // }, []);

  // Function to open diary
  const openDiary = () => {
    // localStorage.setItem('todaysDiaryOpen', 'true');
    dispatch(setIsTodayDiaryOpen(true));
  };

  // Function to close diary
  const closeDiary = () => {
    // localStorage.removeItem('todaysDiaryOpen');
    dispatch(setIsTodayDiaryOpen(false));
  };

  // Handle responsive sidebar behavior
  useEffect(() => {
    const handleResize = () => {
      const isDesktop = window.innerWidth >= 1280; // xl breakpoint
      if (isDesktop) {
        setShowSideIcons(true); // Always show sidebar on desktop
      } else {
        setShowSideIcons(false); // Hide sidebar on mobile/tablet by default
      }
    };

    // Initial check
    handleResize();

    // Add event listener
    window.addEventListener('resize', handleResize);
    // Cleanup
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Clear validation errors when user starts typing
  useEffect(() => {
    if (validationErrors.subject && subject.trim()) {
      setValidationErrors((prev) => ({ ...prev, subject: undefined }));
    }
  }, [subject, validationErrors.subject]);

  useEffect(() => {
    if (validationErrors.message && message.trim()) {
      setValidationErrors((prev) => ({ ...prev, message: undefined }));
    }
  }, [message, validationErrors.message]);

  // Function to count words in a string
  const countWords = (text) => {
    if (!text || text.trim() === '') return 0;
    return text.trim().split(/\s+/).length;
  };

  const handleApplySkin = (newSkin) => {
    if (!newSkin.templateContent) {
      toast.error('This skin has no valid template content');
      return;
    }
    dispatch(setSelectedSkin(newSkin));
    dispatch(setIsSkinModalOpen(false));
    toast.success(`Skin "${newSkin.name}" applied successfully`);
  };

  // Detect browser zoom level
  useEffect(() => {
    const detectZoom = () => {
      const zoom =
        Math.round((window.outerWidth / window.innerWidth) * 100) / 100;
      setZoomLevel(zoom);
    };

    detectZoom();
    window.addEventListener('resize', detectZoom);

    return () => window.removeEventListener('resize', detectZoom);
  }, []);

  // Handle stage change
  const handleStageChange = useCallback(
    async (stage) => {
      if (!todayEntry?.id) {
        console.error(
          'Cannot change stage: todayEntry.id is not available',
          todayEntry
        );
        return;
      }

      try {
        const response = await api.patch(
          `/diary/entries/${todayEntry.id}/settings`,
          {
            settingsTemplateId: stage.id,
          },
          { showSuccessToast: false }
        );

        console.log('Stage change successful:', response);
        setSelectedStageTemplateId(stage.id);
        setSelectedStage(stage);

        // Update word count when stage changes
        setWordCount(countWords(message));
      } catch (error) {
        console.error('Error changing stage:', error);
      }
    },
    [todayEntry, message]
  );

  // Background color listener
  useEffect(() => {
    const storedColor = localStorage.getItem('diaryLayoutBackground');
    if (storedColor) {
      dispatch(setLayoutBackground(storedColor));
    }

    const handleBackgroundChange = (event) => {
      dispatch(setLayoutBackground(event.detail));
    };

    window.addEventListener('layoutBackgroundChanged', handleBackgroundChange);
    return () =>
      window.removeEventListener(
        'layoutBackgroundChanged',
        handleBackgroundChange
      );
  }, [dispatch]);

  // Enable preview mode on mount
  useEffect(() => {
    dispatch(setPreviewMode(true));
  }, [dispatch]);

  // Reset decoration state when component unmounts or when navigating away
  useEffect(() => {
    return () => {
      // Cleanup decoration state when leaving WriteDiary component
      dispatch(setIsDecorating(false));
      dispatch(setSelectedDecorationId(null));
      console.log('WriteDiary component unmounting - resetting decoration state');
    };
  }, [dispatch]);

  // Initialize component on mount
  useEffect(() => {
    console.log('Component mounted, initializing...');
    if (isInitialMount.current) {
      dispatch(setIsLoading(true));
    }
  }, [dispatch]);

  // Update word count whenever message changes
  useEffect(() => {
    setWordCount(countWords(message));
  }, [message]);

  // Reactive effect to apply skin template when selectedSkin changes
  useEffect(() => {
    if (!selectedSkin || !selectedSkin.templateContent) return;

    // For subsequent skin changes by the user, preserve current content
    const contentToPreserve = {
      subject: isInitialMount.current ? todayEntry?.title || '' : subject,
      message: isInitialMount.current ? todayEntry?.content || '' : message,
    };

    try {
      dispatch(resetCanvas());
      const templateData = JSON.parse(selectedSkin.templateContent);

      if (templateData.background) {
        dispatch(changeBackground(templateData.background));
      }

      if (templateData.items?.length) {
        templateData.items.forEach((item) => {
          if (item.type === 'text') {
            let content = item.content;
            let itemId = item.id;

            if (item.id === 'subject' || item.id.startsWith('subject')) {
              itemId = 'subject';
              content = contentToPreserve.subject || item.content;
            } else if (item.id === 'body' || item.id.startsWith('body')) {
              itemId = 'body';
              content = contentToPreserve.message || item.content;
            } else if (item.id === 'date' || item.id.startsWith('date')) {
              itemId = 'date';
              content = format(new Date(), item.dateFormat || 'dd MMM yyyy');
            }

            dispatch({
              type: 'canvas/addTextItem',
              payload: {
                ...item,
                id: itemId,
                content,
                styles: {
                  ...item.styles,
                  width: item.styles?.width || 300,
                  height: item.styles?.height || 40,
                  x: item.styles?.x || 50,
                  y: item.styles?.y || 20,
                },
              },
            });
          } else if (item.type === 'image') {
            dispatch(
              addImageToCanvas({
                id: `${item.id}-${Date.now()}`,
                imageSrc: item.image,
                styles: item.styles,
                zIndex: item.zIndex || 1,
              })
            );
          }
        });
      }

      dispatch(setSelectedId(null));
      dispatch(setPreviewMode(false));
      setTimeout(() => dispatch(setPreviewMode(true)), 50);
    } catch (error) {
      console.error('Error applying skin template:', error);
      // toast.error('Failed to apply skin template');
    }
  }, [selectedSkin, dispatch, subject, message, todayEntry]); // Only re-run when skin changes

  // Listen for the openGreetingModal event
  useEffect(() => {
    setHasTutorGreeting(todayEntry?.hasGreeting);
  }, [todayEntry]);

  // Listen for the greetingSent event
  useEffect(() => {
    const handleGreetingSent = async (event) => {
      if (event.detail?.success) {
        // Refresh the diary entry data
        dispatch(setIsLoading(true));
      }
    };

    window.addEventListener('greetingSent', handleGreetingSent);
    return () => {
      window.removeEventListener('greetingSent', handleGreetingSent);
    };
  }, [dispatch]);

  // Initialize selected stage from API data when selectedStageTemplateId changes
  useEffect(() => {
    const fetchStageData = async () => {
      if (!selectedStageTemplateId) return;

      try {
        console.log('Fetching settings for stage:', selectedStageTemplateId);
        const response = await api.get('/diary/settings');
        if (response.success && response.data?.items) {
          const stage = response.data.items.find(
            (item) => item.id === selectedStageTemplateId
          );
          if (stage) {
            setSelectedStage(stage);
            console.log('Selected stage set:', stage);

            // Set the next stage during initialization using level field
            if (stage.level !== undefined) {
              const nextStageItem = response.data.items.find(
                (item) => item.level === stage.level + 1
              );
              setNextStage(nextStageItem || null);
              console.log('Next stage set:', nextStageItem);
            } else {
              setNextStage(null);
            }
          }
        }
      } catch (error) {
        console.error('Error fetching stage data:', error);
      }
    };

    fetchStageData();
  }, [selectedStageTemplateId]);

  // Update canvas when form values change
  useEffect(() => {
    if (isLoading || isInitialMount.current) return;

    const subjectItem = canvasItems.find((item) => item.id === 'subject');
    if (subjectItem && subjectItem.content !== subject) {
      dispatch(
        updateCanvasItem({ id: 'subject', updates: { content: subject } })
      );
    }

    const bodyItem = canvasItems.find((item) => item.id === 'body');
    if (bodyItem && bodyItem.content !== message) {
      dispatch(updateCanvasItem({ id: 'body', updates: { content: message } }));
    }
  }, [subject, message, isLoading, dispatch, canvasItems]);

  // Fetch today's entry or specific entry
  const fetchTodayEntry = useCallback(async () => {
    console.log('fetchTodayEntry called');
    try {
      const endpoint = '/diary/entries/today';

      const response = await api.get(endpoint);

      if (response.success && response.data) {
        const { data: diaryData } = response;

        dispatch(setTodayEntry(diaryData));
        dispatch(setSubject(diaryData.title || ''));
        dispatch(setMessage(diaryData.content || ''));

        // Load decorations if they exist
        if (diaryData.decoration) {
          try {
            const decorations = JSON.parse(diaryData.decoration);
            dispatch(setDecorationItems(decorations));
            console.log('Loaded decorations from API:', decorations);
          } catch (error) {
            console.error('Error parsing decoration data:', error);
            dispatch(setDecorationItems([]));
          }
        } else {
          dispatch(setDecorationItems([]));
        }

        // console.log('Diary data from API:', diaryData);

        if (diaryData.settings?.settingsTemplateId) {
          setSelectedStageTemplateId(diaryData.settings.settingsTemplateId);
        }

        if (diaryData.skin) {
          dispatch(setSelectedSkin(diaryData.skin));
        } else {
          router.push('/select-skin');
        }
      } else if (!selectedSkin) {
        router.push('/select-skin');
      }
    } catch (error) {
      console.error('Error fetching diary entry:', error);
    } finally {
      dispatch(setIsLoading(false));
      isInitialMount.current = false;
    }
  }, [dispatch, router, selectedSkin]);

  // Only auto-fetch on load if loading is true
  useEffect(() => {
    if (isLoading) {
      fetchTodayEntry();
    }
  }, [isLoading, fetchTodayEntry]);

  useEffect(() => {
    if (todayEntry?.content?.length > 0 || todayEntry?.title?.length > 0) {
      setIsEdit(false);
    } else if (todayEntry?.status === 'new') {
      setIsEdit(true);
    }
  }, [todayEntry]);

  // Handle save
  const handleSave = useCallback(async () => {
    // Clear previous validation errors
    setValidationErrors({});

    // Validate form data using Yup schema
    try {
      await diaryValidationSchema.validate(
        { subject, message },
        { abortEarly: false }
      );
    } catch (validationError) {
      // Handle validation errors - set them in state instead of showing toast
      const errors = {};
      if (validationError.inner && validationError.inner.length > 0) {
        validationError.inner.forEach((error) => {
          errors[error.path] = error.message;
        });
      } else if (validationError.path) {
        errors[validationError.path] = validationError.message;
      }
      setValidationErrors(errors);
      return;
    }

    dispatch(setIsSaving(true));
    try {
      const payload = {
        title: subject,
        content: message,
        skinId: selectedSkin?.id || null,
        backgroundColor: layoutBackground,
        isPrivate: false,
        settingsTemplateId: selectedStage?.id || selectedStageTemplateId, // Include the selected stage ID
        // entryId: todayEntry?.id || null,
      };

      let apiCall;

      apiCall = api.post(`/diary/entries/${todayEntry?.id}/submit`, payload);
      // Check if entry exists
      // if (todayEntry?.id) {
      //   // Check if status is "new" - if so, use the submit endpoint
      // } else {
      //   // If no entry exists yet, create a new one
      //   apiCall = api.post('/diary/entries', {
      //     ...payload,
      //     entryDate: format(new Date(), 'yyyy-MM-dd'),
      //   });
      // }

      const response = await apiCall;

      if (response.success) {
        fetchTodayEntry();
        dispatch(setTodayEntry(response.data));
        setIsEdit(false);
        // Show appropriate success message based on the action performed
        if (todayEntry?.status === 'new' && todayEntry?.id) {
          // toast.success('Diary submitted successfully!');
        } else {
          // toast.success('Diary saved successfully!');
        }
      }
    } catch (error) {
      console.error('Error saving diary entry:', error);
      // toast.error('Failed to save diary entry. Please try again.');
    } finally {
      dispatch(setIsSaving(false));
    }
  }, [
    subject,
    message,
    selectedSkin,
    layoutBackground,
    todayEntry,
    dispatch,
    selectedStageTemplateId,
  ]);

  // Handle save as draft
  const handleSaveAsDraft = useCallback(async () => {
    if (!isTodayDiaryOpen) return;

    // Clear previous validation errors
    setValidationErrors({});

    // Basic validation - only check if fields are not empty
    // if (!subject.trim() || !message.trim()) {
    //   setValidationErrors({
    //     subject: !subject.trim() ? 'Subject is required' : '',
    //     message: !message.trim() ? 'Content is required' : '',
    //   });
    //   return;
    // }

    setUpdating(true);

    try {
      const payload = {
        title: subject,
        content: message,
        skinId: selectedSkin?.id || null,
        backgroundColor: layoutBackground,
        isPrivate: false,
        settingsTemplateId: selectedStageTemplateId,
      };

      let apiCall;

      // Always use patch for draft saves if entry exists
      if (todayEntry?.id) {
        apiCall = api.patch(`/diary/entries/${todayEntry.id}`, payload, {
          showSuccessToast: false,
        });
      } else {
        // If no entry exists yet, create a new one as draft
        apiCall = api.post(
          '/diary/entries',
          {
            ...payload,
            entryDate: format(new Date(today), 'yyyy-MM-dd'),
          },
          { showSuccessToast: false }
        );
      }

      const response = await apiCall;

      // if (response.success) {
      //   dispatch(setTodayEntry(response.data));
      //   toast.success('Draft saved successfully!');
      // }
    } catch (error) {
      console.error('Error saving draft:', error);
    } finally {
      setUpdating(false);
    }
  }, [
    subject,
    message,
    selectedSkin,
    layoutBackground,
    todayEntry,
    dispatch,
    selectedStageTemplateId,
  ]);

  // Handle decoration submission
  const handleSubmitDecoration = useCallback(async () => {
    if (!todayEntry?.id) {
      console.error('No diary entry ID available for decoration submission');
      return;
    }

    dispatch(setIsSubmittingDecoration(true));
    dispatch(resetDecorationSubmissionState());

    try {
      // Format decoration data as JSON string
      const decorationData = JSON.stringify(decorationItems);

      const response = await api.patch(
        `/diary/entries/${todayEntry.id}/decoration`,
        { decoration: decorationData }
      );

      if (response.success) {
        dispatch(setDecorationSubmissionSuccess(true));
        // toast.success('Decoration submitted successfully!');
        console.log('Decoration submitted successfully:', response.data);
      } else {
        throw new Error(response.message || 'Failed to submit decoration');
      }
    } catch (error) {
      console.error('Error submitting decoration:', error);
      dispatch(setDecorationSubmissionError(error.message || 'Failed to submit decoration'));
      // toast.error('Failed to submit decoration. Please try again.');
    } finally {
      dispatch(setIsSubmittingDecoration(false));
    }
  }, [todayEntry?.id, decorationItems, dispatch]);

  // Debounced auto-save as draft when user types
  useEffect(() => {
    // if (
    //   todayEntry?.status !== 'submit'
    // )
    //   return;
    // if (!subject.trim() && !message.trim()) return;

    const handler = setTimeout(() => {
      handleSaveAsDraft();
    }, 800); // 2 seconds debounce

    return () => {
      clearTimeout(handler);
    };
  }, [
    subject,
    message,
    selectedSkin,
    layoutBackground,
    selectedStageTemplateId,
  ]);

  // Simple cover page component
  const CoverPage = () => (
    <div className="flex justify-center min-h-[calc(100vh-180px)] transition-all duration-500 ease-in-out">
      <div className="relative w-full max-w-[647px] h-[658px] bg-yellow-100 rounded-lg shadow-lg border border-gray-300 transition-all duration-300">
        <div className="absolute left-0 top-0 bottom-0 w-[60px] bg-[#1E3A8A] rounded-l-lg" />
        <div
          className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 bg-white/30 backdrop-blur-lg rounded-2xl shadow-xl px-6 sm:px-12 py-6 max-sm:ml-6 text-center min-w-[200px] border border-white/40 ring-1 ring-black/5 cursor-pointer transition-all duration-300 hover:bg-white/40 hover:scale-105 hover:shadow-2xl"
          onClick={openDiary}
        >
          <h1 className="text-2xl max-sm:text-xl font-bold text-[#1E3A8A] mb-2">
            Write Today's Diary
          </h1>
          <div className="mt-3 text-xs text-gray-600">
            {format(new Date(), 'EEEE, MMMM do, yyyy')}
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <>
      <div className="flex justify-center items-start min-h-[calc(100vh-180px)] p-6">
        <div
          className={`relative max-h-[658px] ${
            isTodayDiaryOpen ? 'w-full max-w-[1280px]' : 'w-full max-w-[648px]'
          } transition-all duration-300`}
        >
          {isTodayDiaryOpen ? (
            <>
              <div
                className={`absolute left-1/2 -translate-x-1/2 max-sm:-top-8 top-0 bg-gradient-to-b from-yellow-300 via-yellow-400 to-yellow-500 px-4 pr-8 py-1 rounded-full`}
              >
                <DiaryIconsSidebar
                  isOpen={showSideIcons}
                  onClose={() => setShowSideIcons(false)}
                  isEmojiSelectorOpen={isDecorating}
                />
              </div>
              <div
                className={`container border border-gray-300 rounded-lg ${
                  isDecorating ? 'mt-[350px]' : 'mt-10 sm:mt-20'
                }  shadow-lg ${
                  isTodayDiaryOpen ? '' : 'min-h-[500px]'
                } bg-pink-100 p-2 mx-auto`}
              >
                {isLoading ? (
                  <div className="flex items-center justify-center h-[500px]">
                    <div className="text-center">
                      <div className="inline-block animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-yellow-400 mb-4"></div>
                      <h2 className="text-xl font-semibold">
                        Loading your diary...
                      </h2>
                    </div>
                  </div>
                ) : (
                  <>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2 items-center relative">
                      <DiarySkin
                        today={today}
                        todayEntry={todayEntry}
                        subject={subject}
                        message={message}
                        selectedSkin={selectedSkin}
                        handleStageChange={handleStageChange}
                        selectedStageTemplateId={selectedStageTemplateId}
                        onSubmitDecoration={handleSubmitDecoration}
                        isDecorating={isDecorating}
                      />

                      <div className="bg-white h-full p-2 overflow-hidden shadow-xl">
                        <DiaryForm
                          today={today}
                          todayEntry={todayEntry}
                          subject={subject}
                          message={message}
                          wordCount={wordCount}
                          selectedStage={selectedStage}
                          validationErrors={validationErrors}
                          nextStage={nextStage}
                          handleStageChange={handleStageChange}
                          isEditable={isEdit}
                          setIsEdit={setIsEdit}
                          isEdit={isEdit}
                          handleSave={handleSave}
                          isSaving={isSaving}
                          setIsMessageModalOpen={setHasTutorGreeting}
                        />
                      </div>

                      <div className="absolute -right-3 -top-14 2xl:-right-14 2xl:-top-3">
                        <ButtonIcon
                          icon="mdi:close"
                          innerBtnCls="h-10 w-10 cursor-pointer"
                          btnIconCls="h-5 w-5"
                          aria-label="close diary"
                          onClick={closeDiary}
                        />
                      </div>
                    </div>
                  </>
                )}

                {/* <div className="flex justify-between items-center mb-4 absolute -top-16 right-2 z-10">
                  <ButtonIcon
                    icon={'iconamoon:menu-kebab-vertical-bold'}
                    innerBtnCls="h-10 w-10"
                    btnIconCls="h-5 w-5"
                    aria-label={'Skin'}
                    onClick={() => setShowSideIcons(!showSideIcons)}
                  />
                </div> */}

                <SelectSkinModal
                  isOpen={isSkinModalOpen}
                  onClose={() => dispatch(setIsSkinModalOpen(false))}
                  onApply={handleApplySkin}
                  currentSkinId={selectedSkin?.id}
                />
                <MessageModal
                  isOpen={todayEntry && !hasTutorGreeting}
                  onClose={() => setHasTutorGreeting(true)}
                />
              </div>
            </>
          ) : (
            <CoverPage />
          )}
        </div>
      </div>
    </>
  );
}
