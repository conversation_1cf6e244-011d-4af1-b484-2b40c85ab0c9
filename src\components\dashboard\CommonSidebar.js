'use client';
import React, { useState } from 'react';
import { Icon } from '@iconify/react';
import { motion } from 'framer-motion';
import { usePathname, useRouter } from 'next/navigation';
import Link from 'next/link';
import RegularGoBack from '../shared/RegularGoBack';

const CommonSidebar = ({ data, children, title }) => {
  const router = useRouter();
  const pathname = usePathname();
  const [expandedItems, setExpandedItems] = useState({});

  const toggleExpand = (index) => {
    setExpandedItems((prev) => ({
      ...prev,
      [index]: !prev[index],
    }));
  };

  const isPathActive = (path) =>  pathname === path || pathname.startsWith(`${path}/`);

  const isSubmenuActive = (children) =>
    children?.some((child) => isPathActive(child.path));

  return (
    <div className="flex flex-col min-h-[calc(100vh-180px)]">
      <RegularGoBack className={'pb-5 max-w-32'} title={title} />

      <div className="flex flex-1">
        <div className="w-64 bg-[#FEFCE8] p-4 flex flex-col rounded-lg overflow-y-auto border border-yellow-300">
          {data.map((item, index) => (
            <div key={item.value} className="mb-2">
              <button
                className={`p-2 w-full px-3 text-left rounded-lg flex items-center justify-between ${
                  isPathActive(item.path) || isSubmenuActive(item.children)
                    ? 'bg-[#FFDE34] text-black'
                    : 'hover:bg-yellow-200'
                }`}
                onClick={() => {
                  if (item.children && item.children.length > 0) {
                    toggleExpand(index);
                  } else {
                    router.push(item.path);
                  }
                }}
              >
                <div className="flex items-center">
                  <Icon icon={item.icon} className="w-5 h-5 mr-2" />
                  <span>{item.name}</span>
                </div>
                {item.children && item.children.length > 0 && (
                  <Icon
                    icon={
                      expandedItems[index]
                        ? 'lucide:chevron-down'
                        : 'lucide:chevron-right'
                    }
                    className="w-5 h-5"
                  />
                )}
              </button>

              {item.children && item.children.length > 0 && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{
                    opacity: expandedItems[index] ? 1 : 0,
                    height: expandedItems[index] ? 'auto' : 0,
                  }}
                  transition={{
                    duration: 0.3,
                    ease: 'easeInOut',
                  }}
                  className="bg-white mt-1 rounded-lg overflow-hidden"
                >
                  {item.children.map((child, childIndex) => (
                    <motion.div
                      key={child.value}
                      initial={{ x: -20, opacity: 0 }}
                      animate={{ x: 0, opacity: 1 }}
                      transition={{
                        duration: 0.2,
                        delay: childIndex * 0.05,
                      }}
                    >
                      <Link
                        href={child.path}
                        className={`block p-2 px-3 w-full text-left rounded-lg flex items-center ${
                          (pathname === child.path || pathname.startsWith(`${child.path}/`))
                            ? 'text-yellow-600'
                            : 'hover:bg-gray-100 text-gray-700'
                        }`}
                      >
                        <Icon icon={'ei:plus'} className="w-4 h-4 mr-2" />
                        <span>{child.name}</span>
                      </Link>
                    </motion.div>
                  ))}
                </motion.div>
              )}
            </div>
          ))}
        </div>
        <div className="flex-1 p-6 max-h-[calc(100vh-180px)] overflow-y-auto">{children}</div>
      </div>
    </div>
  );
};

export default CommonSidebar;
