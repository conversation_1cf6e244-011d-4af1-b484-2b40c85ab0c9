import { removeAuthTokenCookie } from './auth';
import { toast } from 'sonner';

/**
 * Handle automatic logout when token expires
 * This function is called from the API interceptor when a 401 response is received
 */
export const handleTokenExpiration = () => {
  // Clear authentication data
  removeAuthTokenCookie();
  
  // Clear persisted Redux state and other localStorage items
  if (typeof window !== 'undefined') {
    // Clear localStorage items that might contain auth data
    localStorage.removeItem('persist:auth');
    localStorage.removeItem('userId');
    localStorage.removeItem('password');
    localStorage.removeItem('selectedRole');
    
    // Show token expiration message
    toast.error('Your session has expired. Please login again.');
    
    // Redirect to login page
    window.location.href = '/login';
  }
};

/**
 * Check if the current error is a token expiration error
 * @param {Object} error - The error object from axios
 * @returns {boolean} - True if it's a token expiration error
 */
export const isTokenExpiredError = (error) => {
  return error?.response?.status === 401;
};
