'use client';

import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import useDataFetch from '@/hooks/useDataFetch';
import { formatDate } from '@/utils/dateFormatter';
import Image from 'next/image';

/**
 * HistoryModal component for displaying diary entry edit history
 * @param {Object} props - Component props
 * @param {boolean} props.isOpen - Whether the modal is open
 * @param {Function} props.onClose - Function to close the modal
 * @param {string} props.entryId - The diary entry ID to fetch history for
 */
const HistoryModal = ({ isOpen, onClose, entryId }) => {
  const {
    data: historyData,
    isLoading,
    error,
  } = useDataFetch({
    queryKey: ['diary-history', entryId],
    endPoint: `/diary/entries/${entryId}/history`,
    enabled: isOpen && !!entryId,
  });



  // Handle modal close
  const handleClose = () => {
    onClose();
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onClick={handleClose}
        >
          <motion.div
            className="bg-white rounded-xl shadow-xl w-full max-w-2xl max-h-[80vh] overflow-hidden relative"
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            transition={{ type: 'spring', damping: 20, stiffness: 300 }}
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header */}
            <div className="bg-[#FFF9FB] pt-4 pb-4 px-6 border-b">
              <h2 className="text-xl font-semibold text-gray-800">Edited History</h2>
            </div>

            {/* Modal content */}
            <div className="p-6 overflow-y-auto flex-1 max-h-[60vh]">
              {isLoading ? (
                <div className="flex justify-center items-center h-32">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
                </div>
              ) : error ? (
                <div className="text-red-500 p-4 text-center">
                  Failed to load history. Please try again.
                </div>
              ) : historyData && historyData.versions && historyData.versions.length > 0 ? (
                <div className="space-y-4">
                  {historyData.versions.map((version, index) => (
                    <div
                      key={version.id || index}
                      className="border border-gray-200 rounded-lg p-4 bg-gray-50"
                    >
                      {/* Header with title and date */}
                      <div className="flex justify-between items-start mb-3">
                        <h3 className="font-semibold text-lg text-gray-800">
                          {version.title || 'A picnic journey'}
                        </h3>
                        <div className="text-sm text-gray-500">
                          Date: {formatDate(version.createdAt || version.updatedAt, 'ordinal')}
                        </div>
                      </div>

                      {/* Content */}
                      <div className="text-gray-700 text-sm leading-relaxed mb-3">
                        {version.content || 'I went to school by bus. Today was a fun day. I met my Friends and went to the park. We play games and had a picnic'}
                      </div>

                      {/* Status indicator */}
                      <div className="flex items-center">
                        <div className="flex items-center">
                          <div className={`w-3 h-3 rounded-full mr-2 ${
                            version.isLatest || index === 0
                              ? 'bg-green-500'
                              : 'bg-gray-400'
                          }`}></div>
                          <span className="text-sm text-green-600 font-medium">
                            Make this visible as shared skin
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : error ? (
                // Show mock data when API fails (for demonstration purposes)
                <div className="space-y-4">
                  {[
                    {
                      id: 1,
                      title: 'A picnic journey',
                      content: 'I went to school by bus. Today was a fun day. I met my Friends and went to the park. We play games and had a picnic',
                      createdAt: '2025-03-09',
                      isLatest: true
                    },
                    {
                      id: 2,
                      title: 'A picnic journey',
                      content: 'I went to school by bus. Today was a fun day. I met my Friends and went to the park. We play games and had a picnic',
                      createdAt: '2025-03-09',
                      isLatest: false
                    },
                    {
                      id: 3,
                      title: 'A picnic journey',
                      content: 'I went to school by bus. Today was a fun day. I met my Friends and went to the park. We play games and had a picnic',
                      createdAt: '2025-03-09',
                      isLatest: false
                    }
                  ].map((version, index) => (
                    <div
                      key={version.id}
                      className="border border-gray-200 rounded-lg p-4 bg-gray-50"
                    >
                      {/* Header with title and date */}
                      <div className="flex justify-between items-start mb-3">
                        <h3 className="font-semibold text-lg text-gray-800">
                          {version.title}
                        </h3>
                        <div className="text-sm text-gray-500">
                          Date: 9th March,25
                        </div>
                      </div>

                      {/* Content */}
                      <div className="text-gray-700 text-sm leading-relaxed mb-3">
                        {version.content}
                      </div>

                      {/* Status indicator */}
                      <div className="flex items-center">
                        <div className="flex items-center">
                          <div className={`w-3 h-3 rounded-full mr-2 ${
                            index === 0 ? 'bg-green-500' : 'bg-gray-400'
                          }`}></div>
                          <span className="text-sm text-green-600 font-medium">
                            Make this visible as shared skin
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-gray-500 italic">No edit history available for this entry.</p>
                </div>
              )}
            </div>

            {/* Close button */}
            <button
              className="absolute top-1 right-1 w-8 h-8 hover:opacity-80 transition-opacity"
              onClick={handleClose}
              aria-label="Close history modal"
            >
              <Image
                src="/assets/images/all-img/cross-bg.png"
                alt="Close"
                width={30}
                height={30}
                className="w-full h-full object-contain"
              />
            </button>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default HistoryModal;
