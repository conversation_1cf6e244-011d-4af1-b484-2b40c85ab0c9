'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import api from '@/lib/api';

const AssignTask = () => {
  const router = useRouter();
  const [tutor, setTutor] = useState({
    id: '',
    name: '',
    email: '',
    phoneNumber: '',
    bio: ''
  });
  
  const [availableRoles, setAvailableRoles] = useState([]);
  const [selectedRoles, setSelectedRoles] = useState([]);
  const [loading, setLoading] = useState(false);
  const [rolesLoading, setRolesLoading] = useState(true);
  const [assignmentResults, setAssignmentResults] = useState([]);
  const [showResults, setShowResults] = useState(false);
  
  // Get tutor data from localStorage on component mount
  useEffect(() => {
    const storedTutor = localStorage.getItem('selectedTutor');
    if (storedTutor) {
      const parsedTutor = JSON.parse(storedTutor);
      console.log('Parsed tutor data:', parsedTutor);
      setTutor(parsedTutor);
    } else {
      console.error("No tutor data found in localStorage");
    }
  }, []);

  // Set available roles directly based on API documentation
  useEffect(() => {
    const setRoles = () => {
      try {
        setRolesLoading(true);
        const roles = [
          { id: 'admin', name: 'admin', description: 'Administrator with full access', icon: '👑' },
          // { id: 'tutor', name: 'tutor', description: 'Tutor with teaching capabilities', icon: '📚' },
          { id: 'student', name: 'student', description: 'Student with learning access', icon: '🎓' }
        ];
        setAvailableRoles(roles);
      } catch (error) {
        console.error('Error setting roles:', error);
      } finally {
        setRolesLoading(false);
      }
    };

    setRoles();
  }, []);
  
  // Function to handle role selection
  const handleRoleToggle = (roleId) => {
    if (selectedRoles.includes(roleId)) {
      setSelectedRoles(selectedRoles.filter(id => id !== roleId));
    } else {
      setSelectedRoles([...selectedRoles, roleId]);
    }
  };
  
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (selectedRoles.length === 0) {
      alert('Please select at least one role to assign.');
      return;
    }

    setLoading(true);
    setAssignmentResults([]);
    setShowResults(false);
    
    try {
      const assignmentPromises = selectedRoles.map(async (roleId) => {
        const roleName = availableRoles.find(role => role.id === roleId)?.name || roleId;
        console.log(`Assigning role ${roleName} to user ${tutor.userId}`);
        
        try {
          const response = await api.post(`/users/admin/assign-role/${tutor.userId}/${roleName}`);
          
          return {
            roleId,
            roleName,
            success: true,
            message: `Role "${roleName}" assigned successfully`,
            data: response.data
          };
        } catch (error) {
          console.error(`Error assigning role ${roleName}:`, error);
          
          if (error.response && error.response.data) {
            const errorData = error.response.data;
            
            if (errorData.statusCode === 409 && errorData.message?.includes('already has role')) {
              return {
                roleId,
                roleName,
                success: false,
                alreadyAssigned: true,
                message: errorData.message || `User already has role "${roleName}"`,
                data: errorData
              };
            } else {
              return {
                roleId,
                roleName,
                success: false,
                alreadyAssigned: false,
                message: errorData.message || `Failed to assign role "${roleName}"`,
                data: errorData
              };
            }
          } else {
            return {
              roleId,
              roleName,
              success: false,
              alreadyAssigned: false,
              message: `Failed to assign role "${roleName}": ${error.message}`,
              data: null
            };
          }
        }
      });

      const results = await Promise.all(assignmentPromises);
      setAssignmentResults(results);
      setShowResults(true);
      
      const allSuccessfulOrAssigned = results.every(result => result.success || result.alreadyAssigned);
      
      if (allSuccessfulOrAssigned) {
        localStorage.removeItem('selectedTutor');
        setTimeout(() => {
          router.back();
        }, 3000);
      }
      
    } catch (error) {
      console.error('Unexpected error during role assignment:', error);
      alert('An unexpected error occurred. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const getResultStyling = (result) => {
    if (result.success) {
      return {
        bgColor: 'bg-green-50',
        borderColor: 'border-green-200',
        textColor: 'text-green-800',
        icon: '✅',
        iconBg: 'bg-green-100'
      };
    } else if (result.alreadyAssigned) {
      return {
        bgColor: 'bg-yellow-50',
        borderColor: 'border-yellow-300',
        textColor: 'text-yellow-800',
        icon: '⚠️',
        iconBg: 'bg-yellow-100'
      };
    } else {
      return {
        bgColor: 'bg-red-50',
        borderColor: 'border-red-200',
        textColor: 'text-red-800',
        icon: '❌',
        iconBg: 'bg-red-100'
      };
    }
  };
  
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="px-6 py-8 max-w-6xl mx-auto">
        {/* Header */}
        <div className="flex items-center mb-8">
          <button 
            onClick={() => router.back()} 
            className="group mr-6 p-3 rounded-lg bg-white border border-gray-200 shadow-sm hover:shadow-md transition-all duration-200 hover:bg-gray-50"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-gray-600 group-hover:text-gray-800 transition-colors">
              <path d="M19 12H5M12 19l-7-7 7-7" />
            </svg>
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Role Assignment</h1>
            <p className="text-gray-600 mt-1">Manage user permissions and access levels</p>
          </div>
        </div>
        
        {/* Main Content Card */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          {/* Header Section */}
          <div className="bg-gradient-to-r from-yellow-100 to-yellow-200 px-8 py-6">
            <div className="flex items-center space-x-4">
              <div className="p-3 bg-white/20 rounded-lg backdrop-blur-sm">
                <svg className="w-8 h-8 text-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                </svg>
              </div>
              <div>
                <h2 className="text-xl font-bold text-black">Assign New Role</h2>
                <p className="text-black">Grant access permissions to user</p>
              </div>
            </div>
          </div>

          <div className="p-8">
            {/* User Information Card */}
            <div className="mb-8">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <div className="w-1 h-6 bg-yellow-400 rounded-full mr-3"></div>
                User Information
              </h3>
              <div className="bg-gray-50 rounded-lg p-6 border border-gray-200">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  <div>
                    <label className="text-sm font-medium text-gray-600">User ID</label>
                    <p className="text-gray-900 font-semibold mt-1">{tutor.userId}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Full Name</label>
                    <p className="text-gray-900 font-semibold mt-1">{tutor.name}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Email Address</label>
                    <p className="text-gray-900 font-semibold mt-1">{tutor.email}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Phone Number</label>
                    <p className="text-gray-900 font-semibold mt-1">{tutor.phoneNumber || 'N/A'}</p>
                  </div>
                </div>
                {tutor.bio && (
                  <div className="mt-6 pt-6 border-t border-gray-200">
                    <label className="text-sm font-medium text-gray-600">Bio</label>
                    <p className="text-gray-700 mt-1">{tutor.bio}</p>
                  </div>
                )}
              </div>
            </div>

            {/* Assignment Results */}
            {showResults && assignmentResults.length > 0 && (
              <div className="mb-8">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <div className="w-1 h-6 bg-yellow-400 rounded-full mr-3"></div>
                  Assignment Results
                </h3>
                <div className="space-y-4">
                  {assignmentResults.map((result, index) => {
                    const styling = getResultStyling(result);
                    return (
                      <div 
                        key={index} 
                        className={`p-6 rounded-lg border ${styling.bgColor} ${styling.borderColor}`}
                      >
                        <div className="flex items-start space-x-4">
                          <div className={`flex-shrink-0 w-10 h-10 ${styling.iconBg} rounded-lg flex items-center justify-center`}>
                            <span className="text-lg">{styling.icon}</span>
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center space-x-2 mb-2">
                              <h4 className={`font-semibold ${styling.textColor}`}>
                                {result.roleName.charAt(0).toUpperCase() + result.roleName.slice(1)} Role
                              </h4>
                              {result.success && <span className="px-2 py-1 bg-green-100 text-green-700 text-xs font-medium rounded-full">Success</span>}
                              {result.alreadyAssigned && <span className="px-2 py-1 bg-yellow-100 text-yellow-700 text-xs font-medium rounded-full">Already Assigned</span>}
                              {!result.success && !result.alreadyAssigned && <span className="px-2 py-1 bg-red-100 text-red-700 text-xs font-medium rounded-full">Failed</span>}
                            </div>
                            <p className={`${styling.textColor} mb-2`}>
                              {result.message}
                            </p>
                            {result.data && result.data.timestamp && (
                              <p className={`text-sm ${styling.textColor} opacity-75 flex items-center space-x-1`}>
                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                <span>{new Date(result.data.timestamp).toLocaleString()}</span>
                              </p>
                            )}
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}
            
            <form onSubmit={handleSubmit} className="space-y-8">
              {/* Available Roles */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <div className="w-1 h-6 bg-yellow-400 rounded-full mr-3"></div>
                  Available Roles
                </h3>
                <p className="text-gray-600 mb-6">Select the roles you want to assign to this user</p>
                
                {rolesLoading ? (
                  <div className="flex items-center justify-center py-12">
                    <div className="w-8 h-8 border-3 border-gray-200 border-t-yellow-400 rounded-full animate-spin"></div>
                    <span className="ml-3 text-gray-600 font-medium">Loading roles...</span>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    {availableRoles.map((role) => (
                      <div 
                        key={role.id} 
                        className={`relative p-6 rounded-lg border-2 transition-all duration-200 cursor-pointer hover:shadow-md ${
                          selectedRoles.includes(role.id)
                            ? 'bg-yellow-50 border-yellow-300 shadow-sm'
                            : 'bg-white border-gray-200 hover:border-yellow-300'
                        }`}
                        onClick={() => !loading && handleRoleToggle(role.id)}
                      >
                        <div className="flex items-start space-x-4">
                          <div className="flex-shrink-0 pt-1">
                            <input
                              type="checkbox"
                              id={`role-${role.id}`}
                              checked={selectedRoles.includes(role.id)}
                              onChange={() => handleRoleToggle(role.id)}
                              disabled={loading}
                              className="w-5 h-5 text-yellow-500 border-2 border-gray-300 rounded focus:ring-yellow-400 focus:ring-2"
                            />
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center space-x-2 mb-2">
                              <span className="text-2xl">{role.icon}</span>
                              <h4 className="font-semibold text-gray-900 capitalize">
                                {role.name}
                              </h4>
                            </div>
                            <p className="text-gray-600 text-sm">
                              {role.description}
                            </p>
                          </div>
                        </div>
                        
                        {/* Selection indicator */}
                        {selectedRoles.includes(role.id) && (
                          <div className="absolute top-4 right-4 w-6 h-6 bg-yellow-400 rounded-full flex items-center justify-center">
                            <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                            </svg>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>
              
              {/* Submit Button */}
              <div className="flex justify-end pt-6 border-t border-gray-200">
                <button
                  type="submit"
                  className="px-8 py-3 bg-yellow-400 hover:bg-yellow-500 text-black font-semibold rounded-lg transition-all duration-200 hover:shadow-md disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-yellow-400 disabled:hover:shadow-none"
                  disabled={selectedRoles.length === 0 || loading || rolesLoading}
                >
                  <div className="flex items-center space-x-2">
                    {loading ? (
                      <>
                        <div className="w-5 h-5 border-2 border-black border-t-transparent rounded-full animate-spin"></div>
                        <span>Assigning Roles...</span>
                      </>
                    ) : (
                      <>
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <span>Assign Selected Roles</span>
                      </>
                    )}
                  </div>
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AssignTask;