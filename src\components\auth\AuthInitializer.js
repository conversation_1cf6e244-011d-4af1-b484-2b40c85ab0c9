'use client';

import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { restoreRoleStates } from '@/store/features/authSlice';

// this component is used to initialize auth state on app startup to ensure role states are properly restored

const AuthInitializer = () => {
  const dispatch = useDispatch();
  const { isAuth, user, isAdmin, isStudent, isTutor } = useSelector(
    (state) => state.auth
  );

  useEffect(() => {
    // Only run after rehydration is complete and we have a user
    if (isAuth && user) {
      const userRole = user?.selectedRole || user?.role || user?.type;

      // Check if role states are properly set
      const hasCorrectRoleState =
        (userRole === 'admin' && isAdmin) ||
        (userRole === 'student' && isStudent) ||
        (userRole === 'tutor' && isTutor);

      // If role states are not correctly set, restore them
      if (!hasCorrectRoleState && userRole) {
        dispatch(restoreRoleStates());
      }
    }
  }, [isAuth, user, isAdmin, isStudent, isTutor, dispatch]);

  // This component doesn't render anything
  return null;
};

export default AuthInitializer;
