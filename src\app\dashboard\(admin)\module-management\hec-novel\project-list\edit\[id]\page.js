'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import api from '@/lib/api';

const NovelTopicEditPage = () => {
  const router = useRouter();
  const { id } = useParams();
  const queryClient = useQueryClient();
  
  // Form states
  const [formData, setFormData] = useState({
    title: '',
    sequenceTitle: '',
    category: '',
    instruction: ''
  });
  
  // Loading and error states
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [isError, setIsError] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');

  // Fetch categories using React Query
  const {
    data: categoriesResponse,
    isLoading: loadingCategories,
    refetch: refetchCategories,
    error: queryError
  } = useQuery({
    queryKey: ['novel-categories'],
    queryFn: async () => {
      const response = await api.get('/admin/novel/categories');
      return response.data;
    },
    enabled: true
  });

  // Process categories data
  let categories = [];
  if (categoriesResponse) {
    // If the response has a data property with the array
    if (categoriesResponse.data && Array.isArray(categoriesResponse.data)) {
      categories = categoriesResponse.data;
    }
    // If the response itself is an array
    else if (Array.isArray(categoriesResponse)) {
      categories = categoriesResponse;
    }
    // If the response has categories property
    else if (categoriesResponse.categories && Array.isArray(categoriesResponse.categories)) {
      categories = categoriesResponse.categories;
    }
  }
  
  const categoriesError = queryError || (categoriesResponse && categoriesResponse.success === false);

  console.log('Raw Categories Data:', categoriesResponse);
  console.log('Extracted Categories:', categories);
  console.log('Categories Error:', categoriesError);

  // Fetch topic data
  const fetchTopicData = async () => {
    try {
      setIsLoading(true);
      setIsError(false);
      
      console.log(`Fetching topic with ID: ${id}`);
      const response = await api.get(`/admin/novel/topics/${id}`);
      
      console.log('Topic data response:', response);
      
      if (response?.data) {
        // Handle different response structures
        const topicData = response.data.data || response.data;
        
        setFormData({
          title: topicData.title || '',
          sequenceTitle: topicData.sequenceTitle || '',
          category: topicData.category || '',
          instruction: topicData.instruction || ''
        });
        
        setIsError(false);
        setErrorMessage('');
      } else {
        throw new Error('No data received from API');
      }
    } catch (error) {
      console.error('Error fetching topic data:', error);
      setIsError(true);
      setErrorMessage(error.response?.data?.message || error.message || 'Failed to fetch topic data');
      toast.error('Failed to load topic data');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Basic validation
    if (!formData.title.trim()) {
      toast.error('Title is required');
      return;
    }

    if (!formData.sequenceTitle.trim()) {
      toast.error('Sequence Title is required');
      return;
    }
    
    if (!formData.category) {
      toast.error('Category is required');
      return;
    }
    
    if (!formData.instruction.trim()) {
      toast.error('Instruction is required');
      return;
    }

    try {
      setIsSaving(true);
      
      const updateData = {
        title: formData.title.trim(),
        sequenceTitle: formData.sequenceTitle.trim(),
        category: formData.category,
        instruction: formData.instruction.trim()
      };
      
      console.log('Updating topic with data:', updateData);
      
      const response = await api.put(`/admin/novel/topics/${id}`, updateData);
      
      console.log('Update response:', response);
      
      
      // Navigate back to the list page
      router.push('/dashboard/module-management/hec-novel?tab=openProjectList');
      
    } catch (error) {
      console.error('Error updating topic:', error);
      const errorMsg = error.response?.data?.message || error.message || 'Failed to update topic';
      toast.error(errorMsg);
      setErrorMessage(errorMsg);
    } finally {
      setIsSaving(false);
    }
  };

  // Handle cancel/back navigation
  const handleCancel = () => {
    router.push('/dashboard/module-management/hec-novel?tab=openProjectList');
  };

  // Load topic data on component mount
  useEffect(() => {
    if (id) {
      fetchTopicData();
    }
  }, [id]);

  // Loading state
  if (isLoading) {
    return (
      <div className="container mx-auto p-4">
        <div className="flex justify-center items-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-400 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading topic data...</p>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (isError) {
    return (
        <div className="container mx-auto px-4 py-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6">
          <div className="flex items-center mb-4">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error Loading Topic</h3>
              <p className="text-sm text-red-700 mt-1">{errorMessage}</p>
            </div>
          </div>
          <div className="flex gap-3">
            <button
              onClick={fetchTopicData}
              className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 transition-colors"
            >
              Retry
            </button>
            <button
              onClick={handleCancel}
              className="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 transition-colors"
            >
              Go Back
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Edit Novel Topic</h1>
            <p className="text-gray-600 mt-1">Update the topic details below</p>
          </div>
          <button
            onClick={handleCancel}
            className="text-gray-600 hover:text-gray-800 flex items-center gap-2"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            Back to List
          </button>
        </div>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit} className="bg-white space-y-6">
        {/* Main form content in gray container */}
        <div className="bg-gray-50 p-6 rounded-lg">
          {/* Section header */}
          <div className="mb-4 pb-2 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-800">
              Novel Topic Details
            </h2>
          </div>
          
          {/* Category field with new design */}
          <div className="mb-6">
            <div className="bg-[#FFFAC2] rounded-lg p-4 max-w-lg mx-auto">
              <label htmlFor="category" className="block text-sm font-semibold text-black mb-2">
                Time Frequency<span className="text-red-600">*</span>
              </label>
              <div className="relative">
                <select
                  id="category"
                  name="category"
                  value={formData.category}
                  onChange={handleInputChange}
                  className="w-full text-base rounded-md border border-gray-300 px-4 py-3 appearance-none bg-white focus:outline-none focus:ring-1 focus:ring-yellow-400"
                  disabled={loadingCategories}
                  required
                >
                  <option value="">
                    {loadingCategories ? 'Loading Time Frequency...' : 'Select Time Frequency'}
                  </option>
                  {!loadingCategories && categories.length > 0 && categories.map((category) => (
                    <option key={category.value || category.id || category} value={category.value || category.id || category}>
                      {category.description || category.name || category}
                    </option>
                  ))}
                </select>
                <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                  <svg className="fill-current h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                    <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
                  </svg>
                </div>
              </div>
              {categoriesError && !loadingCategories && (
                <div className="text-red-500 text-sm mt-1">
                  Failed to load categories.
                  <button
                    type="button"
                    onClick={() => refetchCategories()}
                    className="text-blue-500 underline ml-1 hover:text-blue-700"
                  >
                    Retry
                  </button>
                </div>
              )}
              {!loadingCategories && !categoriesError && categories.length === 0 && (
                <div className="text-yellow-600 text-sm mt-1">
                  No categories available.
                </div>
              )}
            </div>
          </div>

         

          {/* Sequence Title */}
          <div className="mb-6">
            <label
              htmlFor="sequenceTitle"
              className="block text-sm font-semibold text-black mb-2"
            >
              Sequence Title<span className="text-red-600">*</span>
            </label>
            <input
              id="sequenceTitle"
              name="sequenceTitle"
              type="text"
              value={formData.sequenceTitle}
              placeholder="Enter sequence title"
              className="w-full text-base rounded border border-gray-300 px-4 py-3 focus:outline-none focus:ring-1 focus:ring-yellow-400"
              onChange={handleInputChange}
              required
            />
          </div>

           {/* Title */}
          <div className="mb-6">
            <label
              htmlFor="title"
              className="block text-sm font-semibold text-black mb-2"
            >
             Project Title<span className="text-red-600">*</span>
            </label>
            <input
              id="title"
              name="title"
              type="text"
              value={formData.title}
              placeholder="Enter the main title"
              className="w-full text-base rounded border border-gray-300 px-4 py-3 focus:outline-none focus:ring-1 focus:ring-yellow-400"
              onChange={handleInputChange}
              required
            />
          </div>

          {/* Instruction textarea */}
          <div>
            <label
              htmlFor="instruction"
              className="block text-sm font-semibold text-black mb-2"
            >
              Instruction<span className="text-red-600">*</span>
            </label>
            <textarea
              id="instruction"
              name="instruction"
              rows="6"
              value={formData.instruction}
              placeholder="Write instruction here"
              className="w-full text-base rounded border border-gray-300 px-4 py-3 resize-none focus:outline-none focus:ring-1 focus:ring-yellow-400"
              onChange={handleInputChange}
              required
            />
          </div>
        </div>

        {/* Form Actions */}
        <div className="flex justify-end gap-3 pt-6 border-t border-gray-200">
          <button
            type="button"
            onClick={handleCancel}
            className="px-6 py-3 text-gray-700 bg-gray-100 border border-gray-300 rounded hover:bg-gray-200 focus:outline-none focus:ring-1 focus:ring-gray-500 transition-colors text-base"
            disabled={isSaving}
          >
            Cancel
          </button>
          <button
            type="submit"
            className="px-6 py-3 bg-yellow-400 text-black rounded hover:bg-yellow-500 focus:outline-none focus:ring-1 focus:ring-yellow-400 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-base font-medium"
            disabled={isSaving}
          >
            {isSaving ? (
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 border-2 border-black border-t-transparent rounded-full animate-spin"></div>
                Saving...
              </div>
            ) : (
              'Save Changes'
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default NovelTopicEditPage;