'use client';
import React, { useState } from 'react';
import Image from 'next/image';
import { ButtonIcon } from '@/components/Button';
import Link from 'next/link';
import EditorViewer from '@/components/EditorViewer';

const EssayFeedBackModal = ({
  isOpen,
  onClose,
  data,
  title = "Teacher's Feedback",
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-xl overflow-hidden">
        <div className="relative">
          <div className="absolute z-10 top-2 right-3">
            <ButtonIcon
              icon="mdi:close"
              innerBtnCls="h-8 w-8"
              btnIconCls="h-5 w-5"
              onClick={onClose}
              aria-label="Close modal"
            />
          </div>
          <div className="bg-[#FFF6EF] p-6 relative shadow-lg text-center">
            <Image
              src={'/assets/images/all-img/woodFrame.png'}
              alt="Mission Confirmation"
              width={600}
              height={200}
              className="max-w-96 mx-auto h-auto"
              priority
            />

            <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 mt-6 bg-[#FCF8EF] rounded-lg p-5 px-4 w-[55%]">
              <h2 className="text-2xl font-bold text-yellow-900 font-serif">
                {title}
              </h2>
            </div>
          </div>

          <div className="p-4 space-y-4">
            <div>
              <h2 className="text-xl font-medium text-yellow-700">
                Feedback:
              </h2>
              { data ? <EditorViewer data={data} /> : <h2 className='text-lg text-center w-full'>No feedback provided yet.</h2>}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EssayFeedBackModal;
