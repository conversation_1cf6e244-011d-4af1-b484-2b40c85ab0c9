'use client';
import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useDispatch } from 'react-redux';
import { toast } from 'sonner';
import api from '@/lib/api';
import { loginSuccess } from '@/store/features/authSlice';

const userTypes = [
  { id: 'admin', label: 'Admin', gradient: 'from-blue-500 to-blue-700', returnUrl: '/dashboard' },
  { id: 'tutor', label: 'Tutor', gradient: 'from-green-500 to-green-700', returnUrl: '/dashboard' },
  { id: 'student', label: 'Student', gradient: 'from-yellow-500 to-yellow-700', returnUrl: '/diary' },
];

export default function TestLogin() {
  const router = useRouter();
  const dispatch = useDispatch();
  const [isLoading, setIsLoading] = useState(false);
  const [selectedUserType, setSelectedUserType] = useState('admin');
  const [showPassword, setShowPassword] = useState(false);
  const [credentials, setCredentials] = useState({
    admin: { id: 'admin', password: '123456_Az' },
    tutor: { id: 'tutor', password: 'Tutor@123' },
    student: { id: 'student', password: 'Student@123' },
  });

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setCredentials(prev => ({
      ...prev,
      [selectedUserType]: {
        ...prev[selectedUserType],
        [name]: value
      }
    }));
  };

  const handleSaveCredentials = () => {
    localStorage.setItem('testLoginCredentials', JSON.stringify(credentials));
    toast.success('Credentials saved successfully!');
  };

  const handleLogin = async (userType) => {
    setIsLoading(true);

    try {
      const loginPayload = {
        userId: credentials[userType].id,
        password: credentials[userType].password,
        selectedRole: userType,
        rememberMe: false
      };

      const response = await api.post('/auth/login', loginPayload);

      if (response.success && response.data.access_token) {
        dispatch(
          loginSuccess({
            user: response.data.user,
            token: response.data.access_token,
            token_expires: response.data.token_expires,
          })
        );

        localStorage.setItem('testLoginCredentials', JSON.stringify(credentials));

        const returnUrl = userTypes.find(type => type.id === userType).returnUrl;
        router.push(returnUrl);
      } else {
        throw new Error(response.message || 'Login failed');
      }
    } catch (error) {
      console.error('Login error:', error);
      toast.error('Login failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-900 p-4">
      <div className="w-full max-w-lg">
        {/* Card */}
        <div className="bg-black border border-gray-800 rounded-lg shadow-2xl overflow-hidden">
          {/* Header */}
          <div className="bg-gray-800 p-4 text-center">
            <h1 className="text-xl font-bold text-white">DEV LOGIN</h1>
            <p className="text-gray-400 text-xs mt-1">Quick access for testing</p>
          </div>

          {/* Form */}
          <div className="p-6 space-y-4">
            <div className="grid grid-cols-2 gap-4">
              {/* ID Field */}
              <div>
                <label htmlFor="id" className="block text-xs font-medium text-gray-400 mb-1">
                  ID
                </label>
                <input
                  type="text"
                  id="id"
                  name="id"
                  value={credentials[selectedUserType].id}
                  onChange={handleInputChange}
                  className="w-full px-3 py-1.5 text-sm bg-gray-800 border border-gray-700 rounded text-gray-200 focus:ring-gray-700 focus:border-gray-600"
                  placeholder="Enter ID"
                />
              </div>

              {/* Password Field */}
              <div>
                <div className="flex justify-between items-center mb-1">
                  <label htmlFor="password" className="block text-xs font-medium text-gray-400">
                    PASSWORD
                  </label>
                  <span className="text-[10px] text-gray-500 flex items-center">
                    {showPassword ? 'HIDE' : 'SHOW'}
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="ml-1 text-gray-500 hover:text-gray-300 focus:outline-none"
                    >
                      <svg className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                    </button>
                  </span>
                </div>
                <div className="relative">
                  <input
                    type={showPassword ? "text" : "password"}
                    id="password"
                    name="password"
                    value={credentials[selectedUserType].password}
                    onChange={handleInputChange}
                    className="w-full px-3 py-1.5 text-sm bg-gray-800 border border-gray-700 rounded text-gray-200 focus:ring-gray-700 focus:border-gray-600"
                    placeholder="Enter password"
                  />
                </div>
              </div>
            </div>

            {/* User Type Buttons */}
            <div className="flex space-x-2">
              {userTypes.map(type => (
                <button
                  key={type.id}
                  type="button"
                  onClick={() => handleLogin(type.id)}
                  disabled={isLoading}
                  className={`flex-1 py-2 px-4 rounded text-sm font-medium text-white bg-gradient-to-r ${type.gradient} ${
                    isLoading ? 'opacity-50 cursor-not-allowed' : 'opacity-100 hover:opacity-90'
                  }`}
                >
                  {isLoading && selectedUserType === type.id ? (
                    <span className="flex items-center justify-center">
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Logging in...
                    </span>
                  ) : (
                    type.label
                  )}
                </button>
              ))}
            </div>

            {/* Save Credentials Button */}
            <div className="flex justify-center">
              <button
                type="button"
                onClick={handleSaveCredentials}
                className="bg-gray-800 hover:bg-gray-700 text-white text-sm font-medium py-1.5 px-3 rounded border border-gray-700 transition duration-200 flex items-center justify-center"
              >
                <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"></path>
                </svg>
                <span className="text-xs">SAVE CREDENTIALS</span>
              </button>
            </div>
          </div>

          {/* User Info */}
          <div className="px-6 pb-4">
            <div className="bg-gray-800 rounded p-3 text-xs text-gray-400">
              <div className="flex justify-between items-center mb-2">
                <span className="font-medium">Current User:</span>
                <span className="px-2 py-0.5 bg-gray-900 rounded text-gray-300">{selectedUserType}</span>
              </div>
              <div className="space-y-2">
                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <span className="block text-gray-500">ID:</span>
                    <span className="font-mono">{credentials[selectedUserType].id || 'Not set'}</span>
                  </div>
                  <div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-500">Password:</span>
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="text-[10px] text-gray-600 hover:text-gray-400 flex items-center focus:outline-none"
                      >
                        {showPassword ? 'HIDE' : 'SHOW'}
                        <svg className="h-2.5 w-2.5 ml-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                      </button>
                    </div>
                    <span className="font-mono">
                      {showPassword
                        ? credentials[selectedUserType].password
                        : '•'.repeat(Math.min(8, credentials[selectedUserType].password.length))}
                    </span>
                  </div>
                </div>
                <div>
                  <span className="block text-gray-500">Redirect URL:</span>
                  <span className="font-mono text-xs text-gray-400">{userTypes.find(type => type.id === selectedUserType).returnUrl}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="px-4 py-2 bg-gray-900 text-center text-xs text-gray-600 border-t border-gray-800">
            Development use only
          </div>
        </div>
      </div>
    </div>
  );
}
