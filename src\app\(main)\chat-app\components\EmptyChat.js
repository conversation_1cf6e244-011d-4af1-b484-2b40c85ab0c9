'use client';

import React from 'react';

const EmptyChat = () => {
  return (
    <section
      style={{
        flex: 1,
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        background: '#f7f9fb',
        color: '#6b7280',
        textAlign: 'center',
        padding: '40px 20px',
      }}
    >
      <div
        style={{
          width: '120px',
          height: '120px',
          borderRadius: '50%',
          background: '#e5e7eb',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          marginBottom: '24px',
        }}
      >
        <svg
          width="48"
          height="48"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
          style={{ color: '#9ca3af' }}
        >
          <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" />
        </svg>
      </div>
      
      <h3
        style={{
          fontSize: '20px',
          fontWeight: '600',
          color: '#374151',
          marginBottom: '8px',
        }}
      >
        Welcome to Chat
      </h3>
      
      <p
        style={{
          fontSize: '16px',
          color: '#6b7280',
          marginBottom: '24px',
          maxWidth: '400px',
          lineHeight: '1.5',
        }}
      >
        Select a contact from the sidebar to start a conversation, or search for someone to chat with.
      </p>
      
      <div
        style={{
          display: 'flex',
          flexDirection: 'column',
          gap: '12px',
          fontSize: '14px',
          color: '#9ca3af',
        }}
      >
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <div
            style={{
              width: '6px',
              height: '6px',
              borderRadius: '50%',
              background: '#d1d5db',
            }}
          />
          Send messages, images, and files
        </div>
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <div
            style={{
              width: '6px',
              height: '6px',
              borderRadius: '50%',
              background: '#d1d5db',
            }}
          />
          See when messages are delivered and read
        </div>
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <div
            style={{
              width: '6px',
              height: '6px',
              borderRadius: '50%',
              background: '#d1d5db',
            }}
          />
          Real-time typing indicators
        </div>
      </div>
    </section>
  );
};

export default EmptyChat;
