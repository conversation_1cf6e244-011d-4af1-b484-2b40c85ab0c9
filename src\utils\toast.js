import { toast } from 'sonner';

/**
 * Toast utility functions for consistent toast notifications across the app
 */

export const showToast = {
  /**
   * Show a success toast
   * @param {string} message - The message to display
   * @param {object} options - Additional options for the toast
   */
  success: (message, options = {}) => {
    toast.success(message, {
      duration: 4000,
      position: 'top-right',
      ...options
    });
  },

  /**
   * Show an error toast
   * @param {string} message - The message to display
   * @param {object} options - Additional options for the toast
   */
  error: (message, options = {}) => {
    toast.error(message, {
      duration: 5000,
      position: 'top-right',
      ...options
    });
  },

  /**
   * Show an info toast
   * @param {string} message - The message to display
   * @param {object} options - Additional options for the toast
   */
  info: (message, options = {}) => {
    toast.info(message, {
      duration: 4000,
      position: 'top-right',
      ...options
    });
  },

  /**
   * Show a warning toast
   * @param {string} message - The message to display
   * @param {object} options - Additional options for the toast
   */
  warning: (message, options = {}) => {
    toast.warning(message, {
      duration: 4000,
      position: 'top-right',
      ...options
    });
  },

  /**
   * Show a loading toast
   * @param {string} message - The message to display
   * @param {object} options - Additional options for the toast
   */
  loading: (message, options = {}) => {
    return toast.loading(message, {
      position: 'top-right',
      ...options
    });
  },

  /**
   * Dismiss a specific toast
   * @param {string} toastId - The ID of the toast to dismiss
   */
  dismiss: (toastId) => {
    toast.dismiss(toastId);
  },

  /**
   * Dismiss all toasts
   */
  dismissAll: () => {
    toast.dismiss();
  }
};

/**
 * API request helper that allows explicit control over toast notifications
 * @param {function} apiCall - The API call function
 * @param {object} options - Options for toast behavior
 * @param {boolean} options.showSuccessToast - Whether to show success toast (default: true)
 * @param {boolean} options.showErrorToast - Whether to show error toast (default: true)
 * @param {string} options.successMessage - Custom success message
 * @param {string} options.errorMessage - Custom error message
 */
export const apiWithToast = async (apiCall, options = {}) => {
  const {
    showSuccessToast = true,
    showErrorToast = true,
    successMessage,
    errorMessage
  } = options;

  try {
    const response = await apiCall();
    
    if (showSuccessToast && response?.data?.success) {
      const message = successMessage || response?.data?.message || 'Operation completed successfully!';
      showToast.success(message);
    }
    
    return response;
  } catch (error) {
    if (showErrorToast) {
      const message = errorMessage || 
                     error?.response?.data?.message || 
                     error.message || 
                     'Something went wrong!';
      showToast.error(message);
    }
    throw error;
  }
};

export default showToast;
