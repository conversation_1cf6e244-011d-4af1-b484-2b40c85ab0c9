'use client';

import { ButtonIcon } from '@/components/Button';
import GoBack from '@/components/shared/GoBack';
import PrivateRoute from '@/components/shared/PrivateRoute';
import useDataFetch from '@/hooks/useDataFetch';
import api from '@/lib/api';
import { loginSuccess } from '@/store/features/authSlice';
import { setSelectedPlan } from '@/store/features/commonSlice';
import { Icon } from '@iconify/react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import React, { useState, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { toast } from 'sonner';

// Skeleton loader component for plan cards
const PlanCardSkeleton = () => {
  return (
    <div className="plan-card text-left mb-4 border-2 border-[#FFDE34] rounded-lg p-6">
      <div className="h-7 bg-gray-200 rounded w-3/4 mb-4"></div>
      <div className="h-6 bg-gray-200 rounded w-1/4 mb-4"></div>
      <div className="h-3 bg-gray-200 rounded w-1/5 mb-4 mt-1"></div>
      <div className="h-12 bg-gray-200 rounded w-full mb-4"></div>

      <div className="mt-10 space-y-2">
        <div className="h-4 bg-gray-200 rounded w-full"></div>
        <div className="h-4 bg-gray-200 rounded w-full"></div>
        <div className="h-4 bg-gray-200 rounded w-full"></div>
        <div className="h-4 bg-gray-200 rounded w-full"></div>
        <div className="h-4 bg-gray-200 rounded w-full"></div>
      </div>

      <div className="h-12 bg-gray-200 rounded w-full mt-28"></div>
    </div>
  );
};

const PricingPlan = () => {
  const [selectedSubscriptionType, setSelectedSubscriptionType] =
    useState('monthly');
  const router = useRouter();

  // Fetch plans data from API
  const { data, isLoading, error } = useDataFetch({
    queryKey: 'plans',
    endPoint: '/plans',
  });
  const plans = data?.items;

  // Filter plans by subscription type
  const filteredPlans = useMemo(() => {
    if (!plans || !Array.isArray(plans)) return [];
    return plans.filter(
      (plan) => plan.subscriptionType === selectedSubscriptionType
    );
  }, [plans, selectedSubscriptionType]);

  // const handlePurchaseContinue = async () => {
  //   try {
  //     const response = await api.post('/shop/cart/add', {
  //       shopItemId: selectedItem.id,
  //       quantity: 1,
  //     });
  //     cartRefetch();
  //     router.push('/checkout');
  //   } catch (error) {
  //     console.log(error);
  //   }
  // };

  return (
    // <PrivateRoute>
    <div className="max-w-7xl mx-auto px-5 xl:px-0">
      <GoBack title={'HEC Pricing plan'} linkClass="my-5 mb-8 max-w-60" />

      <div className="text-center space-y-3">
        <h1 className="font-inter font-semibold text-4xl md:text-5xl">
          Find the Perfect Subscription for Your Learning Needs
        </h1>
        <p className="font-inter text-lg">
          Pay Only for What You Need and Enjoy Unlimited Access to Premium
          Content.
        </p>
      </div>

      <div className="py-10 pb-20 relative">
        <div className="flex justify-center">
          <div className="flex items-center justify-center bg-[#FFFFFF] rounded-full p-2 shadow-md  border">
            <button
              className={`text-[16px] text-center py-1 px-[15px] rounded-full font-semibold focus:outline-none ${
                selectedSubscriptionType === 'monthly'
                  ? 'bg-[#FFDE34] text-[#2B2A28]'
                  : 'text-[#959595]'
              }`}
              onClick={() => setSelectedSubscriptionType('monthly')}
            >
              Monthly
            </button>
            <button
              className={`text-[16px] text-center py-1 px-4 rounded-full font-semibold focus:outline-none ${
                selectedSubscriptionType === 'yearly'
                  ? 'bg-[#FFDE34] text-[#2B2A28]'
                  : 'text-[#959595]'
              }`}
              onClick={() => setSelectedSubscriptionType('yearly')}
            >
              Yearly
            </button>
          </div>
        </div>

        {selectedSubscriptionType === 'yearly' && (
          <div className="absolute left-1/2 -translate-x-1/2 ml-36 sm:ml-40 top-12 sm:top-16 text-[#7732BB] text-sm font-semibold">
            SAVE UP TO 16%
          </div>
        )}

        {selectedSubscriptionType === 'yearly' && (
          <div className="mt-2 absolute left-1/2 -translate-x-1/2 ml-24 top-20 ">
            <Image
              src="/assets/images/all-img/arrow.png"
              alt="icon"
              width={70}
              height={30}
            />
          </div>
        )}
      </div>

      {isLoading ? (
        <div className="grid md:grid-cols-4 gap-0.5">
          {[...Array(4)].map((_, index) => (
            <div key={index} className="w-[70%] md:w-[80%] mx-auto">
              <PlanCardSkeleton />
            </div>
          ))}
        </div>
      ) : error ? (
        <div className="text-center text-red-500">
          Error loading plans. Please try again later.
        </div>
      ) : (
        <div className="grid md:grid-cols-4 gap-5 mb-14">
          {filteredPlans.map((plan) => (
            <PlanCard
              key={plan.id}
              title={plan.name}
              price={
                <span className="w-[59px] h-[36px] font-inter font-bold text-[25px] leading-[100%] tracking-[0%]">
                  ₩{plan.price}
                </span>
              }
              plan={plan}
              description={plan.description}
              isPopular={plan.type === 'pro'}
              isBestValue={plan.subscriptionType === 'yearly'}
              subscriptionType={plan.subscriptionType}
              planId={plan.id}
              features={plan.planFeatures.map((feature) => ({
                name: feature.name,
                isAvailable: true,
              }))}
            />
          ))}
        </div>
      )}
    </div>
    // </PrivateRoute>
  );
};

const PlanCard = ({
  title,
  price,
  description,
  isPopular,
  isBestValue,
  features,
  subscriptionType,
  planId,
}) => {
  const [isSubscribing, setIsSubscribing] = useState(false);
  const { user, token } = useSelector((state) => state.auth);
  const router = useRouter();
  const dispatch = useDispatch();
  // Check if user is already subscribed to this plan
  const isCurrentPlan = useMemo(() => {
    if (!user) return false;
    return user.activePlan === title;
  }, [user, title]);

  const currentPlanId = user?.activePlanDetails?.id;

  return (
    <div
      className={`plan-card text-left border-2 border-[#FFDE34] rounded-lg p-6 pb-20 relative ${
        isPopular ? 'bg-[#FFDE34]' : ''
      }`}
    >
      {isBestValue && (
        <div className="absolute -top-3 -right-3 bg-[#7732BB] text-white text-xs font-semibold px-3 py-1 rounded-full transform rotate-12">
          Best Value
        </div>
      )}
      <h2 className="text-xl font-bold mb-4">
        {title}{' '}
        {isPopular && (
          <span className="bg-[#FFDE34] text-xs font-semibold px-2 py-1 rounded-full">
            Most Popular
          </span>
        )}
      </h2>
      <p className="text-left font-bold mb-4">{price}</p>
      <p className="text-[#656565] font-inter font-semibold text-xs mb-4 -mt-5">
        {subscriptionType === 'yearly' ? '/year' : '/month'}
      </p>

      <p className="text-[#454545] mb-4">{description}</p>

      <ul className="mt-10 text-left text-[#454545] mb-4 font-inter font-semibold text-sm">
        {features.map((feature, index) => (
          <li className="mb-2" key={index}>
            {feature.isAvailable ? (
              <span className="text-black mr-2">✔</span>
            ) : (
              <span className="mr-2">&nbsp;&nbsp;&nbsp;&nbsp;</span>
            )}
            {feature.name}
          </li>
        ))}
      </ul>

      <div className="absolute w-[93%] left-1/2 bottom-3 left-3">
        {isCurrentPlan ? (
          <div className="bg-green-600 text-white rounded px-4 py-3 mt-8 flex items-center justify-center">
            <Icon
              icon="fluent:select-all-on-16-regular"
              width="20"
              height="20"
              className="mr-1"
            />
            Current Plan
          </div>
        ) : (
          <button
            className={`w-full bg-[#432005] text-white rounded py-3 mt-8 flex items-center justify-center transition-all hover:bg-[#5a2c07] ${
              isSubscribing ? 'cursor-not-allowed opacity-70' : ''
            }`}
            onClick={() =>
              router.push(`/checkout?planId=${planId}&type=subscription`)
            }
            disabled={isSubscribing}
          >
            {isSubscribing ? (
              <>
                <div className="w-5 h-5 border-t-2 border-b-2 border-white rounded-full animate-spin mr-2"></div>
                Subscribing...
              </>
            ) : currentPlanId ? (
              'Upgrade'
            ) : (
              'Subscribe Now'
            )}
          </button>
        )}
      </div>
    </div>
  );
};

export default PricingPlan;
