import React, { useState } from 'react';
import Image from 'next/image';
import { useSelector } from 'react-redux';
import useDataFetch from '@/hooks/useDataFetch';
import api from '@/lib/api';
import { useRouter } from 'next/navigation';

const RecentlyInvitedPeople = ({ entryId }) => {
  const router = useRouter();
  const [sharingWithUser, setSharingWithUser] = useState(null);
  const [sharedIds, setSharedIds] = useState([]);

  const todayEntry = useSelector((state) => state.diary?.todayEntry);
  const diaryEntryId = entryId || todayEntry?.id;

  const { data, isLoading } = useDataFetch({
    queryKey: ['my-friends', diaryEntryId],
    endPoint: `/student/friends/diary-follows/sharing-status?entryId=${diaryEntryId}`,
  });

  const invitedPeople = data?.items || data || [];

  const handleShare = async (person) => {
    if (!diaryEntryId) return;
    const targetUserId = person.requesterId || person.id;
    setSharingWithUser(targetUserId);
    try {
      await api.post(`/diary/entries/${diaryEntryId}/share-with-friend`, {
        targetUserId,
      });
      setSharedIds((prev) => [...prev, targetUserId]);
    } finally {
      setSharingWithUser(null);
    }
  };

  if (isLoading) {
    return (
      <div className="pb-6 border-b">
        <h3 className="text-2xl font-semibold text-gray-700 mb-4">
          Recently invited people
        </h3>
        <div className="flex justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-yellow-500" />
        </div>
      </div>
    );
  }

  return (
    <div className="pb-6 border-b">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-2xl font-semibold text-gray-700">
          Recently invited people
        </h3>
      </div>

      {invitedPeople.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          <p>No followers found</p>
          <p className="text-sm">Add follower to share your diary with them</p>
          <button
            className="mt-4 bg-yellow-100 text-gray-700 px-4 py-2 rounded-lg text-sm hover:bg-yellow-200 border border-gray-500"
            onClick={() => router.push('/invite-friends')}
          >
            Invite Friends
          </button>
        </div>
      ) : (
        <div className="space-y-3">
          {invitedPeople.map((person) => {
            const personId = person.requesterId || person.id;
            const isLoadingForPerson = sharingWithUser === personId;
            const isShared = person.isAlreadyShared || sharedIds.includes(personId);

            return (
              <div key={personId} className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="w-8 h-8 rounded-full overflow-hidden mr-3 relative">
                    <Image
                      src={
                        person.profilePicture ||
                        person.avatar ||
                        '/assets/images/all-img/introduction/Frame 1.png'
                      }
                      alt={person.diaryOwnerName || person.username || 'User'}
                      fill
                      className="object-cover"
                    />
                  </div>
                  <div>
                    <p className="text-base font-medium">
                      {person.requesterName || person.username}
                    </p>
                  </div>
                </div>
                <button
                  className={`px-4 py-1 rounded-lg text-sm transition-colors ${
                    isLoadingForPerson || isShared
                      ? 'bg-gray-100 text-gray-500 cursor-not-allowed'
                      : 'bg-yellow-100 text-gray-700 border border-gray-500 hover:bg-yellow-200'
                  }`}
                  onClick={() => handleShare(person)}
                  disabled={isLoadingForPerson || isShared}
                >
                  {isLoadingForPerson ? (
                    <div className="flex items-center gap-1">
                      <span className="animate-spin h-3 w-3 border border-gray-400 rounded-full border-t-transparent" />
                      Sharing...
                    </div>
                  ) : isShared ? (
                    'Shared'
                  ) : (
                    'Share'
                  )}
                </button>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};

export default RecentlyInvitedPeople;
