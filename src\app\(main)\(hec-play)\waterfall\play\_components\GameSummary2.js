'use client';
import React from 'react';
import { Icon } from '@iconify/react';
import { motion } from 'framer-motion';
import Link from 'next/link';
import Image from 'next/image';
import { generateSummaryFromServerData } from './utils/answerUtils';

const GameSummary2 = ({ resultData, questionsData, onRestart }) => {
  // Extract data from API response
  const {
    set_title,
    total_correct_answers,
    total_questions,
    score,
    answers,
    submitted_at
  } = resultData;

  // Debug logging
  // console.log('GameSummary2 Debug:', {
  //   total_questions,
  //   answers_count: answers?.length,
  //   answers,
  //   questionsData_count: questionsData?.length,
  //   questionsData
  // });

  // Debug question IDs matching
  // console.log('Question ID Matching:');
  // console.log('All answer question_ids:', answers?.map(a => a.question_id));
  // console.log('All question ids:', questionsData?.map(q => q.id));

  questionsData?.forEach((q, i) => {
    const matchingAnswer = answers?.find(a => a.question_id === q.id);
    // console.log(`Question ${i + 1}:`, {
    //   question_id: q.id,
    //   has_matching_answer: !!matchingAnswer,
    //   matching_answer: matchingAnswer
    // });
  });
  

  return (
    <div className="min-h-[calc(100vh-200px)] p-4">
      <div className="">
        {/* Header with Cat and Score */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="bg-white rounded-2xl shadow-lg p-6 mb-6"
        >
          <div className="flex items-center justify-between">
            {/* Cat Image */}
            <div className="flex items-center">
              <div className="w-24 h-24 bg-yellow-100 rounded-2xl flex items-center justify-center mr-6">
                <Image
                  src="/assets/images/all-img/catImg.png"
                  alt="Cat"
                  width={80}
                  height={80}
                  className="object-contain"
                />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-800 mb-2">Game Completed!</h1>
                {set_title && (
                  <p className="text-gray-600">Set: {set_title}</p>
                )}
              </div>
            </div>

            {/* Score Badge */}
            <div className="relative">
              <Image src={'/assets/images/all-img/summaryScore.png'} alt="Medal" width={400} height={200} />
              <div className="text-center absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                <h2 className="text-2xl font-bold text-yellow-700 opacity-90">Your Score: {score}</h2>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Answer Sheet */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className=" max-w-4xl mx-auto mb-6"
        >
          <div className="border-2 border-yellow-600 bg-yellow-100 rounded-xl p-4 mb-6">
            <h2 className="text-xl font-bold text-center text-gray-800 text-yellow-700">Answer Sheet</h2>
          </div>

          {questionsData && questionsData.length > 0 && (
            <div className="space-y-4 max-h-80 overflow-y-auto p-4 border rounded-lg">
              {questionsData.map((questionData, index) => {
                // Find the corresponding answer data (if exists)
                const answer = answers?.find(a => a.question_id === questionData.id);

                let summaryHTML;
                if (answer && answer.submitted_answers && answer.correct_answers) {
                  // User answered this question - show with color coding
                  summaryHTML = generateSummaryFromServerData(answer, questionData);
                } else {
                  // User didn't answer this question - show plain correct sentence without highlighting
                  let gapIndex = 0;
                  summaryHTML = questionData.question_text_plain.replace(/\[\[gap\]\]/g, () => {
                    const correctAnswer = questionData.correct_answers[gapIndex] || '';
                    gapIndex++;
                    return correctAnswer; // Just return the word without any styling
                  });
                }

                // console.log(`Question ${index + 1}:`, {
                //   question_id: questionData.id,
                //   has_answer: !!answer,
                //   answer,
                //   summaryHTML,
                //   question_text: questionData.question_text_plain,
                //   correct_answers: questionData.correct_answers
                // });

                return (
                  <motion.div
                    key={questionData.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                    className="py-4 border-b border-gray-100 last:border-b-0"
                  >
                    <div className="flex items-start">
                      <span className="text-lg font-medium text-gray-700 mr-4 mt-1">
                        {index + 1}.
                      </span>
                      <div className="flex-1">
                        {/* Display the complete sentence with color-coded answers */}
                        <div
                          className="text-lg leading-relaxed"
                          dangerouslySetInnerHTML={{
                            __html: summaryHTML || questionData.question_text_plain
                          }}
                        />
                      </div>
                    </div>
                  </motion.div>
                );
              })}
            </div>
          )}
        </motion.div>

        {/* Play Again Button */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          className="flex justify-center gap-4"
        >
          <button
            onClick={onRestart}
            className="bg-gradient-to-r from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600 text-white px-8 py-3 rounded-full font-medium shadow-lg transition-all duration-200 flex items-center gap-2"
          >
            Play Again
            <Icon icon="mdi:refresh" className="text-xl" />
          </button>

          <Link
            href="/waterfall"
            className="bg-gray-100 hover:bg-gray-200 text-gray-700 px-8 py-3 rounded-full font-medium transition-all duration-200"
          >
            Back to Games
          </Link>
        </motion.div>
      </div>
    </div>
  );
};

export default GameSummary2;
