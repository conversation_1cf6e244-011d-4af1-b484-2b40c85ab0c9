import React, { useState, useRef, useEffect } from 'react';
import { Icon } from '@iconify/react';
import api from '@/lib/api';
import { toast } from 'sonner';

const SkinStatusDropdown = ({
  skinId,
  status,
  onSuccess,
}) => {
  const [currentStatus, setCurrentStatus] = useState(status);
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const dropdownRef = useRef(null);

  const statusOptions = [
    {
      value: true,
      label: 'Active',
      icon: 'heroicons:check-circle',
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      hoverColor: 'hover:bg-green-100'
    },
    {
      value: false,
      label: 'Inactive',
      icon: 'heroicons:x-circle',
      color: 'text-red-600',
      bgColor: 'bg-red-50',
      hoverColor: 'hover:bg-red-100'
    }
  ];

  const currentOption = statusOptions.find(option => option.value === currentStatus);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleStatusChange = async (newStatus) => {
    if (newStatus === currentStatus) {
      setIsOpen(false);
      return;
    }

    try {
      setIsLoading(true);
      setIsOpen(false);

      // Make API call to update skin status
      await api.patch(`/admin/diary/skins/${skinId}/status`, {
        isActive: newStatus,
      });

      // Update local state
      setCurrentStatus(newStatus);

      // Call onSuccess callback if provided
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('Error updating skin status:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        disabled={isLoading}
        className={`
          inline-flex items-center gap-1.5 px-2 py-0.5 text-xs font-medium rounded-md border
          ${currentOption?.bgColor} ${currentOption?.color} border-transparent
          hover:${currentOption?.hoverColor} focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:ring-offset-1
          disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200
        `}
        title="Change status"
      >
        {isLoading ? (
          <Icon icon="eos-icons:loading" className="w-3 h-3 animate-spin" />
        ) : (
          <Icon icon={currentOption?.icon} className="w-3 h-3" />
        )}
        <span>{currentOption?.label}</span>
        <Icon 
          icon="heroicons:chevron-down" 
          className={`w-3 h-3 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`} 
        />
      </button>

      {isOpen && (
        <div className="absolute right-0 mt-1 w-32 bg-white rounded-md shadow-lg border border-gray-200 z-50">
          <div className="py-1">
            {statusOptions.map((option) => (
              <button
                key={option.value}
                onClick={() => handleStatusChange(option.value)}
                className={`
                  w-full text-left px-3 py-2 text-xs font-medium flex items-center gap-2
                  ${option.value === currentStatus 
                    ? `${option.bgColor} ${option.color}` 
                    : 'text-gray-700 hover:bg-gray-50'
                  }
                  transition-colors duration-150
                `}
              >
                <Icon icon={option.icon} className="w-3 h-3" />
                <span>{option.label}</span>
                {option.value === currentStatus && (
                  <Icon icon="heroicons:check" className="w-3 h-3 ml-auto" />
                )}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default SkinStatusDropdown;
