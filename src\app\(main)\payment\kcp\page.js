'use client';

import { useSearchParams, useRouter } from 'next/navigation';
import React, { useEffect, useRef, useState } from 'react';
import { toast } from 'sonner';

const KCPPaymentGateway = () => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const formRef = useRef(null);
  const [sdkLoaded, setSdkLoaded] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [orderData, setOrderData] = useState({});

  // Extract all parameters from URL query
  const params = {
    site_cd: searchParams.get('site_cd'),
    site_name: searchParams.get('site_name'),
    ordr_idxx: searchParams.get('ordr_idxx'),
    good_name: searchParams.get('good_name'),
    good_mny: searchParams.get('good_mny'),
    buyr_name: searchParams.get('buyr_name'),
    buyr_tel2: searchParams.get('buyr_tel2'),
    buyr_mail: searchParams.get('buyr_mail'),
    pay_method: searchParams.get('pay_method'),
    quotaopt: searchParams.get('quotaopt'),
    res_cd: searchParams.get('res_cd'),
    res_msg: searchParams.get('res_msg'),
    enc_info: searchParams.get('enc_info'),
    enc_data: searchParams.get('enc_data'),
    tran_cd: searchParams.get('tran_cd'),
    return_url: searchParams.get('return_url'),
    action_url: searchParams.get('action_url'),
    tno: searchParams.get('tno'),
    ordr_chk: searchParams.get('ordr_chk'),
    kcp_sign_data: searchParams.get('kcp_sign_data'),
  };

  // KCP parameters for the gateway
  const kcpParams = {
    site_cd: params?.site_cd,
    site_name: params?.site_name,
    quotaopt: params?.quotaopt,
    res_cd: params?.res_cd,
    res_msg: params?.res_msg,
    enc_info: params?.enc_info,
    enc_data: params?.enc_data,
    tran_cd: params?.tran_cd,
  };

  // Initialize order data from URL parameters
  useEffect(() => {
    setOrderData({
      ordr_idxx: params?.ordr_idxx,
      good_name: params?.good_name,
      good_mny: params?.good_mny,
      buyr_name: params?.buyr_name,
      buyr_tel2: params?.buyr_tel2,
      buyr_mail: params?.buyr_mail,
      pay_method: params?.pay_method,
    });
  }, [searchParams]);

  // Load KCP SDK
  useEffect(() => {
    // Remove existing script if any
    const existingScript = document.querySelector(
      'script[src*="kcp_spay_hub.js"]'
    );
    if (existingScript) {
      existingScript.remove();
    }

    const script = document.createElement('script');
    script.src = 'https://testspay.kcp.co.kr/plugin/kcp_spay_hub.js';
    script.async = false; // Load synchronously for KCP
    script.onload = () => {
      console.log('KCP SDK loaded successfully');
      setSdkLoaded(true);
      setIsLoading(false);
    };
    script.onerror = () => {
      console.error('Failed to load KCP SDK');
      toast.error('Failed to load payment gateway. Please try again.');
      setIsLoading(false);
    };

    document.head.appendChild(script);

    return () => {
      if (document.head.contains(script)) {
        document.head.removeChild(script);
      }
    };
  }, []);

  // Define the required KCP callback function
  useEffect(() => {
    // This function MUST be named m_Completepayment and be available globally
    window.m_Completepayment = function (FormOrJson, closeEvent) {
      console.log('Payment completion callback triggered', FormOrJson);

      try {
        // KCP passes the form data back to this function
        const form = formRef.current;
        if (!form) {
          console.error('Form reference not found');
          return;
        }

        // GetField function equivalent - set form values from KCP response
        if (typeof FormOrJson === 'object') {
          Object.keys(FormOrJson).forEach((key) => {
            const input = form.querySelector(`[name="${key}"]`);
            if (input) {
              input.value = FormOrJson[key];
            }
          });
        }

        // Check payment result
        const resCode = form.querySelector('[name="res_cd"]')?.value;
        const resMsg = form.querySelector('[name="res_msg"]')?.value;

        if (resCode === '0000') {
          // Payment authentication successful - now submit for approval
          console.log(
            'Payment authentication successful, submitting for approval'
          );

          // You would typically submit to your backend here
          // form.action = '/api/kcp/payment-approval';
          // form.submit();

          // For demo purposes, redirect to success page
          toast.success('Payment completed successfully!');
          if (params?.return_url) {
            window.location.href = params.return_url;
          } else {
            router.replace('/payment/success');
          }
        } else {
          // Payment authentication failed
          // toast.error(`Payment failed: [${resCode}] ${resMsg}`);
          if (closeEvent && typeof closeEvent === 'function') {
            closeEvent();
          }
          // Optionally redirect back or show error page
          router.replace('/payment/failed');
        }
      } catch (error) {
        console.error('Error in payment completion callback:', error);
        toast.error('An error occurred during payment processing.');
      }
    };

    // Define jsf__pay function
    window.jsf__pay = function (form) {
      let retries = 0;
      const maxRetries = 10; // Retry max 10 times
      const interval = 500; // Every 500ms

      const checkAndPay = () => {
        if (typeof window.KCP_Pay_Execute_Web === 'function') {
          console.log('KCP SDK Ready. Proceeding with Payment.');
          window.KCP_Pay_Execute_Web(form);
        } else if (retries < maxRetries) {
          retries++;
          console.log(`Waiting for KCP SDK... (${retries}/${maxRetries})`);
          setTimeout(checkAndPay, interval);
        } else {
          console.error('Failed to load KCP SDK within expected time.');
          // alert('Payment system is not ready. Please try again later.');
        }
      };

      checkAndPay();
    };

    return () => {
      // Cleanup global functions
      if (window.m_Completepayment) delete window.m_Completepayment;
      if (window.jsf__pay) delete window.jsf__pay;
    };
  }, [router, params?.return_url]);

  // Handle payment initiation - called automatically when SDK loads
  const handlePayment = () => {
    if (!sdkLoaded) {
      console.log('SDK not loaded yet, waiting...');
      return;
    }

    if (!window.jsf__pay) {
      toast.message('Payment function not available. Please refresh the page.');
      return;
    }

    if (!formRef.current) {
      toast.error('Payment form not ready. Please try again.');
      return;
    }

    console.log('Initiating payment with data:', orderData);
    window.jsf__pay(formRef.current);
  };

  // Auto-trigger payment when SDK is loaded
  useEffect(() => {
    if (sdkLoaded) {
      // Add a small delay to ensure form is ready
      setTimeout(() => {
        handlePayment();
      }, 300);
    }
  }, [sdkLoaded]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading KCP Payment Gateway...</p>
          <p className="text-sm text-gray-500 mt-2">
            Please wait while we prepare your payment...
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-2xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h1 className="text-2xl font-bold text-center mb-6">
            KCP Payment Gateway
          </h1>

          <div className="text-center">
            <div className="animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-3/4 mx-auto mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2 mx-auto mb-4"></div>
            </div>
            <p className="text-gray-600">
              Initializing secure payment process...
            </p>
            <p className="text-sm text-gray-500 mt-2">
              The payment window will open automatically
            </p>
          </div>

          {/* Hidden Form for KCP */}
          <form
            name="order_info"
            ref={formRef}
            method="post"
            action={params?.action_url || '/api/kcp/payment-approval'}
            style={{ display: 'none' }}
          >
            {/* Order Data */}
            <input
              type="hidden"
              name="ordr_idxx"
              value={orderData.ordr_idxx || ''}
            />
            <input
              type="hidden"
              name="good_name"
              value={orderData.good_name || ''}
            />
            <input
              type="hidden"
              name="good_mny"
              value={orderData.good_mny || ''}
            />
            <input
              type="hidden"
              name="buyr_name"
              value={orderData.buyr_name || ''}
            />
            <input
              type="hidden"
              name="buyr_tel2"
              value={orderData.buyr_tel2 || ''}
            />
            <input
              type="hidden"
              name="buyr_mail"
              value={orderData.buyr_mail || ''}
            />
            <input
              type="hidden"
              name="pay_method"
              value={orderData.pay_method || ''}
            />

            {/* KCP Required Parameters */}
            <input
              type="hidden"
              name="site_cd"
              value={kcpParams.site_cd || ''}
            />
            <input
              type="hidden"
              name="site_name"
              value={kcpParams.site_name || ''}
            />
            <input
              type="hidden"
              name="quotaopt"
              value={kcpParams.quotaopt || ''}
            />

            {/* Additional KCP Parameters */}
            <input type="hidden" name="tno" value={params?.tno || ''} />
            <input
              type="hidden"
              name="ordr_chk"
              value={params?.ordr_chk || ''}
            />
            <input
              type="hidden"
              name="kcp_sign_data"
              value={params?.kcp_sign_data || ''}
            />

            {/* KCP Response Fields - These will be populated by KCP */}
            <input type="hidden" name="res_cd" value={kcpParams.res_cd || ''} />
            <input
              type="hidden"
              name="res_msg"
              value={kcpParams.res_msg || ''}
            />
            <input
              type="hidden"
              name="enc_info"
              value={kcpParams.enc_info || ''}
            />
            <input
              type="hidden"
              name="enc_data"
              value={kcpParams.enc_data || ''}
            />
            <input
              type="hidden"
              name="tran_cd"
              value={kcpParams.tran_cd || ''}
            />
          </form>
        </div>
      </div>
    </div>
  );
};

export default KCPPaymentGateway;
