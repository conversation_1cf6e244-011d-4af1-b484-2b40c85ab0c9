import { Icon } from '@iconify/react';
import Image from 'next/image';
import React from 'react';

export const ButtonIcon = ({ icon, innerBtnCls, btnIconCls, ...props }) => {
  return (
    <span
      {...props}
      className={`${innerBtnCls} relative hover:filter hover:brightness-110 flex items-center cursor-pointer`}
    >
      <Image
        src={'/assets/images/all-img/btn-bg.png'}
        alt="icon"
        width={50}
        height={50}
        className="w-full h-full"
      />

      <Icon
        icon={icon}
        className={`${btnIconCls} absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 z-10`}
      />
    </span>
  );
};

const Button = ({ icon, buttonText, isSquareBtn = false, disabled = false, ...props }) => {
  return (
    <div>
      {isSquareBtn ? (
        <div className={`p-px bg-white rounded-lg border border-yellow-800 w-full ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}>
          <button
            {...props}
            className="px-4 py-1 max-sm:px-2 max-sm:py-0 w-full justify-center bg-[#723F11] rounded-lg flex items-center text-white gap-1 max-sm:text-xs"
          >
            {icon && (
              <ButtonIcon
                icon={icon}
                {...props}
                innerBtnCls="h-8"
                btnIconCls={'h-4 w-4'}
              />
            )}

            {buttonText}
          </button>
        </div>
      ) : (
        <div className={`p-px bg-white rounded-full border border-yellow-800 w-full ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}>
          <button
            {...props}
            className="px-4 py-1 max-sm:px-2 max-sm:py-0 w-full justify-center yellow-btn rounded-full flex items-center text-yellow-800 gap-1 max-sm:text-xs"
          >
            {icon && (
              <ButtonIcon
                icon={icon}
                {...props}
                innerBtnCls="h-8"
                btnIconCls={'h-4 w-4'}
              />
            )}

            {buttonText}
          </button>
        </div>
      )}
    </div>
  );
};

export default Button;
