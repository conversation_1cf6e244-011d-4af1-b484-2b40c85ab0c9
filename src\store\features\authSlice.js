import { createSlice } from '@reduxjs/toolkit';
import { REHYDRATE } from 'redux-persist';
import api from '@/lib/api';
import { setAuthTokenCookie, removeAuthTokenCookie } from '@/lib/auth';

// Helper function to reset all role states
const resetRoleStates = (state) => {
  state.isAdmin = false;
  state.isStudent = false;
  state.isTutor = false;
};

// Helper function to set role states based on user type
const setRoleStates = (state, userType) => {
  // Reset all roles first
  resetRoleStates(state);

  // Set the appropriate role based on user type
  switch (userType?.toLowerCase()) {
    case 'admin':
      state.isAdmin = true;
      break;
    case 'student':
      state.isStudent = true;
      break;
    case 'tutor':
      state.isTutor = true;
      break;
    default:
      // If no valid role found, keep all roles as false
      console.warn('Unknown user type:', userType);
  }
};

const initialState = {
  isAuth: false,
  isAdmin: false,
  isStudent: false,
  isTutor: false,
  user: null,
  token: null,
  isLoading: false,
  error: null,
  goBackStep: 'login-role', // or 'register-role' depending on the page
};

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    loginStart: (state) => {
      state.isLoading = true;
      state.error = null;
    },
    loginSuccess: (state, action) => {
      state.isLoading = false;
      state.isAuth = true;
      state.user = action.payload.user;
      state.token = action.payload.token;
      state.error = null;

      // Set role-based states automatically from login response
      const userRole = action.payload.user?.selectedRole || action.payload.user?.role || action.payload.user?.type;
      setRoleStates(state, userRole);

      // Set token and role in cookies with expiration from response
      setAuthTokenCookie(action.payload.token, action.payload.token_expires, userRole);
    },
    loginFailure: (state, action) => {
      state.isLoading = false;
      state.error = action.payload;
    },
    logout: (state) => {
      // Reset auth state
      state.user = null;
      state.token = null;
      state.error = null;
      state.isAuth = false;

      // Reset all role states
      resetRoleStates(state);

      // Clear auth cookie
      removeAuthTokenCookie();

      // Clear localStorage items
      if (typeof window !== 'undefined') {
        localStorage.removeItem('persist:auth');
        localStorage.removeItem('userId');
        localStorage.removeItem('password');
        localStorage.removeItem('selectedRole');

        // Navigate to login page
        window.location.href = '/login';
      }
    },
    // Handle automatic logout from token expiration
    forceLogout: (state) => {
      // Reset auth state
      state.user = null;
      state.token = null;
      state.error = null;
      state.isAuth = false;

      // Reset all role states
      resetRoleStates(state);

      // Clear auth cookie
      removeAuthTokenCookie();

      // Clear localStorage items
      if (typeof window !== 'undefined') {
        localStorage.removeItem('persist:auth');
        localStorage.removeItem('userId');
        localStorage.removeItem('password');
        localStorage.removeItem('selectedRole');
      }
    },
    setGoBackStep: (state, action) => {
      state.goBackStep = action.payload;
    },
    // Update user role states (useful for role changes during session)
    updateUserRole: (state, action) => {
      const newRole = action.payload;
      setRoleStates(state, newRole);

      // Update user object if it exists
      if (state.user) {
        state.user.type = newRole;
        state.user.role = newRole;
      }
    },
    // Manually restore role states from user data (useful for debugging or manual fixes)
    restoreRoleStates: (state) => {
      if (state.user && state.isAuth) {
        const userRole = state.user?.selectedRole || state.user?.role || state.user?.type;
        if (userRole) {
          setRoleStates(state, userRole);
        }
      }
    },
  },
  extraReducers: (builder) => {
    builder.addCase(REHYDRATE, (state, action) => {
      // Handle rehydration from persist
      if (action.payload && action.payload.auth) {
        const persistedAuth = action.payload.auth;

        // If we have a persisted user but role states are not set correctly,
        // restore them from the user data
        if (persistedAuth.user && persistedAuth.isAuth) {
          const userRole = persistedAuth.user?.selectedRole ||
                          persistedAuth.user?.role ||
                          persistedAuth.user?.type;

          if (userRole) {
            // Ensure role states are correctly set after rehydration
            setRoleStates(state, userRole);
          }
        }
      }
    });
  },
});

export const {
  loginStart,
  loginSuccess,
  loginFailure,
  logout,
  forceLogout,
  setGoBackStep,
  updateUserRole,
  restoreRoleStates
} = authSlice.actions;
export default authSlice.reducer;

// Thunk for handling login
export const loginUser = (credentials) => async (dispatch) => {
  try {
    dispatch(loginStart());
    const response = await api.post('/login', credentials);

    dispatch(
      loginSuccess({
        user: response.user,
        token: response.token,
        token_expires: response.token_expires,
      })
    );
    return response;
  } catch (error) {
    dispatch(loginFailure(error?.response?.data?.message || 'Login failed'));
    throw error;
  }
};

// Thunk for handling logout
export const logoutUser = () => async (dispatch) => {
  try {
    // Call logout API
    await api.post('/auth/logout');

    // Clear local state after successful API call
    dispatch(logout());
    return true;
  } catch (error) {
    // Even if API call fails, still clear local state
    console.error('Logout API failed:', error);
    dispatch(logout());
    return false;
  }
};
