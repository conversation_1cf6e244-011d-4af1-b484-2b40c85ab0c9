'use client';

import React, { useState, useRef, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  setSubject,
  setMessage,
  selectHasGreeting,
} from '@/store/features/diarySlice';
import { formatDate } from '@/utils/dateFormatter';
import FeedbackViewModal from './FeedbackViewModal';
import Image from 'next/image';

import { useSearchParams } from 'next/navigation';
import Button from '@/components/Button';

const DiaryForm = ({
  today,
  todayEntry,
  subject,
  message,
  wordCount,
  selectedStage,
  validationErrors = {},
  fetchTodayEntry,
  nextStage,
  handleStageChange,
  isEditable,
  setIsEdit,
  isEdit,
  handleSave,
  isSaving,
  setIsMessageModalOpen,
}) => {
  const dispatch = useDispatch();
  const textareaRef = useRef(null);
  const showReview = useSearchParams().get('review');
  const isDisabled = todayEntry?.hasGreeting === false;
  const CorrectionData = todayEntry?.correction;
  const [showFeedbackModal, setShowFeedbackModal] = useState(
    showReview === 'true' ? true : false
  );

  const [showValidationErrors, setShowValidationErrors] = useState(false);

  // Auto-focus textarea when isEditable is true
  useEffect(() => {
    if (isEditable && textareaRef.current) {
      textareaRef.current.focus();
      // Set cursor to end of text
      const length = textareaRef.current.value.length;
      textareaRef.current.setSelectionRange(length, length);
    }
  }, [isEditable]);

  // Determine if we should show the history button
  // Show history button if entry exists and has been updated or has certain statuses

  // console.log(todayEntry, 'correction data');
  // Function to handle message changes with word limit validation
  const handleMessageChange = (e) => {
    // If hasGreeting is false, open the modal and prevent input
    if (todayEntry?.hasGreeting === false) {
      // We need to set isMessageModalOpen to true in the parent component
      // onClose is actually closing the modal, not opening it
      window.dispatchEvent(new CustomEvent('openGreetingModal'));
      return;
    }

    const newText = e.target.value;
    const wordLimit = selectedStage?.wordLimit || 50;

    // Count words in the new text
    const wordCount =
      newText.trim() === '' ? 0 : newText.trim().split(/\s+/).length;

    // If word count exceeds limit, prevent the change
    if (wordCount > wordLimit) {
      return; // Don't update the message
    }

    dispatch(setMessage(newText));
  };

  return (
    <>
      <div className="flex flex-col h-full">
        <div
          className={`${CorrectionData ? 'min-h-1/2' : 'h-full'} pb-2 relative`}
        >
          <div className="bg-white rounded-xl shadow-md border-2 p-2 flex flex-col h-full">
            {todayEntry?.hasGreeting === false && (
              <div className="mb-4 p-3 rounded-md text-sm bg-[#FFD3D8]">
                <p className={isDisabled ? 'text-red-800' : 'text-yellow-800'}>
                  <span className="font-semibold">Note:</span> Please send a
                  greeting message to your tutor before writing your diary
                  entry.
                </p>
              </div>
            )}

            <div className="mb-4">
              <div
                className={`flex justify-between items-center pb-2 border-b-2 border-dotted ${
                  validationErrors.subject
                    ? 'border-red-300'
                    : 'border-gray-300'
                }`}
              >
                {(todayEntry?.status === 'submit' && isEditable) ||
                (!todayEntry?.isResubmission && isEditable) ||
                (todayEntry?.status === 'new' && isEditable) ? (
                  <input
                    type="text"
                    spellCheck="false"
                    autoComplete="off"
                    autoCorrect="off"
                    autoCapitalize="off"
                    data-gramm="false"
                    data-gramm_editor="false"
                    data-enable-grammarly="false"
                    data-grammarly-disable="true"
                    data-lt-disable="true"
                    data-pwa-disable="true"
                    data-ginger-disable="true"
                    data-whitesmoke-disable="true"
                    data-ms-editor="false"
                    data-webkit-grammar-checking="false"
                    data-moz-spellcheck="false"
                    data-spell-check="false"
                    data-grammar-check="false"
                    translate="no"
                    placeholder="Write subject here..."
                    value={subject}
                    onChange={(e) => {
                      if (todayEntry?.status !== 'submit' && isEditable) {
                        if (todayEntry?.hasGreeting === false) {
                          window.dispatchEvent(
                            new CustomEvent('openGreetingModal')
                          );
                          return;
                        }
                        dispatch(setSubject(e.target.value));
                      }
                    }}
                    className={`flex-grow p-0 border-none outline-none text-lg font-medium bg-transparent ${
                      validationErrors.subject ? 'text-red-600' : ''
                    }`}
                  />
                ) : (
                  <p className="text-lg font-medium">{subject}</p>
                )}
                <div className="text-sm text-gray-500 whitespace-nowrap flex items-center">
                  {today}
                </div>
              </div>
              {validationErrors.subject && (
                <p className="text-red-500 text-sm mt-1">
                  {validationErrors.subject}
                </p>
              )}
            </div>

            <div className="flex-grow">
              {(todayEntry?.status === 'submit' && isEditable) ||
              (!todayEntry?.isResubmission && isEditable) ||
              (todayEntry?.status === 'new' && isEditable) ? (
                <textarea
                  ref={textareaRef}
                  placeholder="Write here..."
                  value={message}
                  onChange={handleMessageChange}
                  onFocus={() => setShowValidationErrors(true)}
                  onBlur={() => setShowValidationErrors(false)}
                  onPaste={(e) => e.preventDefault()}
                  // Disable all spell checking and grammar checking
                  spellCheck="false"
                  autoComplete="off"
                  autoCorrect="off"
                  autoCapitalize="off"
                  // Disable Grammarly
                  data-gramm="false"
                  data-gramm_editor="false"
                  data-enable-grammarly="false"
                  data-grammarly-disable="true"
                  // Disable LanguageTool
                  data-lt-disable="true"
                  // Disable ProWritingAid
                  data-pwa-disable="true"
                  // Disable Ginger
                  data-ginger-disable="true"
                  // Disable WhiteSmoke
                  data-whitesmoke-disable="true"
                  // Disable browser's built-in spell checker
                  data-ms-editor="false"
                  data-webkit-grammar-checking="false"
                  data-moz-spellcheck="false"
                  // Additional attributes to prevent extensions
                  data-spell-check="false"
                  data-grammar-check="false"
                  translate="no"
                  // Prevent text selection for some extensions
                  data-testid="no-spell-check"
                  className={`w-full min-h-44 h-full p-3 border rounded-md outline-none ${
                    validationErrors.message
                      ? 'border-red-300 focus:border-red-400 focus:ring-2 focus:ring-red-100'
                      : showValidationErrors &&
                        wordCount >= (selectedStage?.wordLimit || 50)
                      ? 'border-red-400 focus:border-red-500 focus:ring-2 focus:ring-red-100'
                      : 'border-gray-300 focus:border-blue-400 focus:ring-2 focus:ring-blue-100'
                  }`}
                />
              ) : (
                <p className="w-full min-h-44 h-full p-3 rounded">{message}</p>
              )}
            </div>
            <div className="flex justify-between items-center text-sm mt-2">
              {validationErrors.message && (
                <p className="text-red-500">{validationErrors.message}</p>
              )}
              <div
                className={`${validationErrors.message ? 'ml-auto' : ''} ${
                  wordCount >= selectedStage?.wordLimit
                    ? 'text-red-600 font-medium'
                    : showValidationErrors &&
                      wordCount >= selectedStage?.wordLimit * 0.9
                    ? 'text-orange-500 font-medium'
                    : 'text-gray-500'
                }`}
              >
                {wordCount} / {selectedStage?.wordLimit} words
              </div>
              <div className="flex items-center gap-2 ml-auto">
                {/* Action Buttons */}
                {(todayEntry?.status !== 'submit' ||
                  todayEntry?.isResubmission) && (
                  <>
                    {!isEdit && (
                      <button
                        onClick={() => setIsEdit(!isEdit)}
                        className="text-black font-medium py-1 px-4 text-center rounded-full border-2 transition-all duration-300 bg-gradient-to-b from-yellow-300 to-yellow-500 hover:from-yellow-400 hover:to-yellow-600 shadow-[0px_4px_6px_-1px_rgba(0,0,0,0.1),0px_2px_4px_-2px_rgba(0,0,0,0.1),-4px_8px_12px_0px_#00000026] border-yellow-100 whitespace-nowrap relative ring-2 ring-[#A36105] text-sm"
                      >
                        Edit
                      </button>
                    )}
                    {isEdit &&
                      (todayEntry?.hasGreeting === false ? (
                        <button
                          onClick={() => setIsMessageModalOpen(true)}
                          className="text-black font-medium py-1 px-4 text-center rounded-full whitespace-nowrap border-2 border-yellow-100 shadow-[0px_4px_6px_-1px_rgba(0,0,0,0.1),0px_2px_4px_-2px_rgba(0,0,0,0.1),-4px_8px_12px_0px_#00000026] transition-all duration-300 bg-gradient-to-b from-yellow-300 to-yellow-500 hover:from-yellow-400 hover:to-yellow-600 relative ring-2 ring-[#A36105] text-sm"
                        >
                          Continue
                        </button>
                      ) : (
                        <button
                          onClick={handleSave}
                          disabled={isSaving}
                          className={`text-black font-medium py-1 px-4 text-center rounded-full whitespace-nowrap border-2 border-yellow-100 shadow-[0px_4px_6px_-1px_rgba(0,0,0,0.1),0px_2px_4px_-2px_rgba(0,0,0,0.1),-4px_8px_12px_0px_#00000026] transition-all duration-300 bg-gradient-to-b from-yellow-300 to-yellow-500 hover:from-yellow-400 hover:to-yellow-600 relative ring-2 ring-[#A36105] text-sm ${
                            isSaving
                              ? 'bg-gray-300 cursor-not-allowed'
                              : 'bg-yellow-400 hover:bg-yellow-300'
                          }`}
                        >
                          {todayEntry?.status === 'new'
                            ? isSaving
                              ? 'Submitting...'
                              : 'Submit'
                            : todayEntry?.status === 'submit'
                            ? isSaving
                              ? 'Updating...'
                              : 'Update'
                            : todayEntry?.status === 'reviewed'
                            ? isSaving
                              ? 'Updating...'
                              : 'Update'
                            : 'Submit'}
                        </button>
                      ))}
                  </>
                )}
              </div>
            </div>
            {/* Word limit warning */}
            {showValidationErrors &&
              wordCount >= (selectedStage?.wordLimit || 50) && (
                <div
                  className="mt-2 p-2 bg-red-50 border border-red-200 rounded-md flex items-center gap-2"
                  onMouseDown={(e) => e.preventDefault()} // Prevent focus loss when clicking
                >
                  <p className="text-red-600 text-sm font-semibold">
                    You have reached the word limit of Stage{' '}
                    {selectedStage?.level}.
                  </p>

                  {nextStage?.id !== selectedStage?.id && nextStage && (
                    <Button
                      buttonText={`Upgrade to Stage ${nextStage?.level}`}
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        handleStageChange(nextStage);
                      }}
                      onMouseDown={(e) => e.preventDefault()} // Prevent focus loss
                      type="button"
                    />
                  )}
                </div>
              )}
          </div>
        </div>
        {(CorrectionData || todayEntry?.feedbacks?.length > 0) && (
          <div className="h-full p-3 rounded-md bg-white border shadow-lg custom-scrollbar relative mt-2">
            {CorrectionData ? (
              <div className="h-full">
                <div className="h-full">
                  <p className="text-sm text-[#864D0D] text-center font-medium mb-2">
                    Tutor Review Zone
                  </p>
                  <div className="flex justify-between items-center border-b-2 border-dashed border-gray-300 mb-3">
                    <h3 className="text-lg font-semibold mb-2">
                      {todayEntry?.title}
                    </h3>
                    <div className="flex items-center gap-3 text-sm">
                      {formatDate(todayEntry.entryDate, 'ordinal')}
                    </div>
                  </div>
                  <div
                    className="h-1/2 pb-8"
                    dangerouslySetInnerHTML={{
                      __html: CorrectionData.correctionText || '',
                    }}
                  />
                  {todayEntry?.status === 'reviewed' ||
                    (todayEntry?.isResubmission && (
                      <div className="text-center absolute bottom-2 left-1/2 -translate-x-1/2">
                        <h1 className="text-lg text-[#14AE5C] font-medium">
                          Reviewed
                        </h1>
                      </div>
                    ))}
                </div>
              </div>
            ) : todayEntry?.feedbacks?.length > 0 ? (
              <div className="h-full flex items-center justify-center">
                <p className="text-gray-500 italic">No review available yet.</p>
              </div>
            ) : null}

            {/* Feedback Modal */}
            {showFeedbackModal && (
              <FeedbackViewModal
                isOpen={showFeedbackModal}
                onClose={() => setShowFeedbackModal(false)}
                feedbacks={todayEntry?.feedbacks || []}
              />
            )}

            {todayEntry?.feedbacks?.length > 0 && (
              <div className="absolute right-0 bottom-0">
                <button
                  className=""
                  onClick={() => setShowFeedbackModal(true)}
                  aria-label="View feedback"
                >
                  <Image
                    src="/assets/images/all-img/feedback-bg.png"
                    alt="Feedback"
                    width={50}
                    height={50}
                    // className="w-full h-full object-contain"
                  />
                </button>
              </div>
            )}
          </div>
        )}
      </div>
    </>
  );
};

export default DiaryForm;
