'use client';
import useDataFetch from '@/hooks/useDataFetch';
import { useParams, useRouter } from 'next/navigation';
import React, { useEffect, useState } from 'react';
import SkinPreview from '@/components/skin/SkinPreview';
import { ButtonIcon } from '@/components/Button';
import GoBack from '@/components/shared/GoBack';
import { formatDate } from '@/utils/dateFormatter';
import EssayFeedBackModal from '../../_components/FeedbackModal';
import api from '@/lib/api';
import Tooltip from '@/components/Tooltip';
import {
  selectIsSkinModalOpen,
  setIsSkinModalOpen,
} from '@/store/features/diarySlice';
import SelectSkinModal from '@/app/(main)/diary/_component/SelectSkinModal';
import { useDispatch, useSelector } from 'react-redux';
import { toast } from 'sonner';

const EssayDetails = () => {
  const { id } = useParams();
  const dispatch = useDispatch();
  const [modalData, setModalData] = React.useState(null);
  const [subject, setSubject] = useState('');
  const [content, setContent] = useState('');
  const [isSaving, setIsSaving] = useState(false);
  const [selectedSkin, setSelectedSkin] = useState(null);
  const isSkinModalOpen = useSelector(selectIsSkinModalOpen);

  const { data: essayDetails, isLoading } = useDataFetch({
    queryKey: ['essay-details', id],
    endPoint: `/student-essay/myEssays/${id}`,
    enabled: !!id,
  });

  const latestSubmission =
    essayDetails?.submissionHistory?.[
      essayDetails.submissionHistory.length - 1
    ];
  const submissionMark = latestSubmission?.submissionMark;
  const isEditable = essayDetails?.status !== 'submitted';

  useEffect(() => {
    setSelectedSkin(essayDetails?.diarySkin);
    setSubject(essayDetails?.title);
    setContent(latestSubmission?.content);
  }, [essayDetails]);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-20">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-yellow-500"></div>
      </div>
    );
  }

  const handleUpdate = async () => {
    if (!id) return;

    try {
      setIsSaving(true);
      const payload = {
        submissionId: latestSubmission?.id,
        skinId: selectedSkin?.id || null,
        title: subject || '',
        content: content || '',
      };

      const response = await api.post(
        '/student-essay/submit/essay/update',
        payload
      );
      console.log(response);
    } catch (error) {
      console.error('error:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleSkinChange = async (newSkin) => {
    if (!newSkin.templateContent) {
      toast.error('This skin has no valid template content');
      return;
    }

    try {
      // Update both skin states
      setSelectedSkin(newSkin);

      // Close the modal
      dispatch(setIsSkinModalOpen(false));

      toast.success(`Skin "${newSkin.name}" applied successfully`);
    } catch (error) {
      console.error('Error applying skin template:', error);
      toast.error('Failed to apply skin template');
    }
  };

  return (
    <div className="max-w-7xl mx-auto p-5 xl:px-0 mb-8 relative">
      <GoBack title={'Essay Details'} linkClass="my-5 w-full max-w-52" />

      {/* Main Content */}
      <div className="p-2 bg-[#FDE7E9] rounded grid grid-cols-1 lg:grid-cols-2 gap-3 h-full">
        {/* Left Side - Skin Preview */}
        <div className="bg-white rounded-lg text-center flex items-center shadow-lg">
          <div className="relative py-4 space-y-5">
            <div>
              {essayDetails?.diarySkin ? (
                <div className="">
                  <SkinPreview
                    skin={
                      selectedSkin
                        ? selectedSkin?.templateContent
                        : essayDetails.diarySkin.templateContent
                    }
                    contentData={{
                      subject: subject || essayDetails.title,
                      body:
                        content ||
                        latestSubmission?.content ||
                        'No content available',
                      date: formatDate(
                        latestSubmission?.submissionDate ||
                          essayDetails.createdAt
                      ),
                    }}
                  />
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <p>No skin applied to this essay</p>
                </div>
              )}
            </div>

            {/* {isEditable && (
              <button
                onClick={handleUpdate}
                disabled={isSaving}
                className={`text-black font-medium py-2 px-8 text-center rounded-full whitespace-nowrap
                border-2 border-yellow-100
                shadow-[0px_4px_6px_-1px_rgba(0,0,0,0.1),0px_2px_4px_-2px_rgba(0,0,0,0.1),-4px_8px_12px_0px_#00000026]
                transition-all duration-300
                bg-gradient-to-b from-yellow-300 to-yellow-500 hover:from-yellow-400 hover:to-yellow-600
                relative pr-8
                ring-2 ring-[#A36105] ${
                  isSaving
                    ? 'bg-gray-300 cursor-not-allowed'
                    : 'bg-yellow-400 hover:bg-yellow-300'
                }`}
              >
                {isSaving ? 'Updating...' : 'Update'}
              </button>
            )} */}
          </div>
        </div>

        {/* Right Side - Essay Content & Details */}
        <div className="space-y-6">
          {/* Essay Content */}
          <div className="bg-white rounded-lg shadow-lg">
            {latestSubmission ? (
              <div className="space-y-4">
                <div className="border rounded-lg p-4 bg-white">
                  <div className="flex justify-between items-center mb-3 border-b pb-3">
                    {isEditable ? (
                      <input
                        type="text"
                        value={subject}
                        onChange={(e) => setSubject(e.target.value)}
                        placeholder="Enter essay title..."
                        className="p-2 mr-2 w-full rounded border border-gray-200 focus:outline-[1px] focus:outline-gray-200"
                      />
                    ) : (
                      <h3>{essayDetails.title}</h3>
                    )}
                    <span className="text-sm text-gray-500">
                      {formatDate(latestSubmission.submissionDate)}
                    </span>
                  </div>

                  {isEditable ? (
                    <textarea
                      value={content}
                      onChange={(e) => setContent(e.target.value)}
                      placeholder="Essay description"
                      className={`border min-h-52 max-h-60 overflow-y-auto p-2 w-full rounded focus:outline-[1px] focus:outline-gray-400`}
                      rows={4}
                    />
                  ) : (
                    <p className="prose max-w-none text-gray-700 text-sm leading-relaxed min-h-40 max-h-52 overflow-y-auto">
                      {latestSubmission.content}
                    </p>
                  )}
                </div>
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <p>No submission history available</p>
              </div>
            )}
          </div>

          {/* Tutor Feedback */}
          <div className="bg-white rounded-lg shadow-lg relative">
            <div className="space-y-4 p-4">
              <p className="text-sm text-[#864D0D] text-center font-medium">
                Tutor Review Zone
              </p>
              <div className="flex justify-between items-center border-b-2 border-dashed border-gray-300 mb-3">
                <h3 className="mb-2">{essayDetails.title}</h3>
                <div className="flex items-center gap-3 text-sm">
                  {formatDate(
                    latestSubmission?.submissionDate || essayDetails.createdAt
                  )}
                </div>
              </div>

              <div className="min-h-40">
                {submissionMark?.taskRemarks ? (
                  <div
                    className="prose max-w-none text-gray-700 max-h-52 overflow-y-auto"
                    dangerouslySetInnerHTML={{
                      __html: submissionMark.taskRemarks,
                    }}
                  />
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <p>No feedback provided yet.</p>
                  </div>
                )}
              </div>
            </div>

            {/* Feedback Icon */}
            <span className="absolute right-1 bottom-1">
              <ButtonIcon
                icon="tabler:message-2-star"
                innerBtnCls={`h-12 w-12`}
                btnIconCls={`h-5 w-5`}
                onClick={() =>
                  setModalData(
                    submissionMark?.submissionFeedback ||
                      submissionMark?.taskRemarks ||
                      'No feedback provided yet.'
                  )
                }
                aria-label="View Feedback"
              />
            </span>
          </div>
        </div>
      </div>

      {/* Feedback Modal */}
      <EssayFeedBackModal
        isOpen={!!modalData}
        onClose={() => setModalData(null)}
        data={modalData}
        title="Teachers Feedback"
      />

      {isEditable && (
        <>
          <div className="absolute top-28 -right-10">
            <div className="w-8 h-8 cursor-pointer">
              <Tooltip
                content={'Skin'}
                color="user"
                size="lg"
                delay={100}
                className="-ml-3 "
                position="right"
              >
                <ButtonIcon
                  icon={'arcticons:image-combiner'}
                  innerBtnCls="h-12 w-12"
                  btnIconCls="h-5 w-5"
                  aria-label={'Skin'}
                  onClick={() => dispatch(setIsSkinModalOpen(true))}
                />
              </Tooltip>
            </div>
          </div>

          {isSkinModalOpen && (
            <SelectSkinModal
              isOpen={isSkinModalOpen}
              onClose={() => dispatch(setIsSkinModalOpen(false))}
              onApply={handleSkinChange}
              currentSkinId={selectedSkin?.id || essayDetails?.diarySkin?.id}
            />
          )}
        </>
      )}
    </div>
  );
};

export default EssayDetails;
