'use client';
import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { Icon } from '@iconify/react';
import Button from '@/components/Button';
import { useMutation, useQueryClient, useQuery } from '@tanstack/react-query';
import api from '@/lib/api';

const EditBlockPlay = () => {
  const router = useRouter();
  const params = useParams();
  const queryClient = useQueryClient();
  const blockId = params.id;

  // Form state
  const [formData, setFormData] = useState({
    title: '',
    totalScore: '',
    sentences: []
  });

  // Fetch existing block data
  const { data: blockData, isLoading: isLoadingBlock } = useQuery({
    queryKey: ['block-game', blockId],
    queryFn: async () => {
      const response = await api.get(`/play/block/admin/games/${blockId}`);
      return response.data;
    },
    enabled: !!blockId
  });

  // Populate form when data is loaded
  useEffect(() => {
    if (blockData) {
      setFormData({
        title: blockData.title || '',
        totalScore: blockData.score?.toString() || '',
        sentences: blockData.sentences?.map(sentence => ({
          startingPhrase: sentence.starting_part || '',
          expandingPhrase: sentence.expanding_part || ''
        })) || [{ startingPhrase: '', expandingPhrase: '' }]
      });
    }
  }, [blockData]);

  // Add new sentence pair
  const addSentence = () => {
    setFormData(prev => ({
      ...prev,
      sentences: [...prev.sentences, { startingPhrase: '', expandingPhrase: '' }]
    }));
  };

  // Remove sentence pair
  const removeSentence = (index) => {
    if (formData.sentences.length > 1) {
      setFormData(prev => ({
        ...prev,
        sentences: prev.sentences.filter((_, i) => i !== index)
      }));
    }
  };

  // Update sentence data
  const updateSentence = (index, field, value) => {
    setFormData(prev => ({
      ...prev,
      sentences: prev.sentences.map((sentence, i) => 
        i === index ? { ...sentence, [field]: value } : sentence
      )
    }));
  };

  // Handle form field changes
  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Extract all words from starting phrases
  const getStartingWords = () => {
    const allWords = formData.sentences
      .map(sentence => sentence.startingPhrase.trim())
      .filter(phrase => phrase.length > 0)
      .join(' ')
      .split(/\s+/)
      .filter(word => word.length > 0);
    
    return [...new Set(allWords)];
  };

  // Extract all words from expanding phrases
  const getExpandingWords = () => {
    const allWords = formData.sentences
      .map(sentence => sentence.expandingPhrase.trim())
      .filter(phrase => phrase.length > 0)
      .join(' ')
      .split(/\s+/)
      .filter(word => word.length > 0);
    
    return [...new Set(allWords)];
  };

  // Update mutation
  const updateBlockPlayMutation = useMutation({
    mutationFn: async (data) => {
      const response = await api.patch(`/play/block/admin/games/${blockId}`, data);
      return response;
    },
    onSuccess: () => {
      queryClient.invalidateQueries(['block-']);
      queryClient.invalidateQueries(['block-game', blockId]);
      router.push('/dashboard/module-management/hec-play/block');
    }
  });

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();
    
    // Validate form
    if (!formData.title.trim()) {
      alert('Please enter a question set title');
      return;
    }
    
    if (!formData.totalScore.trim()) {
      alert('Please enter a total score');
      return;
    }

    // Check if all sentences have both starting and expanding phrases
    const incompleteSentences = formData.sentences.some(
      sentence => !sentence.startingPhrase.trim() || !sentence.expandingPhrase.trim()
    );

    if (incompleteSentences) {
      alert('Please fill in all starting and expanding phrases');
      return;
    }

    // Prepare data for API
    const submitData = {
      title: formData.title,
      score: parseInt(formData.totalScore),
      sentences: formData.sentences.map((sentence, index) => ({
        starting_part: sentence.startingPhrase,
        expanding_part: sentence.expandingPhrase,
        sentence_order: index + 1
      }))
    };

    updateBlockPlayMutation.mutate(submitData);
  };

  if (isLoadingBlock) {
    return (
      <div className="container mx-auto p-6 max-w-4xl">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <Icon icon="lucide:loader-2" className="w-8 h-8 animate-spin mx-auto mb-4" />
            <p className="text-gray-600">Loading block game...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center gap-4 mb-4">
          <button
            onClick={() => router.back()}
            className="flex items-center text-gray-600 hover:text-gray-800"
          >
            <Icon icon="lucide:arrow-left" className="w-5 h-5 mr-2" />
            Back
          </button>
        </div>
        <h1 className="text-2xl font-bold text-gray-900">Edit Block Play</h1>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 space-y-6">
        {/* Question Set Title and Total Score Row */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Question Set Title */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Question Set Title <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              value={formData.title}
              onChange={(e) => handleInputChange('title', e.target.value)}
              placeholder="Block Question Title"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
            />
          </div>

          {/* Total Score */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Total Score <span className="text-red-500">*</span>
            </label>
            <input
              type="number"
              value={formData.totalScore}
              onChange={(e) => handleInputChange('totalScore', e.target.value)}
              placeholder="Write here"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
              min="1"
            />
          </div>
        </div>

        {/* Create Sentence Section */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-4">
            Create Sentence <span className="text-red-500">*</span>
          </label>

          {/* Sentence Pairs */}
          <div className="space-y-4">
            {formData.sentences.map((sentence, index) => (
              <div key={index} className="grid grid-cols-1 md:grid-cols-2 gap-4 items-start">
                {/* Row Number */}
                <div className="md:col-span-2 flex items-center gap-4">
                  <span className="text-sm font-medium text-gray-600 w-6">
                    {index + 1}.
                  </span>
                  <div className="flex-1 grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Starting Phrase */}
                    <input
                      type="text"
                      value={sentence.startingPhrase}
                      onChange={(e) => updateSentence(index, 'startingPhrase', e.target.value)}
                      placeholder="Write Starting Phrase here"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      required
                    />

                    {/* Expanding Phrase */}
                    <input
                      type="text"
                      value={sentence.expandingPhrase}
                      onChange={(e) => updateSentence(index, 'expandingPhrase', e.target.value)}
                      placeholder="Write expanding phrase here"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      required
                    />
                  </div>

                  {/* Delete Button */}
                  {formData.sentences.length > 1 && (
                    <button
                      type="button"
                      onClick={() => removeSentence(index)}
                      className="text-red-500 hover:text-red-700 p-1"
                    >
                      <Icon icon="lucide:trash-2" className="w-5 h-5" />
                    </button>
                  )}
                </div>
              </div>
            ))}
          </div>

          {/* Add More Button */}
          <div className="mt-4">
            <button
              type="button"
              onClick={addSentence}
              className="w-full md:w-auto px-6 py-2 bg-yellow-300 hover:bg-yellow-400 text-gray-800 rounded-md font-medium transition-colors"
            >
              Add More
            </button>
          </div>

          {/* Word Blocks Section */}
          {(getStartingWords().length > 0 || getExpandingWords().length > 0) && (
            <div className="mt-8 pt-6 border-t border-gray-200">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Starting Blocks */}
                <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                  <h3 className="text-lg font-semibold text-gray-800 mb-4">Starting Blocks</h3>
                  <div className="flex flex-wrap gap-2">
                    {getStartingWords().map((word, index) => (
                      <span
                        key={`starting-${index}`}
                        className="inline-block px-3 py-2 bg-white border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-100 transition-colors shadow-sm"
                      >
                        {word}
                      </span>
                    ))}
                  </div>
                </div>

                {/* Expanding Blocks */}
                <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                  <h3 className="text-lg font-semibold text-gray-800 mb-4">Expanding Blocks</h3>
                  <div className="flex flex-wrap gap-2">
                    {getExpandingWords().map((word, index) => (
                      <span
                        key={`expanding-${index}`}
                        className="inline-block px-3 py-2 bg-white border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-100 transition-colors shadow-sm"
                      >
                        {word}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Submit Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 pt-6">
          <Button
            type="submit"
            buttonText={updateBlockPlayMutation.isPending ? "Updating..." : "Update Block Play"}
            disabled={updateBlockPlayMutation.isPending}
            className="bg-blue-600 hover:bg-blue-700 text-white"
          />
          
          <Button
            type="button"
            buttonText="Cancel"
            onClick={() => router.back()}
            className="bg-gray-200 hover:bg-gray-300 text-gray-800"
          />
        </div>
      </form>
    </div>
  );
};

export default EditBlockPlay;
