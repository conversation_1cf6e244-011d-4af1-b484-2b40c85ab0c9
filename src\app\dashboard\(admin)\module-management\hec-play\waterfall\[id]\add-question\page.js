'use client';
import React, { useRef, useState, useEffect } from 'react';
import SimpleTiptapEditor from '@/components/form/SimpleTiptapEditor';
import { Icon } from '@iconify/react';
import { motion } from 'framer-motion';
import { useSelector, useDispatch } from 'react-redux';
import {
  addBlank,
  removeBlank,
  setEditorContent,
  setPoints,
  selectBlanks,
  selectEditorContent,
  selectPoints,
  resetQuestionState,
} from '@/store/features/hecPlaySlice';
import Button from '../../../_components/Button';
import BlankAnswers from '../../../_components/BlankAnswers';
import { useParams, useRouter } from 'next/navigation';
import api from '@/lib/api';
import WordBlockInput from '@/components/WordBlockInput';
import { Editor } from '@tinymce/tinymce-react';
import { Form, Formik } from 'formik';
import * as Yup from 'yup';
import FormSelect from '@/components/form/FormSelect';
import FormInput from '@/components/form/FormInput';
import DurationPicker from '@/components/form/DurationPicker';

const validationSchema = Yup.object().shape({
  time_limit_in_seconds: Yup.number()
    .min(1, 'Duration must be at least 1 second')
    .required('Time limit is required'),
  level: Yup.string().required('Level is required'),
});

// previous correct answer state update. 

// Main component using Redux
const QuestionCreatorPage = () => {
  const { id } = useParams();
  const editorRef = useRef(null);
  const dispatch = useDispatch();
  const router = useRouter();
  const blanks = useSelector(selectBlanks);
  const editorContent = useSelector(selectEditorContent);
  const points = useSelector(selectPoints);
  const [blankCounter, setBlankCounter] = useState(1);
  const [rawText, setRawText] = useState('');
  const [blocks, setBlocks] = useState([]);
  const [validationErrors, setValidationErrors] = useState({});

  // Validation function to check for duplicates between blanks and blocks
  const validateBlocksAndBlanks = (newBlocks, currentBlanks) => {
    const errors = {};
    const blankAnswers = currentBlanks.map(blank => blank.answer?.toLowerCase().trim()).filter(Boolean);
    const duplicates = [];

    newBlocks.forEach((block, index) => {
      const blockValue = block?.toLowerCase().trim();
      if (blockValue && blankAnswers.includes(blockValue)) {
        duplicates.push({ index, value: block });
      }
    });

    if (duplicates.length > 0) {
      errors.blocks = `The following words are already used as correct answers: ${duplicates.map(d => d.value).join(', ')}`;
    }

    return { isValid: duplicates.length === 0, errors, duplicates };
  };

  const handleBlocksChange = (newBlocks) => {
    const validation = validateBlocksAndBlanks(newBlocks, blanks);

    if (validation.isValid) {
      setBlocks(newBlocks);
      setValidationErrors(prev => ({ ...prev, blocks: null }));
    } else {
      setBlocks(newBlocks); // Still update the blocks to show user input
      setValidationErrors(prev => ({ ...prev, blocks: validation.errors.blocks }));
    }
  };

  // Validate blocks when blanks change
  useEffect(() => {
    if (blocks.length > 0) {
      const validation = validateBlocksAndBlanks(blocks, blanks);
      if (!validation.isValid) {
        setValidationErrors(prev => ({ ...prev, blocks: validation.errors.blocks }));
      } else {
        setValidationErrors(prev => ({ ...prev, blocks: null }));
      }
    }
  }, [blanks, blocks]);
  // console.log(rawText);

  const handleAddBlank = () => {
    if (editorRef.current) {
      const editor = editorRef.current;
      // Get the current selection/cursor position (not used but kept for clarity)

      // Create the blank node
      const blankId = `blank-${blankCounter}`;
      const blankNode = `<span class="blank-highlight" contenteditable="false" data-blank-id="${blankId}">___<span class="blank-remove" contenteditable="false">×</span></span>`;

      // Get current cursor position
      const selection = editor.selection.getBookmark();

      // Insert at current cursor position
      editor.insertContent(blankNode);

      // Get the updated content as plain text with cursor position restored
      editor.selection.moveToBookmark(selection);

      // Update the raw text with [[gap]] markers
      const updatedContent = editor.getContent({ format: 'text' });
      // Replace "___×" with "[[gap]]" and remove any extra newlines
      const processedText = updatedContent.replace(
        /___[\s\n]*×[\s\n]*/g,
        '[[gap]]'
      );
      setRawText(processedText);

      // Focus back on the editor
      editor.focus();

      // Add blank to state
      dispatch(addBlank(blankId));
      setBlankCounter((prev) => prev + 1);
    }
  };

  const handleEditorChange = (content) => {
    dispatch(setEditorContent(content));

    // Update raw text whenever editor content changes
    if (editorRef.current) {
      const editor = editorRef.current;
      const plainText = editor.getContent({ format: 'text' });
      // Replace "___×" with "[[gap]]" and remove any extra newlines
      const processedText = plainText.replace(/___[\s\n]*×[\s\n]*/g, '[[gap]]');
      setRawText(processedText);
    }
  };

  // Effect to handle synchronization when blanks are removed from the answer section
  useEffect(() => {
    // This effect will run when the blanks array changes
    if (editorRef.current) {
      const editor = editorRef.current;
      const allBlankElements = editor.dom.select('.blank-highlight');

      // Check each blank element in the editor
      // Use for...of loop instead of forEach to avoid type issues
      for (const element of Array.from(allBlankElements)) {
        const blankId = element.getAttribute('data-blank-id');
        // If this blank ID is not in the blanks array, remove it from the editor
        if (blankId && !blanks.some((blank) => blank.id === blankId)) {
          editor.dom.remove(element);
        }
      }
    }
  }, [blanks]);

  const handleCreateQuestion = async (values) => {
    // Validate blocks and blanks before submission
    const validation = validateBlocksAndBlanks(blocks, blanks);
    if (!validation.isValid) {
      setValidationErrors(prev => ({ ...prev, blocks: validation.errors.blocks }));
      alert('Please fix validation errors before submitting.');
      return;
    }

    // Check if we have at least one blank answer
    if (blanks.length === 0) {
      alert('Please add at least one blank answer.');
      return;
    }

    // Check if we have at least one block (wrong answer)
    if (blocks.length === 0) {
      alert('Please add at least one block (wrong answer option).');
      return;
    }

    const answers = blanks.map((blank) => blank.answer);
    try {
      // Ensure we have the latest raw text with [[gap]] markers
      let currentRawText = rawText;
      if (editorRef.current) {
        const plainText = editorRef.current.getContent({ format: 'text' });
        // Replace "___×" with "[[gap]]" and remove any extra newlines
        currentRawText = plainText.replace(/___[\s\n]*×[\s\n]*/g, '[[gap]]');
      }

      const response = await api.post(
        `/play/waterfall/admin/sets/${id}/question`,
        {
          question: {
            question_text: editorContent,
            question_text_plain: currentRawText,
            correct_answers: answers,
            options: Object.values(blocks),
            time_limit_in_seconds: values.time_limit_in_seconds,
            level: values.level,
          },
        }
      );
      console.log(response);

      dispatch(resetQuestionState());
      setBlocks([]);
      dispatch(setEditorContent(''));
      router.push(`/dashboard/module-management/hec-play/waterfall/${id}`);
    } catch (error) {
      console.log(error);
    }
  };

  const handleCancel = () => {
    dispatch(resetQuestionState());
    setBlocks([]);
    dispatch(setEditorContent(''));
    router.push(`/dashboard/module-management/hec-play/waterfall/${id}`);
  };

  return (
    <motion.div
      className=""
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: 0.2 }}
    >
      <div>
        <h1 className="text-xl font-semibold text-gray-900 mb-4">
          Create Question
        </h1>
      </div>

      <div className="bg-white rounded-lg shadow-lg border p-5 relative">
        <div className="bg-gray-100 p-5 rounded-lg">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-medium text-gray-800">
              Write question with blank
            </h2>
          </div>

          <div className="mb-4 relative">
            <Editor
              apiKey={
                process.env.NEXT_TINYMCE_TOKEN ||
                '0f5xprs4e8mmep1mgpa5ljg8ne5oe0dhcgaf8dl41ub0j3wh'
              }
              onInit={(_, editor) => (editorRef.current = editor)}
              init={{
                height: 200,
                menubar: false,
                plugins: [' autoresize'],
                toolbar: 'bold italic underline | formatselect | link',
                content_style: `
                body { font-family: Inter, -apple-system, BlinkMacSystemFont, sans-serif; font-size: 14px; }
                .blank-highlight {
                  background-color: #FFF8E0;
                  padding: 2px 6px;
                  border-radius: 4px;
                  margin: 0 3px;
                  display: inline-flex;
                  align-items: center;
                  border: 1px dashed #F59E0B;
                  cursor: default;
                  user-select: none;
                  font-weight: bold;
                  letter-spacing: 2px;
                }
                .blank-highlight:hover {
                  background-color: #FFE4A0;
                }
                .blank-remove {
                  margin-left: 4px;
                  cursor: pointer;
                  color: #EF4444;
                  font-weight: bold;
                  background-color: rgba(239, 68, 68, 0.1);
                  border-radius: 50%;
                  width: 16px;
                  height: 16px;
                  display: inline-flex;
                  align-items: center;
                  justify-content: center;
                }
                .blank-remove:hover {
                  background-color: rgba(239, 68, 68, 0.2);
                }
              `,
                setup: (editor) => {
                  // Store a reference to the removeBlank function
                  const handleRemoveBlank = (id) => {
                    console.log('Removing blank with ID:', id);
                    dispatch(removeBlank(id));

                    // Update raw text after removing a blank
                    setTimeout(() => {
                      const plainText = editor.getContent({
                        format: 'text',
                      });
                      // Replace "___×" with "[[gap]]" and remove any extra newlines
                      const processedText = plainText.replace(
                        /___[\s\n]*×[\s\n]*/g,
                        '[[gap]]'
                      );
                      setRawText(processedText);
                    }, 0);
                  };

                  // Handle clicks on the remove button
                  editor.on('click', (e) => {
                    const target = e.target;
                    if (target.className === 'blank-remove') {
                      const blankElement = target.closest('.blank-highlight');
                      if (blankElement) {
                        const blankId =
                          blankElement.getAttribute('data-blank-id');
                        if (blankId) {
                          // Remove the blank element from the editor
                          editor.dom.remove(blankElement);
                          // Remove the blank from the state to update the answer section
                          handleRemoveBlank(blankId);
                        }
                      }
                    }
                  });

                  // Prevent editing of blank elements
                  editor.on('BeforeSetContent', (e) => {
                    // This event fires before content is set in the editor
                    if (
                      e.content &&
                      e.content.indexOf('blank-highlight') !== -1
                    ) {
                      // If the content contains a blank, make sure it's not editable
                      e.content = e.content.replace(
                        /<span class="blank-highlight"/g,
                        '<span class="blank-highlight" contenteditable="false"'
                      );
                    }
                  });

                  // Prevent selection inside blank elements
                  editor.on('NodeChange', (e) => {
                    const node = e.element;
                    if (
                      node &&
                      (node.className === 'blank-highlight' ||
                        (node.parentNode &&
                          node.parentNode.className === 'blank-highlight'))
                    ) {
                      // If the cursor is inside a blank element, move it outside
                      const blankElement =
                        node.className === 'blank-highlight'
                          ? node
                          : node.parentNode;

                      // Place cursor after the blank element
                      const range = editor.selection.getRng();
                      range.setStartAfter(blankElement);
                      range.setEndAfter(blankElement);
                      editor.selection.setRng(range);
                    }
                  });
                },
              }}
              onEditorChange={handleEditorChange}
            />

            <div className="absolute w-full h-[27px] rounded-b-lg bottom-0 bg-white border-[2px] border-t-0 z-10"></div>

            <div className="flex justify-end absolute right-2 top-1.5 z-10">
              <button
                onClick={handleAddBlank}
                className="flex items-center gap-2 bg-[#FFDE34] text-black text-base rounded px-6 py-1.5 hover:bg-yellow-300 focus:outline-none focus:ring-2 focus:ring-yellow-400"
              >
                Add Blank
              </button>
            </div>
          </div>

          {blanks.length > 0 && <BlankAnswers />}

          <div className="mt-4">
            <WordBlockInput
              label="Blocks"
              required
              value={blocks}
              onChange={handleBlocksChange}
            />
            {validationErrors.blocks && (
              <div className="mt-2 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md p-3">
                <div className="flex items-start">
                  <Icon
                    icon="heroicons:exclamation-triangle"
                    className="w-4 h-4 text-red-500 mt-0.5 mr-2 flex-shrink-0"
                  />
                  <div>
                    <p className="font-medium">Duplicate words detected:</p>
                    <p>{validationErrors.blocks}</p>
                    <p className="mt-1 text-xs text-red-500">
                      Please use different words for blocks (wrong answers) than the ones used in blanks (correct answers).
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        <Formik
          initialValues={{ time_limit_in_seconds: 60, level: '' }}
          validationSchema={validationSchema}
          onSubmit={handleCreateQuestion}
        >
          {({ isSubmitting, errors }) => (
            <Form className="space-y-6">
              {/* Question Settings Section */}
              <div className="bg-gray-100 p-5 pt-0 rounded-b-lg space-y-6">
                <h2 className="text-lg font-medium text-gray-800 mb-4">
                  Question Settings
                </h2>

                {/* Level Selection */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <FormSelect
                    name="level"
                    label="Difficulty Level"
                    required
                    options={[
                      { value: '1', label: 'Beginner' },
                      { value: '2', label: 'Intermediate' },
                      { value: '3', label: 'Advanced' },
                    ]}
                    placeholder="Select difficulty level"
                  />
                </div>

                {/* Duration Picker */}
                <div>
                  <DurationPicker
                    name="time_limit_in_seconds"
                    label="Time Limit"
                    required
                  />
                </div>
              </div>

              {/* Form Actions */}
              <div className="flex justify-end space-x-4">
                <button
                  onClick={handleCancel}
                  type="button"
                  className="bg-gray-300 hover:bg-gray-50 text-black font-medium py-2 px-4 rounded"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={isSubmitting || validationErrors.blocks || blanks.length === 0 || blocks.length === 0}
                  className="bg-[#FFDE34] hover:bg-yellow-300 text-black font-medium py-2 px-4 rounded disabled:opacity-50 disabled:cursor-not-allowed"
                  title={
                    validationErrors.blocks
                      ? 'Fix validation errors before submitting'
                      : blanks.length === 0
                      ? 'Add at least one blank answer'
                      : blocks.length === 0
                      ? 'Add at least one block (wrong answer)'
                      : 'Create Question'
                  }
                >
                  {isSubmitting ? 'Creating...' : 'Create Question'}
                </button>
              </div>
            </Form>
          )}
        </Formik>
      </div>
    </motion.div>
  );
};

export default QuestionCreatorPage;
