'use client';

import React, { useState, useEffect } from 'react';
import api from '@/lib/api';
import NewTablePage from '@/components/form/NewTablePage';
import { useRouter } from 'next/navigation';
import { toast } from 'react-toastify';
import { useQuery } from '@tanstack/react-query';
import { useSelector } from 'react-redux';

const QASubmissionsList = () => {
  const router = useRouter();
  const auth = useSelector((state) => state.auth);
  
  // State variables
  const [submissions, setSubmissions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [sortField, setSortField] = useState('createdAt');
  const [sortDirection, setSortDirection] = useState('DESC');
  const [activeTab, setActiveTab] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalItems, setTotalItems] = useState(0);
  const [totalPages, setTotalPages] = useState(1);

  // Tabs configuration
  const tabs = [
    { name: 'Pending Submissions', endpoint: '/tutor/qa/submissions/pending' },
    { name: 'Reviewed Submissions', endpoint: '/tutor/qa/submissions/reviewed' }
  ];

  // Table columns configuration
  const columns = [
    { 
      label: 'SUBMISSION ID', 
      field: 'id',
      
      cellRenderer: (value) => (
        <div className="flex items-center">
          <span className="text-sm font-mono text-gray-600">
            {value?.substring(0, 8)}...
          </span>
        </div>
      )
    },
    { 
      label: 'STATUS', 
      field: 'status',
     
      cellRenderer: (value) => (
        <div className="flex items-center">
          <span className={`px-2 py-1 text-xs font-medium rounded-full ${
            value === 'submitted' ? 'bg-yellow-100 text-yellow-800' :
            value === 'reviewed' ? 'bg-green-100 text-green-800' :
            value === 'pending' ? 'bg-blue-100 text-blue-800' :
            'bg-gray-100 text-gray-800'
          }`}>
            {value?.charAt(0).toUpperCase() + value?.slice(1)}
          </span>
        </div>
      )
    },
  
  ];

  // Handle view submission action
  const handleViewSubmission = (submission) => {
  router.push(`hec-qa/review/${submission.id}?tab=missionQAList`);
};

  // Table actions configuration
  const actions = [
    {
      icon: 'material-symbols:visibility',
      className: 'text-blue-600 hover:text-blue-700',
      onClick: handleViewSubmission,
      tooltip: 'View Submission',
    }
  ];

  // Fetch submissions from API
  const fetchSubmissions = async () => {
    try {
      setLoading(true);
      
      // Construct query parameters
      const params = {
        page: currentPage,
        limit: rowsPerPage,
        sortBy: sortField,
        sortDirection: sortDirection
      };

      const response = await api.get(tabs[activeTab].endpoint, { params });

      if (response?.success) {
        const submissionItems = response.data?.items || [];
        
        const formattedSubmissions = submissionItems.map((submission) => ({
          id: submission.id,
          score: submission.score,
          answer: submission.answer,
          status: submission.status,
          submissionDate: submission.submissionDate,
          feedback: submission.feedback,
          corrections: submission.corrections,
          createdAt: submission.createdAt,
          updatedAt: submission.updatedAt
        }));
        
        setSubmissions(formattedSubmissions);
        setTotalItems(response.data?.totalItems || response.data?.totalCount || 0);
        setTotalPages(response.data?.totalPages || Math.ceil((response.data?.totalCount || 0) / rowsPerPage));
      } else {
        throw new Error(response?.message || 'Failed to fetch submissions');
      }
    } catch (error) {
      console.error('Error fetching submissions:', error);
      toast.error(error.message || 'Error fetching submissions');
      setSubmissions([]);
      setTotalItems(0);
      setTotalPages(1);
    } finally {
      setLoading(false);
    }
  };

  // Use React Query for data fetching
  useQuery({
    queryKey: [
      'qaSubmissions',
      activeTab,
      currentPage,
      rowsPerPage,
      sortField,
      sortDirection
    ],
    queryFn: fetchSubmissions,
  });

  // Reset states when tab changes
  useEffect(() => {
    setCurrentPage(1);
  }, [activeTab]);

  // Handle page change
  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  // Handle sort change
  const handleSort = (field) => {
    if (field === sortField) {
      setSortDirection(sortDirection === 'ASC' ? 'DESC' : 'ASC');
    } else {
      setSortField(field);
      setSortDirection('ASC');
    }
    setCurrentPage(1);
  };

  // Handle tab change
  const handleTabChange = (index) => {
    setActiveTab(index);
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold">Q&A Submissions</h1>
      </div>
      
      {/* Tab navigation */}
      <div className="flex border-b border-gray-200 mb-6">
        {tabs.map((tab, index) => (
          <button
            key={index}
            className={`py-3 px-6 font-medium text-sm focus:outline-none ${
              activeTab === index 
                ? 'text-black border-b-2 border-yellow-500 hover:bg-[#FEFCE8]' 
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => handleTabChange(index)}
          >
            {tab.name}
          </button>
        ))}
      </div>
      
      {/* Table component */}
      <NewTablePage
        title=""
        showCreateButton={false}
        columns={columns}
        actions={actions}
        data={submissions}
        loading={loading}
        currentPage={currentPage}
        totalPages={totalPages}
        changePage={handlePageChange}
        totalItems={totalItems}
        rowsPerPage={rowsPerPage}
        setRowsPerPage={setRowsPerPage}
        showSearch={false}
        showNameFilter={false}
        showSortFilter={false}
        onSort={handleSort}
        sortField={sortField}
        sortDirection={sortDirection}
        showCheckboxes={false}
      />
      
      {/* Empty state */}
      {submissions.length === 0 && !loading && (
        <div className="text-center py-8 text-gray-500">
          No submissions found.
        </div>
      )}
    </div>
  );
};

export default QASubmissionsList;