import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useRouter, usePathname, useSearchParams } from 'next/navigation';
import { useSelector } from 'react-redux';

// Import smaller components
import Logo from './header/Logo';
import MobileMenuToggle from './header/MobileMenuToggle';
import NotificationSection from './header/NotificationSection';
import UserProfile from './header/UserProfile';
import AuthButtons from './header/AuthButtons';
import MobileMenu from './header/MobileMenu';
import DesktopMenu from './header/DesktopMenu';

const Header = () => {
  const { isAuth, user, isStudent, isAdmin, isTutor } = useSelector(
    (state) => state.auth
  );
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  // State management
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [activeMenu, setActiveMenu] = useState(null);
  const [previewImg, setPreviewImg] = useState(null);

  // User plan data
  const currentPlanId = user?.activePlanDetails?.id;
  const activeFeatures = user?.activePlanDetails?.plan?.features;

  // Function to check if a submenu item is active based on the current path
  const isSubmenuItemActive = useCallback(
    (item) => {
      const { path, subPaths = [] } = item;

      // Combine pathname + searchParams (if any)
      const currentFullPath = searchParams.toString()
        ? `${pathname}?${searchParams.toString()}`
        : pathname;

      // 1. Exact match with full path (supports query strings)
      if (currentFullPath === path) return true;

      // 2. Match base path if there's no query
      if (!path.includes('?') && pathname === path) return true;

      // 3. Match subPaths if defined
      if (subPaths.length > 0) {
        return subPaths.some(
          (subPath) =>
            pathname === subPath || pathname.startsWith(subPath + '/')
        );
      }

      return false;
    },
    [pathname, searchParams]
  );

  // Function to check if a parent menu should be active based on its children
  const isParentMenuActive = useCallback(
    (menuItem) => {
      if (!menuItem?.submenu) return false;

      return menuItem.submenu.some((subItem) => {
        const [subItemPath, subItemQuery] = subItem.path.split('?');

        // 1. Exact match (path and query)
        if (pathname === subItemPath) {
          if (!subItemQuery) return true;

          const expectedParams = new URLSearchParams(subItemQuery);
          let allMatch = true;
          expectedParams.forEach((val, key) => {
            if (searchParams.get(key) !== val) {
              allMatch = false;
            }
          });

          if (allMatch) return true;
        }

        // 2. Starts with (for dynamic nested routes)
        if (pathname.startsWith(subItemPath + '/')) return true;

        // 3. subPaths array match
        if (subItem.subPaths?.length) {
          return subItem.subPaths.some(
            (subPath) =>
              pathname === subPath || pathname.startsWith(subPath + '/')
          );
        }

        return false;
      });
    },
    [pathname, searchParams]
  );

  // const profileImage = user?.profilePictureUrl;

  useEffect(() => {
    if (user?.profilePictureUrl) {
      setPreviewImg(user?.profilePictureUrl);
    } else {
      setPreviewImg('/assets/images/all-img/avatar.png');
    }
  }, [user]);

  // console.log(user);

  const getMenuItemsByPlan = (features = []) => {
    const baseMenuItems = [
      {
        name: 'Introduction',
        path: '/',
      },
    ];

    const featureMap = {
      hec_user_diary: {
        name: 'HEC Diary',
        submenu: [
          {
            name: 'My Diary',
            path: '/diary/my',
            subPaths: [
              '/diary',
              '/diary/my',
              '/diary/award',
              '/diary/owned-item',
              '/diary/shared',
              '/diary/mission',
            ],
          },
          { name: 'Mission Diary', path: '/diary-missions' },
          // { name: 'Award', path: '/diary/award' },
          { name: 'Find Friends', path: '/find-friends' },
          { name: 'My Tutor', path: '/tutors?planFeatureType=hec_user_diary' },
        ],
      },
      hec_play: {
        name: 'HEC Play',
        submenu: [
          { name: 'Block', path: '/block' },
          { name: 'Waterfall', path: '/waterfall' },
          { name: 'Story Maker', path: '/story-maker' },
          // { name: 'My Tutor', path: '/tutors?planFeatureType=hec_play' },
        ],
      },
      english_qa_writing: {
        name: 'HEC Q&A',
        submenu: [
          { name: 'My HEC Q&A', path: '/qa' },
          { name: 'Mission Q&A', path: '/hec-mission' },
          {
            name: 'My Tutor',
            path: '/tutors?planFeatureType=english_qa_writing',
          },
        ],
      },
      english_essay: {
        name: 'HEC Essay',
        submenu: [
          { name: 'My HEC Essay', path: '/essay', subPaths: [] },
          {
            name: 'Mission Essay',
            path: '/essay/mission',
            subPaths: ['/essay/mission'],
          },
          { name: 'My Tutor', path: '/tutors?planFeatureType=english_essay' },
        ],
      },
      english_novel: {
        name: 'HEC Novel',
        submenu: [
          { name: 'My HEC Novel', path: '/novel' },
          { name: 'My Tutor', path: '/tutors?planFeatureType=english_novel' },
        ],
      },
    };

    const activeMenus = features
      .filter((item) => featureMap[item.type])
      .map((item) => featureMap[item.type]);

    return [...activeMenus];
  };

  const menuItems = useMemo(() => {
    return isAuth && activeFeatures?.length
      ? getMenuItemsByPlan(activeFeatures)
      : [
          { name: 'Introduction', path: '/' },
          { name: 'About Us', path: '/aboutus' },
          { name: 'Contact Us', path: '/contactus' },
        ];
  }, [isAuth, activeFeatures]);

  // We're removing the auto-opening behavior
  // This effect is intentionally removed to prevent menus from opening automatically

  useEffect(() => {
    const handleOutsideClick = (event) => {
      // Close active menu when clicking outside
      if (
        activeMenu &&
        !event.target.closest('.menu-item') &&
        !event.target.closest('.submenu')
      ) {
        setActiveMenu(null);
      }
    };

    document.addEventListener('click', handleOutsideClick);
    return () => {
      document.removeEventListener('click', handleOutsideClick);
    };
  }, [activeMenu]);

  const handleMenuClick = (menuName) => {
    if (activeMenu === menuName) {
      setActiveMenu(null);
    } else {
      setActiveMenu(menuName);
    }
  };

  const handleMenuItemClick = (path) => {
    setActiveMenu(null);
    router.push(path);
  };

  return (
    <div>
      <div className="fixed top-0 left-0 w-full z-50 shadow-md">
        <header className="bg-[#FFF189] text-black relative">
          <div className="flex justify-between items-center max-w-7xl mx-auto px-5 xl:px-0 relative">
            <div className="flex items-center gap-3">
              <MobileMenuToggle
                isMenuOpen={isMenuOpen}
                setIsMenuOpen={setIsMenuOpen}
              />
              <Logo isAuth={isAuth} isStudent={isStudent} />
            </div>

            <div className="flex items-center sm:space-x-4 relative">
              <NotificationSection isAuth={isAuth} />
              {isAuth ? (
                <UserProfile
                  user={user}
                  previewImg={previewImg}
                  setPreviewImg={setPreviewImg}
                  isAdmin={isAdmin}
                  isTutor={isTutor}
                />
              ) : (
                <AuthButtons />
              )}
            </div>
          </div>
        </header>

        <MobileMenu
          isMenuOpen={isMenuOpen}
          setIsMenuOpen={setIsMenuOpen}
          menuItems={menuItems}
          activeMenu={activeMenu}
          setActiveMenu={setActiveMenu}
          handleMenuClick={handleMenuClick}
          handleMenuItemClick={handleMenuItemClick}
          isParentMenuActive={isParentMenuActive}
          isSubmenuItemActive={isSubmenuItemActive}
          isAuth={isAuth}
          user={user}
          previewImg={previewImg}
          setPreviewImg={setPreviewImg}
          isAdmin={isAdmin}
          isTutor={isTutor}
        />

        {/* Main Menu Bar */}
        {(isStudent || !isAuth) && (
          <DesktopMenu
            menuItems={menuItems}
            activeMenu={activeMenu}
            handleMenuClick={handleMenuClick}
            handleMenuItemClick={handleMenuItemClick}
            isParentMenuActive={isParentMenuActive}
            isSubmenuItemActive={isSubmenuItemActive}
            currentPlanId={currentPlanId}
          />
        )}
      </div>
      <div
        className={(isStudent || !isAuth) ? `h-[52px] lg:h-[101px]` : 'h-[52px] lg:h-[56px]'}
      ></div>
    </div>
  );
};

export default Header;
