'use client';
import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import api from '@/lib/api';
import SentenceBlock from './components/SentenceBlock';
import GameInstructionView from './components/GameInstructionView';
import InitialInstructionsStage from './components/InitialInstructionsStage';
import GameResultsStage from './components/GameResultsStage';
import BlockPlayModal from './components/BlockPlayModal';
// import AchievementNotification from './components/AchievementNotification';

// Game stages enum
const GAME_STAGES = {
  INITIAL_INSTRUCTIONS: 'initial_instructions',
  SENTENCE_BUILDING: 'sentence_building',
  WORD_EXPANSION: 'word_expansion',
  RESULTS: 'results',
};

const BlockSentenceBuilder = () => {
  const [gameData, setGameData] = useState(null);
  const [startingSentences, setStartingSentences] = useState([]);
  const [expandingSentences, setExpandingSentences] = useState([]);
  const [draggedWord, setDraggedWord] = useState(null);
  const [usedWords, setUsedWords] = useState([]);
  const [currentStep, setCurrentStep] = useState(0);
  const [currentStage, setCurrentStage] = useState(
    GAME_STAGES.INITIAL_INSTRUCTIONS
  );
  const [gameResults, setGameResults] = useState(null);
  const [showAchievement, setShowAchievement] = useState(false);
  const [achievementData, setAchievementData] = useState(null);
  const [isGameStarted, setIsGameStarted] = useState(false);
  const [modalStage, setModalStage] = useState('sentence_building'); // 'sentence_building' or 'word_expansion'

  useEffect(() => {
    const fetchData = async () => {
      try {
        const res = await api.get('/play/block/play');
        const sentences = res.data.sentences;
        const start = sentences.map((s) =>
          Array(s.starting_part.split(' ').length).fill(null)
        );
        const expand = sentences.map((s) =>
          Array(s.expanding_part.split(' ').length).fill(null)
        );
        setGameData(res.data);
        setStartingSentences(start);
        setExpandingSentences(expand);
      } catch (err) {
        console.error('Error fetching block data:', err);
      }
    };
    fetchData();
  }, []);

  const handleDragStart = (word, type) => {
    setDraggedWord({ word, type });
  };

  const handleDrop = (type, index, blockIndex) => {
    if (!draggedWord || draggedWord.type !== type) return;
    if (
      type === 'expand' &&
      startingSentences[blockIndex].some((w) => w === null)
    )
      return;
    const sentences =
      type === 'start' ? [...startingSentences] : [...expandingSentences];
    const current = sentences[blockIndex];
    if (!current[index]) {
      current[index] = draggedWord.word;
      if (type === 'start') setStartingSentences(sentences);
      else setExpandingSentences(sentences);
      setUsedWords((prev) => [...prev, draggedWord.word]);
      setDraggedWord(null);
    }
  };

  const removeWord = (index, type, blockIndex) => {
    const sentences =
      type === 'start' ? [...startingSentences] : [...expandingSentences];
    const current = sentences[blockIndex];
    const wordToRemove = current[index];
    current[index] = null;
    if (type === 'start') setStartingSentences(sentences);
    else setExpandingSentences(sentences);
    setUsedWords((prev) => prev.filter((w) => w !== wordToRemove));
  };

  const allComplete = startingSentences.every(
    (s, i) =>
      s.every((w) => w !== null) &&
      expandingSentences[i].every((w) => w !== null)
  );

  const allSentencesBuilt = startingSentences.every((s) =>
    s.every((w) => w !== null)
  );

  // Achievement system
  const showAchievementNotification = (
    title,
    message,
    icon = 'mdi:trophy',
    color = 'text-yellow-600'
  ) => {
    setAchievementData({ title, message, icon, color });
    setShowAchievement(true);
  };

  // const hideAchievementNotification = () => {
  //   setShowAchievement(false);
  //   setAchievementData(null);
  // };

  // Stage transition functions
  const startGame = () => {
    setCurrentStage(GAME_STAGES.SENTENCE_BUILDING);
    // Don't open modal automatically - let user click "Let's Play" button
    // showAchievementNotification(
    //   'Game Started!',
    //   "Let's build some amazing sentences!",
    //   'mdi:rocket-launch',
    //   'text-blue-600'
    // );
  };

  const openGameModal = () => {
    setIsGameStarted(true);
    setModalStage('sentence_building');
  };

  const startWordExpansion = () => {
    setModalStage('word_expansion');
    // showAchievementNotification(
    //   'Sentences Complete!',
    //   'Time to expand your words!',
    //   'mdi:text-box-plus',
    //   'text-green-600'
    // );
  };

  const submitAll = async () => {
    try {
      // Prepare sentence constructions from the completed sentences
      const sentenceConstructions = gameData.sentences.map((_, index) => {
        const startingSentence =
          startingSentences[index]?.filter((word) => word !== null).join(' ') ||
          '';
        const expandingSentence =
          expandingSentences[index]
            ?.filter((word) => word !== null)
            .join(' ') || '';

        return {
          starting_sentence: startingSentence,
          expanding_sentence: expandingSentence,
        };
      });

      const payload = {
        block_game_id: gameData.id,
        sentence_constructions: sentenceConstructions,
      };

      console.log('Submitting payload:', payload);

      const response = await api.post('/play/block/submit', payload);
      console.log('Game submitted successfully:', response.data);

      // Store results and transition to results stage
      setGameResults(response.data);
      setCurrentStage(GAME_STAGES.RESULTS);
      setIsGameStarted(false); // Close the modal

      // Show completion achievement
      showAchievementNotification(
        'Game Complete!',
        'Congratulations! Check your results!',
        'mdi:trophy',
        'text-yellow-600'
      );
    } catch (error) {
      console.error('Error submitting game:', error);
    }
  };

  const restartGame = () => {
    // Reset all game state
    setCurrentStep(0);
    setCurrentStage(GAME_STAGES.INITIAL_INSTRUCTIONS);
    setGameResults(null);
    setUsedWords([]);
    setIsGameStarted(false);
    setModalStage('sentence_building');

    // Reset sentences
    if (gameData) {
      const start = gameData.sentences.map((s) =>
        Array(s.starting_part.split(' ').length).fill(null)
      );
      const expand = gameData.sentences.map((s) =>
        Array(s.expanding_part.split(' ').length).fill(null)
      );
      setStartingSentences(start);
      setExpandingSentences(expand);
    }
  };

  if (!gameData) return <div className="p-6 text-center">Loading...</div>;

  const renderCurrentStage = () => {
    switch (currentStage) {
      case GAME_STAGES.INITIAL_INSTRUCTIONS:
        return (
          <InitialInstructionsStage
            gameData={gameData}
            onStartGame={startGame}
          />
        );

      case GAME_STAGES.SENTENCE_BUILDING:
        return (
          <div className="w-full space-y-6 sm:space-y-8">
            <div className="w-full max-w-5xl mx-auto">
              <GameInstructionView gameData={gameData} />
            </div>
            <div className="text-center">
              <button
                onClick={openGameModal}
                className="bg-gradient-to-b from-yellow-500 to-yellow-400 hover:from-yellow-500 hover:to-yellow-500 text-white font-bold py-3 px-8 sm:py-4 sm:px-12 rounded-full text-lg sm:text-xl transition-all duration-300 flex items-center gap-3 mx-auto"
              >
                Starting now
              </button>
            </div>

            {/* Unified Game Modal */}
            {isGameStarted && (
              <BlockPlayModal
                isOpen={isGameStarted}
                onClose={() => {
                  setIsGameStarted(false);
                  setModalStage('sentence_building');
                }}
                currentStep={modalStage === 'sentence_building' ? currentStep : 0}
                totalSteps={gameData.sentences.length}
                completedSteps={
                  modalStage === 'sentence_building'
                    ? startingSentences.filter(s => s.every(w => w !== null)).length
                    : expandingSentences.filter(s => s.every(w => w !== null)).length
                }
                stageTitle={modalStage === 'sentence_building' ? "Building Sentences" : "Expanding Words"}
                showProgress={true}
              >
                {modalStage === 'sentence_building' ? (
                  <SentenceBlock
                    type="start"
                    show={true}
                    hide={false}
                    currentStep={currentStep}
                    gameData={gameData}
                    startingSentences={startingSentences}
                    expandingSentences={expandingSentences}
                    usedWords={usedWords}
                    handleDragStart={handleDragStart}
                    handleDrop={handleDrop}
                    removeWord={removeWord}
                    onNext={() => {
                      if (currentStep + 1 >= gameData.sentences.length) {
                        startWordExpansion();
                      } else {
                        setCurrentStep((prev) => prev + 1);
                      }
                    }}
                    allSentencesBuilt={allSentencesBuilt}
                    onStartExpansion={startWordExpansion}
                    onSentenceComplete={(sentenceNumber) => {
                      showAchievementNotification(
                        `Sentence ${sentenceNumber} Complete!`,
                        'Great job! Keep building!',
                        'mdi:check-circle',
                        'text-green-600'
                      );
                    }}
                  />
                ) : (
                  <SentenceBlock
                    type="expand"
                    show={true}
                    hide={false}
                    currentStep={currentStep}
                    gameData={gameData}
                    startingSentences={startingSentences}
                    expandingSentences={expandingSentences}
                    usedWords={usedWords}
                    handleDragStart={handleDragStart}
                    handleDrop={handleDrop}
                    removeWord={removeWord}
                    allComplete={allComplete}
                    onSubmit={submitAll}
                    showCompletedSentences={true}
                  />
                )}
              </BlockPlayModal>
            )}
          </div>
        );

      case GAME_STAGES.WORD_EXPANSION:
        // This stage is now handled within the unified modal above
        return (
          <div className="w-full space-y-6 sm:space-y-8">
            <div className="w-full max-w-5xl mx-auto">
              <GameInstructionView gameData={gameData} />
            </div>
            <div className="text-center">
              <p className="text-lg text-gray-600">Word expansion is in progress...</p>
            </div>
          </div>
        );

      case GAME_STAGES.RESULTS:
        return (
          <GameResultsStage
            gameResults={gameResults}
            gameData={gameData}
            startingSentences={startingSentences}
            expandingSentences={expandingSentences}
            onRestart={restartGame}
          />
        );

      default:
        return null;
    }
  };

  return (
    <div className="py-4 sm:py-6 lg:py-10 px-4 sm:px-6 lg:px-8 flex flex-col items-center text-[#5A3D1A] bg-white">
      {/* Achievement Notification */}
      {/* <AchievementNotification
        show={showAchievement}
        title={achievementData?.title}
        message={achievementData?.message}
        icon={achievementData?.icon}
        color={achievementData?.color}
        onComplete={hideAchievementNotification}
      /> */}

      <div className="w-full max-w-7xl mx-auto">
        <AnimatePresence mode="wait">
          <motion.div
            key={currentStage}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.5 }}
            className="w-full flex flex-col items-center"
          >
            {renderCurrentStage()}
          </motion.div>
        </AnimatePresence>
      </div>
    </div>
  );
};

export default BlockSentenceBuilder;
