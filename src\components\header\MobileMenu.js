import React, { useEffect, useRef } from 'react';
import <PERSON> from 'next/link';
import { FiChevronDown } from 'react-icons/fi';
import UserProfile from './UserProfile';
import AuthButtons from './AuthButtons';

const MobileMenu = ({
  isMenuOpen,
  setIsMenuOpen,
  menuItems,
  activeMenu,
  setActiveMenu,
  handleMenuClick,
  handleMenuItemClick,
  isParentMenuActive,
  isSubmenuItemActive,
  isAuth,
  user,
  previewImg,
  setPreviewImg,
  isAdmin,
  isTutor
}) => {
  const mobileMenuRef = useRef(null);

  // Close mobile menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        isMenuOpen &&
        mobileMenuRef.current &&
        !mobileMenuRef.current.contains(event.target) &&
        !event.target.closest('button[aria-label="Toggle Menu"]')
      ) {
        setIsMenuOpen(false);
      }
    };

    if (isMenuOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('touchstart', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('touchstart', handleClickOutside);
    };
  }, [isMenuOpen, setIsMenuOpen]);

  if (!isMenuOpen) return null;

  return (
    <div 
      ref={mobileMenuRef}
      className="lg:hidden absolute top-[53px] left-0 w-full bg-[#FFFAC2] shadow-md p-5 z-50 max-h-[90vh] overflow-y-auto"
    >
      {/* Mobile Menu Items */}
      <div>
        {menuItems.map((item, index) => (
          <div key={index} className="mb-2">
            <button
              className={`w-full text-left py-2 px-3 rounded-md menu-item flex justify-between items-center touch-manipulation ${
                activeMenu === item.name || isParentMenuActive(item)
                  ? 'bg-[#FFE34D] font-bold'
                  : 'bg-[#FFFAC2]'
              } hover:bg-[#FFE34D] active:bg-[#FFE34D] transition-colors duration-200`}
              onClick={() =>
                item.submenu
                  ? handleMenuClick(item.name)
                  : handleMenuItemClick(item.path)
              }
            >
              <span>{item.name}</span>
              {item.submenu && (
                <FiChevronDown
                  className={`transition-transform duration-200 ${
                    activeMenu === item.name ? 'transform rotate-180' : ''
                  }`}
                  size={16}
                />
              )}
            </button>

            {item.submenu && activeMenu === item.name && (
              <>
                {/* Small triangle indicator */}
                <div className="absolute top-0 left-[-5px] transform -translate-y-1/2 w-2 h-2 bg-white border-t border-l border-[#FFE34D] rotate-45 z-40 ml-4"></div>
                <div className="mt-1 border-l-2 border-[#FFE34D] pl-2 submenu bg-white rounded-md relative z-50">
                  {item.submenu.map((subItem, subIndex) => (
                    <div key={subIndex}>
                      <Link
                        href={subItem.path}
                        className={`block px-4 py-2 text-sm ${
                          isSubmenuItemActive(subItem)
                            ? 'font-bold text-yellow-600'
                            : ''
                        } hover:font-bold rounded-md mt-1 transition-all duration-200 transform hover:scale-105`}
                        onClick={() => handleMenuItemClick(subItem.path)}
                      >
                        {subItem.name}
                      </Link>
                      {subIndex < item.submenu.length - 1 && (
                        <div className="mx-2 border-b border-dashed border-gray-300"></div>
                      )}
                    </div>
                  ))}
                </div>
              </>
            )}
          </div>
        ))}
      </div>

      {/* <div className="flex items-center space-x-4">
        {isAuth ? (
          <UserProfile
            user={user}
            previewImg={previewImg}
            setPreviewImg={setPreviewImg}
            isAdmin={isAdmin}
            isTutor={isTutor}
            isMobile={true}
          />
        ) : (
          <AuthButtons />
        )}
      </div> */}
    </div>
  );
};

export default MobileMenu;
