'use client';
import DetailsModal from '@/components/form/modal/MissionConfirmationModal';
import useDataFetch from '@/hooks/useDataFetch';
import { useRouter } from 'next/navigation';
import React, { useState } from 'react';
import MissionDetailsModal from './_components/MissionDetailsModal';
import ShowMission from './_components/ShowMission';

const EssayMission = () => {
  const router = useRouter();
  const [weeklyMission, setWeeklyMission] = useState(true);
  const [selectedMission, setSelectedMission] = useState(null);

  const { data: activeTask } = useDataFetch({
    queryKey: ['active-task'],
    endPoint: `/student-essay/activeTask`,
  });
  console.log("active task", activeTask)

  const { data, isLoading } = useDataFetch({
    queryKey: ['student-essay', weeklyMission],
    endPoint: `/student-essay/getMission${
      weeklyMission ? '?timeFrequency=weekly' : '?timeFrequency=monthly'
    }`,
  });

  // Get missions directly from the API response
  const missions = data?.items || [];

  if (activeTask?.task) {
    router.push(`/essay/mission/${activeTask?.task?.id}`);
    return null;
  }

  return (
    <div className="">
      {!isLoading && (
        <div className="max-w-7xl mx-auto px-5 xl:px-0 relative my-10 space-y-4">
          <div className="rounded-xl shadow-lg border p-5 bg-[#FFF9FB]">
            <div>
              <div className="flex items-start justify-between text-gray-600">
                <div>
                  <h1 className="text-2xl text-yellow-800 font-semibold">
                    Hello English Coaching Mission Essay
                  </h1>
                  {/* <p>Instruction:</p>
                <p>1. Write a short writtings on a dog.</p> */}
                </div>

                <h1 className="bg-gradient-to-b from-[#ECB306] to-[#AE6E33] bg-clip-text text-3xl font-extrabold text-transparent font-serif">
                  HEC Essay
                </h1>
              </div>

              {/* <button className="flex items-center gap-2 border border-yellow-800 text-3xl text-yellow-900 bg-gradient-to-b from-[#DCA600] to-[#FFDE5B] px-10 py-1 rounded-full mt-3">
              GO{' '}
              <ButtonIcon
                icon={'tabler:arrow-right'}
                innerBtnCls={'h-8 w-8 '}
                btnIconCls={'h-4 w-4'}
              />
            </button> */}
            </div>
          </div>

          <div className="p-5 bg-[#FFFDF5] shadow-md border rounded-lg">
            <div className="">
              <div className="flex items-center border-2 border-yellow-500 rounded-full bg-[#FEFCE8]">
                <div className="w-full flex-1 relative">
                  <button
                    onClick={() => setWeeklyMission(true)}
                    className={`w-full flex-1 py-2.5 ${
                      weeklyMission && 'bg-yellow-500  rounded-l-full'
                    }`}
                  >
                    Weekly Mission
                  </button>
                  {weeklyMission && (
                    <div className="h-6 w-6 bg-yellow-500 rotate-45 absolute z-0 -bottom-3 left-1/2 -translate-x-1/2"></div>
                  )}
                </div>

                <div className="w-full flex-1 relative">
                  <button
                    onClick={() => setWeeklyMission(false)}
                    className={`w-full py-2.5 ${
                      !weeklyMission && 'bg-yellow-500 rounded-r-full'
                    }`}
                  >
                    Monthly Mission
                  </button>

                  {!weeklyMission && (
                    <div className="h-6 w-6 bg-yellow-500 rotate-45 absolute z-0 -bottom-3 left-1/2 -translate-x-1/2"></div>
                  )}
                </div>
              </div>
            </div>

            <div className="">
              {
                /* Display missions using ShowMission component */
                missions.map((mission) => (
                  <ShowMission
                    key={mission.id}
                    mission={mission}
                    weeklyMission={weeklyMission}
                    taskProgress={mission.taskProgress || []}
                    onTaskClick={(task) => {
                      // Find the corresponding progress for this task
                      const taskProgressItem = mission.taskProgress?.find(
                        (progress) => progress.id === task.id
                      );
                      const taskStatus = taskProgressItem?.status;
                      setSelectedMission({ task: task, status: taskStatus });
                    }}
                  />
                ))
              }

              {/* Show message if no missions available */}
              {!isLoading && missions.length === 0 && (
                <div className="py-10 text-center">
                  <p className="text-gray-500">
                    No missions available for this time period.
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {!!selectedMission && (
        <MissionDetailsModal
          isOpen={!!selectedMission}
          onClose={() => setSelectedMission(null)}
          data={selectedMission?.task}
          title={'Mission Essay'}
          isNavigate={selectedMission?.status !== null}
          endPoint={'/student-essay/start/task'}
          returnTo={`/essay/mission/${selectedMission?.task?.id}`}
          topic="Essay"
        />
      )}
    </div>
  );
};

export default EssayMission;
