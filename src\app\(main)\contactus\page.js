'use client';
import React from 'react';
import HeaderCard from '@/components/HeaderCard';
import InputField from '@/components/form/InputField';
import { Formik, Form } from 'formik';
import Link from 'next/link';
import { ButtonIcon } from '@/components/Button';
import { useRouter } from 'next/navigation';
import FooterSection from '@/components/FooterSection';

const Contact = () => {
  const router = useRouter();
  // Initial values for form fields
  const initialValues = {
    payment_method: '',
  };

  // Handle form submission
  const handleSubmit = (values) => {
    console.log(values);
  };

  return (
    <div className="">
      <HeaderCard
        text="Contact Us"
        bgColor="#FFF9FB"
        textColor="#333"
        textClass=""
      />

      <div className="bg-[#EDFDFD]">
        <div className="relative flex-col items-center justify-center max-w-7xl mx-auto px-5 xl:px-0 pb-10">
          <div
            onClick={() => router.back()}
            className="flex justify-start items-center gap-2 py-5 mb-8 max-w-52 group cursor-pointer"
          >
            <ButtonIcon
              icon="famicons:arrow-back-outline"
              innerBtnCls="h-12 w-12 text-white"
              btnIconCls="h-5 w-5 text-white"
            />

            <h3 className="text-xl font-[500] text-gray-800 group-hover:text-yellow-600">
              Back
            </h3>
          </div>

          <div className="max-w-2xl mx-auto space-y-3">
            <div className="text-gray-700">
              <h2 className="text-3xl font-semibold text-center mb-4">
                Contact Us
              </h2>
              <p>
                If you need more information about plans,
                customization,suggestions, or partner ideas,{' '}
              </p>
              <p>
                reach us at{' '}
                <Link href="mailto:<EMAIL>">
                  helloenglishcoaching.com
                </Link>{' '}
                or fill out the contact form.
              </p>
            </div>

            <Formik initialValues={initialValues} onSubmit={handleSubmit}>
              <Form className="bg-[#FFF9FB] p-5 text-white flex-col items-center justify-center mx-auto rounded-lg shadow-md space-y-3">
                <InputField name="Name" type="text" placeholder="Name" />

                <InputField name="E-mail" type="text" placeholder="E-mail" />

                <InputField
                  name="message"
                  as="textarea" // Change input to textarea
                  type="text"
                  placeholder="Write your message..."
                  className="h-32 w-full p-2 border border-gray-300 rounded-md resize-none"
                />

                <div className="flex items-center justify-center">
                  <button
                    type="submit"
                    className="px-10 py-1.5 bg-[#033D63] text-white rounded-lg"
                  >
                    Send
                  </button>
                </div>
              </Form>
            </Formik>
          </div>
        </div>
      </div>

      <FooterSection />
    </div>
  );
};

export default Contact;
