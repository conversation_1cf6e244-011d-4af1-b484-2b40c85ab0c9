'use client';
import { ButtonIcon } from '@/components/Button';
import <PERSON><PERSON>ie<PERSON> from '@/components/EditorViewer';
import useDataFetch from '@/hooks/useDataFetch';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import React, { useState } from 'react';
import { Icon } from '@iconify/react';

const StoryMaker = () => {
  const router = useRouter();
  const [currentPage, setCurrentPage] = useState(1);
  const [limit, setLimit] = useState(10);

  const { data, isLoading } = useDataFetch({
    queryKey: ['story-maker', currentPage, limit],
    endPoint: '/play/story-maker/play/list',
    params: { page: currentPage, limit: limit },
  });

  const items = data?.games || [];
  const totalCount = data?.total_count || 0;
  const totalPages = Math.ceil(totalCount / limit);

  return (
    <div className="max-w-7xl mx-auto p-5 xl:px-0 my-8">
      <div className="p-5 bg-[#FFF9FB] rounded-lg shadow-lg flex items-center justify-between">
        <h1 className="text-2xl text-yellow-800 font-bold">Story Maker</h1>
        <h1 className="text-3xl text-yellow-600 font-bold font-serif">
          HEC PLAY
        </h1>
      </div>

      {/* Loading State */}
      {isLoading ? (
        <div className="flex justify-center items-center py-20">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-yellow-500"></div>
        </div>
      ) : (
        <>
          {/* Stories Grid */}
          <div className="grid grid-cols-1 gap-4 mt-6 px-6">
            {items.length > 0 ? (
              items.map((item) => (
                <div
                  key={item.id}
                  className="bg-[#FFF9FB] rounded-lg shadow-lg p-4 cursor-pointer transition-all duration-300 flex items-center gap-5 border border-gray-100 relative"
                >
                  <span className="absolute z-10 top-0 right-0 bg-green-500 text-white text-xs px-3 py-1 rounded-bl-lg rounded-tr-lg">
                    {item?.is_played ? 'Played' : 'New'}
                  </span>
                  <div className="relative">
                    <Image
                      src={item?.picture || '/assets/images/all-img/noImage.png'}
                      alt={item.title}
                      height={300}
                      width={300}
                      className="rounded-lg object-cover max-h-60"
                    />
                  </div>

                  <div className="text-gray-600">
                    <h4 className="font-medium text-yellow-700 text-2xl ">
                      {item.title}
                    </h4>
                    <EditorViewer
                      data={item?.description || item?.instruction || 'This is a dummy description'}
                    />

                    <button
                      onClick={() => router.push(`/story-maker/${item.id}`)}
                      className="flex items-center gap-2 border border-yellow-800 text-2xl text-yellow-900 bg-gradient-to-b from-[#DCA600] to-[#FFDE5B] px-10 py-1 rounded-full mt-3"
                    >
                      See{' '}
                      <ButtonIcon
                        icon={'tabler:arrow-right'}
                        innerBtnCls={'h-8 w-8 '}
                        btnIconCls={'h-4 w-4'}
                      />
                    </button>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-20">
                <p className="text-gray-500 text-lg">No stories available</p>
              </div>
            )}
          </div>

          {/* Pagination Controls */}
          {totalCount > 0 && (
            <div className="flex items-center justify-between p-6 mt-6 bg-white rounded-lg">
              {/* Left: Record count */}
              <div className="text-sm text-gray-700">
                Showing {(currentPage - 1) * limit + 1}-{Math.min(currentPage * limit, totalCount)} of {totalCount} stories
              </div>

              {/* Center: Pagination */}
              <div className="flex items-center space-x-1">
                <button
                  onClick={() => setCurrentPage(currentPage - 1)}
                  disabled={currentPage === 1}
                  className="p-2 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <Icon icon="mdi:chevron-left" className="w-5 h-5" />
                </button>

                {/* Dynamic pagination buttons */}
                {totalPages <= 5 ? (
                  // If 5 or fewer pages, show all
                  Array.from({ length: totalPages }, (_, i) => (
                    <button
                      key={i}
                      onClick={() => setCurrentPage(i + 1)}
                      className={`px-3 py-1 rounded ${
                        currentPage === i + 1
                          ? 'bg-yellow-400 text-white font-medium'
                          : 'hover:bg-gray-100'
                      }`}
                    >
                      {i + 1}
                    </button>
                  ))
                ) : (
                  // If more than 5 pages, show smart pagination
                  <>
                    {/* First page */}
                    <button
                      onClick={() => setCurrentPage(1)}
                      className={`px-3 py-1 rounded ${
                        currentPage === 1
                          ? 'bg-yellow-400 text-white font-medium'
                          : 'hover:bg-gray-100'
                      }`}
                    >
                      1
                    </button>

                    {/* Ellipsis for first gap if needed */}
                    {currentPage > 3 && <span className="px-2">...</span>}

                    {/* Pages around current page */}
                    {Array.from({ length: Math.min(3, totalPages) }, (_, i) => {
                      let pageNum;
                      if (currentPage <= 2) {
                        pageNum = i + 2;
                      } else if (currentPage >= totalPages - 1) {
                        pageNum = totalPages - 3 + i;
                      } else {
                        pageNum = currentPage - 1 + i;
                      }

                      if (pageNum > 1 && pageNum < totalPages) {
                        return (
                          <button
                            key={i}
                            onClick={() => setCurrentPage(pageNum)}
                            className={`px-3 py-1 rounded ${
                              currentPage === pageNum
                                ? 'bg-yellow-400 text-white font-medium'
                                : 'hover:bg-gray-100'
                            }`}
                          >
                            {pageNum}
                          </button>
                        );
                      }
                      return null;
                    }).filter(Boolean)}

                    {/* Ellipsis for last gap if needed */}
                    {currentPage < totalPages - 2 && <span className="px-2">...</span>}

                    {/* Last page */}
                    {totalPages > 1 && (
                      <button
                        onClick={() => setCurrentPage(totalPages)}
                        className={`px-3 py-1 rounded ${
                          currentPage === totalPages
                            ? 'bg-yellow-400 text-white font-medium'
                            : 'hover:bg-gray-100'
                        }`}
                      >
                        {totalPages}
                      </button>
                    )}
                  </>
                )}

                <button
                  onClick={() => setCurrentPage(currentPage + 1)}
                  disabled={currentPage === totalPages}
                  className="p-2 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <Icon icon="mdi:chevron-right" className="w-5 h-5" />
                </button>
              </div>

              {/* Right: Items per page */}
              <div className="flex items-center">
                <span className="text-sm text-gray-700 mr-2">Items per page:</span>
                <select
                  className="border border-gray-300 rounded px-2 py-1 text-sm"
                  value={limit}
                  onChange={(e) => {
                    setLimit(Number(e.target.value));
                    setCurrentPage(1); // Reset to first page when changing limit
                  }}
                >
                  <option value="10">10</option>
                  <option value="25">25</option>
                  <option value="25">25</option>
                  <option value="50">50</option>
                  <option value="100">100</option>
                </select>
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default StoryMaker;
