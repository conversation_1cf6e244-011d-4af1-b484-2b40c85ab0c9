'use client';
import React, { useState } from 'react';
import NewTablePage from '@/components/form/NewTablePage';
import useDataFetch from '@/hooks/useDataFetch';
import { useRouter } from 'next/navigation';
import api from '@/lib/api';

const Waterfall = ({ onChangeTab }) => {
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const router = useRouter();

  const { data, isLoading, refetch } = useDataFetch({
    queryKey: ['waterfall', currentPage, rowsPerPage],
    endPoint: '/play/waterfall/admin/sets',
    params: { page: currentPage, limit: rowsPerPage },
  });

  const waterFallItems = data?.items || [];

  // Use server-side pagination data from API response
  const totalItems = data?.totalItems || data?.totalCount || 0;
  const totalPages = data?.totalPages || Math.ceil(totalItems / rowsPerPage);

  // Define columns for the table
  const columns = [
    {
      label: 'Question',
      field: 'title',
    },
    {
      label: 'Points',
      field: 'total_score',
    },
    {
      label: 'Questions',
      field: 'total_questions',
    },
    {
      label: 'ACTIVE STATUS',
      field: 'is_active',
      cellRenderer: (value, row) => {
        const toggleStatus = async (id, currentStatus) => {
          try {
            await api.patch(
              `/play/waterfall/admin/sets/${id}/toggle-status`,
              { is_active: !currentStatus }
            );
            refetch();
          } catch (error) {
            console.log(error);
          }
        };

        return (
          <button
            className={`relative inline-flex h-6 w-11 items-center rounded-full ${
              row?.is_active ? 'bg-green-500' : 'bg-gray-300'
            }`}
            onClick={() => toggleStatus(row?.id, row?.is_active)}
          >
            <span
              className={`inline-block h-4 w-4 transform rounded-full bg-white transition ${
                row?.is_active ? 'translate-x-6' : 'translate-x-1'
              }`}
            />
          </button>
        );
      },
    },
  ];

  // Define actions
  const actions = [
    {
      name: 'View',
      icon: 'material-symbols:visibility',
      className: 'text-blue-600 border cursor-pointer',
      onClick: (row) => router.push(`/dashboard/module-management/hec-play/waterfall/${row.id}`),
    },
    // {
    //   name: 'Edit',
    //   icon: 'material-symbols:edit-outline',
    //   className: 'text-black-600 border',
    //   onClick: (row) => console.log(row),
    // },
    // {
    //   name: 'Delete',
    //   icon: 'material-symbols:delete-outline',
    //   className: 'text-red-600 border',
    //   onClick: (row) => console.log('Delete', row),
    // },
  ];

  // Handle page change
  const handleChangePage = (page) => {
    setCurrentPage(page);
  };

  // Handle rows per page change
  const handleRowsPerPageChange = (newRowsPerPage) => {
    setRowsPerPage(newRowsPerPage);
    setCurrentPage(1); // Reset to first page when changing rows per page
  };

  return (
    <div>
      <NewTablePage
        title="Waterfall Question Set List"
        createButton="Create Question Set"
        createPage="/dashboard/module-management/hec-play/waterfall/add"
        createBtnLink="/dashboard/module-management/hec-play/waterfall/add"
        columns={columns}
        data={waterFallItems}
        actions={actions}
        currentPage={currentPage}
        changePage={handleChangePage}
        totalItems={totalItems}
        rowsPerPage={rowsPerPage}
        setRowsPerPage={handleRowsPerPageChange}
        totalPages={totalPages}
        showCheckboxes={false}
        showSearch={true}
        showNameFilter={false}
        showSortFilter={false}
      />
    </div>
  );
};

export default Waterfall;
