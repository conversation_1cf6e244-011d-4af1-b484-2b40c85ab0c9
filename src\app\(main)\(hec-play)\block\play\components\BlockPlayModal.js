import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Icon } from '@iconify/react';

const BlockPlayModal = ({
  isOpen,
  onClose,
  children,
  currentStep = 0,
  totalSteps = 1,
  stageTitle = 'Game Progress',
  showProgress = true,
  completedSteps = 0,
}) => {
  const progressPercentage =
    totalSteps > 0 ? (completedSteps / totalSteps) * 100 : 0;

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          //   animate={{ opacity: 1 }}
          //   exit={{ opacity: 0 }}
          className="fixed -top-10 left-0 right-0 bottom-0 bg-black bg-opacity-25 flex items-center justify-center z-50 p-4"
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            className="bg-white rounded-2xl shadow-2xl w-full max-w-6xl max-h-[80vh] overflow-hidden flex flex-col"
          >
            {/* Header with Progress and Close Button */}
            <div
              className={`${
                stageTitle.includes('Expanding')
                  ? 'bg-gradient-to-r from-yellow-500 to-yellow-400'
                  : 'bg-gradient-to-r from-yellow-500 to-yellow-400'
              } text-white p-4 flex items-center justify-between transition-all duration-500`}
            >
              <div className="flex items-center gap-4 flex-1">
                <div className="flex items-center gap-2">
                  <motion.div
                    key={stageTitle}
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ duration: 0.3 }}
                  >
                    <Icon
                      icon={
                        stageTitle.includes('Expanding')
                          ? 'mdi:text-box-plus'
                          : 'mdi:text-box'
                      }
                      className="w-6 h-6"
                    />
                  </motion.div>
                  <h2 className="text-lg font-bold">{stageTitle}</h2>
                </div>
                {showProgress && (
                  <div className="flex-1 max-w-md">
                    <div className="flex items-center justify-between text-sm mb-1">
                      <span>Progress</span>
                      <span>
                        {completedSteps}/{totalSteps}
                      </span>
                    </div>
                    <div className="w-full bg-white bg-opacity-30 rounded-full h-2">
                      <motion.div
                        className="bg-white h-full rounded-full transition-all duration-500"
                        initial={{ width: 0 }}
                        animate={{ width: `${progressPercentage}%` }}
                      />
                    </div>
                  </div>
                )}
              </div>

              <button
                onClick={onClose}
                className="p-2 hover:bg-white hover:bg-opacity-20 rounded-full transition-colors"
              >
                <Icon icon="mdi:close" className="w-6 h-6" />
              </button>
            </div>

            {/* Content Area */}
            <div className="flex-1 overflow-y-auto p-6 relative">
              <AnimatePresence mode="wait">
                <motion.div
                  key={stageTitle}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.3 }}
                >
                  {children}
                </motion.div>
              </AnimatePresence>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default BlockPlayModal;
