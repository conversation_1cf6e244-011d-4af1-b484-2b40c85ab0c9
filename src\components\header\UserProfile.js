import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useDispatch } from 'react-redux';
import { logoutUser } from '@/store/features/authSlice';
import { Icon } from '@iconify/react';

const UserProfile = ({ 
  user, 
  previewImg, 
  setPreviewImg, 
  isAdmin, 
  isTutor, 
  isMobile = false 
}) => {
  const router = useRouter();
  const dispatch = useDispatch();

  const handleLogout = () => {
    dispatch(logoutUser());
    router.push('/');
  };

  const profileDropdownClasses = isMobile 
    ? "hidden group-hover:block absolute top-10 left-0 mt-4 w-64 bg-white border border-gray-200 rounded-md shadow-lg z-50 p-2"
    : "hidden group-hover:block absolute top-10 right-0 mt-4 w-60 bg-white border border-gray-200 rounded-md shadow-lg z-50 p-2";

  return (
    <div className="group relative">
      <div className="flex items-center gap-2 py-2">
        <Image
          src={previewImg || '/assets/images/all-img/avatar.png'}
          alt="icon"
          width={80}
          height={80}
          onError={() => setPreviewImg('/assets/images/all-img/avatar.png')}
          className={`rounded-full border border-white ${
            isMobile ? 'w-10 h-10' : 'sm:w-10 sm:h-10 w-8 h-8'
          }`}
        />

        <p className={isMobile ? '' : 'hidden sm:block'}>
          {user?.name?.split(' ')[0]}
        </p>

        <Icon
          icon={'mingcute:down-line'}
          width="16"
          height="16"
          className="group-hover:rotate-180 transition-transform duration-300 opacity-70"
        />
      </div>

      <div className={profileDropdownClasses}>
        {user?.roles?.includes('student') ? (
          <>
            <Link
              href={
                isAdmin
                  ? `/dashboard/admin-profile/${user?.id}`
                  : isTutor
                  ? `/dashboard/tutor-profile/${user?.id}`
                  : '/profile'
              }
            >
              <button className="w-full px-4 py-2 hover:bg-[#FFFAC2] rounded-lg text-start flex items-center">
                <Icon
                  icon="mdi:account-circle"
                  className="w-5 h-5 mr-2"
                />
                Profile Details
              </button>
            </Link>

            <Link href="/change-password">
              <button className="w-full px-4 py-2 hover:bg-[#FFFAC2] rounded-lg text-start flex items-center">
                <Icon icon="mdi:lock" className="w-5 h-5 mr-2" />
                Change Password
              </button>
            </Link>

            <button
              onClick={handleLogout}
              className="w-full px-4 py-2 hover:bg-red-50 rounded-lg text-start flex items-center"
            >
              <Icon icon="mdi:logout" className="w-5 h-5 mr-2" />
              Logout
            </button>
          </>
        ) : (
          <>
            <Link href="/dashboard">
              <button className="w-full px-4 py-2 hover:bg-[#FFFAC2] rounded-lg text-start">
                Dashboard
              </button>
            </Link>

            <Link
              href={
                isAdmin
                  ? `/dashboard/admin-profile/${user.id}`
                  : isTutor
                  ? `/dashboard/tutor-profile/${user.id}`
                  : '/profile'
              }
            >
              <button className="w-full px-4 py-2 hover:bg-[#FFFAC2] rounded-lg text-start">
                Profile
              </button>
            </Link>

            <button
              onClick={handleLogout}
              className="w-full px-4 py-2 hover:bg-red-50 rounded-lg text-start"
            >
              Logout
            </button>
          </>
        )}
      </div>
    </div>
  );
};

export default UserProfile;
