import React from 'react';
import { motion } from 'framer-motion';
import { Icon } from '@iconify/react';
import Image from 'next/image';

const InitialInstructionsStage = ({ gameData, onStartGame }) => {
  if (!gameData) return null;

  const totalSentences = gameData?.sentences?.length || 0;
  const totalScore = gameData?.score || 0;


  return (
    <div className="w-full max-w-5xl mx-auto px-4 sm:px-6">
      {/* Header Section */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="text-center mb-6 sm:mb-8 flex flex-col sm:flex-row items-center justify-between relative p-4 sm:p-5 sm:py-8 bg-[#FCF8EF] rounded-lg [box-shadow:2px_2px_12px_0px_#F5D1B066_inset,_-2px_-2px_12px_0px_#F5D1B066_inset] gap-3"
      >
        <div className="relative">
          <Image
            src="/assets/images/all-img/catImg.png"
            alt="Game Character"
            width={80}
            height={80}
            className="mx-auto sm:mx-0 sm:w-[120px] sm:h-[120px]"
          />
        </div>

        <div className="flex-1">
          <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-[#8B4513]">
            Block Sentence Builder
          </h1>
          <p className="text-sm sm:text-base lg:text-lg text-[#5A3D1A] mb-2">
            Create amazing sentences using word blocks!
          </p>
          <div className="flex flex-col sm:flex-row items-center justify-center gap-2 sm:gap-4 text-xs sm:text-sm text-[#8B4513]">
            <div className="flex items-center gap-1">
              <Icon icon="mdi:format-list-numbered" className="w-3 h-3 sm:w-4 sm:h-4" />
              <span>{totalSentences} Sentences</span>
            </div>
            <div className="flex items-center gap-1">
              <Icon icon="mdi:star" className="w-3 h-3 sm:w-4 sm:h-4" />
              <span>Max {totalScore} Points</span>
            </div>
          </div>
        </div>

        {/* Start Game Button */}
        <motion.div
          // initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.6, delay: 0.7 }}
          className="text-center"
        >
          <motion.button
            whileTap={{ scale: 0.95 }}
            onClick={onStartGame}
            className="bg-gradient-to-b from-yellow-500 to-yellow-400 hover:from-yellow-500 hover:to-yellow-500 text-white font-bold py-3 px-8 sm:py-4 sm:px-12 rounded-full text-lg sm:text-xl transition-all duration-300 flex items-center gap-2 sm:gap-3 mx-auto"
          >
            <Icon icon="mdi:play" className="w-5 h-5 sm:w-6 sm:h-6" />
            Start Game
            <motion.div
              animate={{ x: [0, 5, 0] }}
              transition={{ duration: 1.5, repeat: Infinity }}
            >
              <Icon icon="mdi:arrow-right" className="w-5 h-5 sm:w-6 sm:h-6" />
            </motion.div>
          </motion.button>
        </motion.div>

        {/* <motion.div
          animate={{ rotate: [0, 10, -10, 0] }}
          transition={{ duration: 2, repeat: Infinity, repeatDelay: 3 }}
          className="absolute -top-2 -right-2"
        >
          <Icon
            icon="mdi:star-four-points"
            className="w-8 h-8 text-yellow-400"
          />
        </motion.div> */}
      </motion.div>

      {/* Decorative Elements */}
      <motion.div
        // initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 1, delay: 1 }}
        className="my-8 text-center"
      >
        <Image
          src="/assets/Frame 1000007108.png"
          alt="Decoration"
          width={500}
          height={20}
          className="w-full h-auto opacity-60"
        />
      </motion.div>

    </div>
  );
};

export default InitialInstructionsStage;
