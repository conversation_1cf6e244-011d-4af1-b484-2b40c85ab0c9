'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import api from '@/lib/api';
import { toast } from 'react-toastify';
import { useQuery } from '@tanstack/react-query';
import HecQALayout from '../../_components/HecQALayout';
import EditorViewer from '@/components/EditorViewer';
import FeedbackModal from '../FeedbackModal';
import { ButtonIcon } from '@/components/Button';
import SimpleTiptapEditor from '@/components/form/SimpleTiptapEditor';
import Image from 'next/image';

const QASubmissionViewPage = () => {
  const params = useParams();
  const router = useRouter();
  const submissionId = params.id;

  const searchParams = useSearchParams();
  const tabParam = searchParams.get('tab');

  // Map tab parameters to the correct tab for this mission review page
  const getActiveTabForMissionReview = (tabParam) => {
    // Both questionSubmission and missionQAList should show missionQAList tab in mission review
    if (tabParam === 'questionSubmission' || tabParam === 'missionQAList') {
      return 'missionQAList';
    }
    return 'missionQAList'; // default
  };

  const [activeTab, setActiveTab] = useState(
    getActiveTabForMissionReview(tabParam)
  );

  useEffect(() => {
    if (tabParam) {
      setActiveTab(getActiveTabForMissionReview(tabParam));
    }
  }, [tabParam]);
  // Editor ref for TinyMCE
  const editorRef = useRef(null);

  // Tutor feedback states
  const [feedback, setFeedback] = useState('');
  const [score, setScore] = useState('');
  const [isSubmittingReview, setIsSubmittingReview] = useState(false);
  const [correctionText, setCorrection] = useState('');
  const [isFeedbackModalOpen, setIsFeedbackModalOpen] = useState(false);

  // Fetch submission details
  const fetchSubmissionDetails = async () => {
    try {
      const response = await api.get(
        `/tutor-qa-mission/submission-history/${submissionId}`
      );

      if (response?.success) {
        return response.data;
      } else {
        throw new Error(
          response?.message || 'Failed to fetch submission details'
        );
      }
    } catch (error) {
      console.error('Error fetching submission details:', error);
      toast.error(error.message || 'Error fetching submission details');
      throw error;
    }
  };

  // Use React Query for data fetching
  const {
    data: submissionData,
    isLoading,
    isError,
    error,
    refetch, // Extract refetch from useQuery
  } = useQuery({
    queryKey: ['qaSubmissionDetails', submissionId],
    queryFn: fetchSubmissionDetails,
    enabled: !!submissionId,
    retry: 1,
    onSuccess: (data) => {
      // Pre-populate feedback if it exists
      if (data?.marking) {
        setFeedback(data.marking.submissionFeedback || '');
        setScore(data.marking.score || '');
      }
    },
  });

  // Initialize correction text with review content if exists, otherwise original content
  useEffect(() => {
    if (submissionData?.marking?.taskRemarks) {
      // Show the review/marking content if it exists
      setCorrection(submissionData.marking.taskRemarks);
    } else if (submissionData?.submissionHistory?.content) {
      // Otherwise show the original submission content
      setCorrection(submissionData.submissionHistory.content);
    }

    if (submissionData?.marking?.score) {
      setScore(submissionData.marking.score);
    }
  }, [submissionData]);

  // Handle back navigation
  const handleBack = () => {
    router.back();
  };

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // Parse HTML content to plain text for display
  const parseHtmlContent = (htmlContent) => {
    if (!htmlContent) return 'No content available';

    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = htmlContent;
    return tempDiv.textContent || tempDiv.innerText || 'No content available';
  };

  // Handle feedback submission - UPDATED TO USE CORRECT API
  // Extract only the handleSubmitReview function from QASubmissionViewPage
  // This is the corrected version that sends taskRemarks

  // Extract only the handleSubmitReview function from QASubmissionViewPage
  // This is the corrected version that sends taskRemarks

  const handleSubmitReview = async () => {
    if (!correctionText.trim() || !score) {
      toast.error('Please provide both correction and score');
      return;
    }

    setIsSubmittingReview(true);
    try {
      // Try using the actual submission ID from the data
      const actualSubmissionId =
        submissionData?.submission?.id ||
        submissionData?.submissionHistory?.submissionId ||
        submissionId;

      console.log('QASubmissionViewPage - Submitting task remarks:', {
        submissionId: actualSubmissionId,
        taskRemarks: correctionText.trim(),
        score: parseInt(score),
        // Only sending taskRemarks and score, not touching submissionFeedback
      });

      // Send ONLY taskRemarks and score, don't include submissionFeedback at all
      const response = await api.post(`/tutor-qa-mission/QAMarking`, {
        submissionId: actualSubmissionId,
        taskRemarks: correctionText.trim(), // THIS SHOULD UPDATE TASK REMARKS ONLY
        score: parseInt(score),
        // Don't send submissionFeedback at all - let backend preserve existing value
      });

      console.log('QASubmissionViewPage - API Response:', response);

      if (response?.success) { 
        // Refresh the data after successful submission
        refetch();
      } else {
        throw new Error(response?.message || 'Failed to submit review');
      }
    } catch (error) {
      console.error('QASubmissionViewPage - Error submitting review:', error);
      console.error(
        'QASubmissionViewPage - Full error response:',
        error.response?.data
      );
    } finally {
      setIsSubmittingReview(false);
    }
  };

  // Calculate actualSubmissionId for FeedbackModal
  const actualSubmissionId =
    submissionData?.submission?.id ||
    submissionData?.submissionHistory?.submissionId ||
    submissionId;

  // Loading state
  if (isLoading) {
    return (
      <HecQALayout activeTab={activeTab}>
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading submission details...</p>
          </div>
        </div>
      </HecQALayout>
    );
  }

  // Error state
  if (isError) {
    return (
      <HecQALayout activeTab={activeTab}>
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <div className="text-red-600 text-xl mb-4">⚠️</div>
            <p className="text-gray-600 mb-4">
              {error?.message || 'Failed to load submission'}
            </p>
            <button
              onClick={handleBack}
              className="bg-orange-600 text-white px-4 py-2 rounded-md hover:bg-orange-700"
            >
              Go Back
            </button>
          </div>
        </div>
      </HecQALayout>
    );
  }

  const { submissionHistory, submission, marking, task } = submissionData || {};

  return (
    <HecQALayout activeTab={activeTab}>
      <div className="flex justify-between items-center mb-2">
        <div className="flex gap-4 items-center">
          <h6 className="text-lg text-gray-700 font-medium">View Mission QA</h6>
        </div>
      </div>

      <div className="grid items-center bg-[#FFF9FB] gap-2 p-4 shadow-xl border rounded-lg space-y-3">
        {/* Task Header */}
        <div className="p-5 bg-[#FCF8EF] rounded-lg [box-shadow:2px_2px_12px_0px_#F5D1B066_inset,_-2px_-2px_12px_0px_#F5D1B066_inset] flex items-center justify-between">
          <div>
            <h1 className="text-2xl text-yellow-800 font-semibold">
              {task?.title}
            </h1>
            <p>Instructions:</p>
            <EditorViewer data={task?.instructions || task?.description} />
          </div>
          <div>
            <h2 className="text-3xl font-semibold text-yellow-600 font-serif">
              Mission Q&A
            </h2>
            <p className="text-gray-600 text-end mt-2">
              Total Score: {task?.totalScore || 0}
            </p>
          </div>
        </div>

        {/* Student Submission Section */}
        <div className="bg-white p-4 rounded-lg border shadow">
          <h3 className="text-xl text-yellow-800 font-semibold mb-2">
            Student Submission
          </h3>
          <div className="rounded-md shadow border p-4 min-h-32 max-h-72 overflow-y-auto bg-gray-50">
            <EditorViewer
              data={submissionHistory?.content}
              className="whitespace-pre-wrap text-sm text-[#314158]"
            />
          </div>
        </div>

        {/* Tutor Correction Zone */}
        <div className="bg-white p-4 rounded-lg border shadow">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-xl text-yellow-800 font-semibold">
              Tutor Correction Zone
            </h3>
          </div>

          {submissionData?.marking?.taskRemarks ? (
            <EditorViewer
              data={
                submissionData?.marking?.taskRemarks
              }
            />
          ) : (
            <SimpleTiptapEditor
              editorRef={editorRef}
              initialValue={
                submissionData?.marking?.taskRemarks ||
                submissionData?.submissionHistory?.content ||
                ''
              }
              setValue={setCorrection}
              height={400}
            />
          )}

          <div className="flex items-center justify-between mt-2">
            <div className="flex items-center gap-5">
              <ButtonIcon
                icon={'arcticons:feedback-2'}
                innerBtnCls={'h-12 w-12'}
                btnIconCls={'h-5 w-5'}
                onClick={() => setIsFeedbackModalOpen(true)}
              />

              <div className="flex items-center">
                <label className="mr-2 font-medium">Score:</label>
                {marking?.score ? (
                  <div className="text-gray-800 font-semibold">
                    {marking.score} / {task?.totalScore || 10}
                  </div>
                ) : (
                  <>
                    <input
                      type="number"
                      value={score}
                      onChange={(e) => {
                        const newScore = Number(e.target.value);
                        if (
                          newScore < 0 ||
                          newScore > (task?.totalScore || 10)
                        ) {
                          toast.error(
                            `Score must be between 0 to ${
                              task?.totalScore || 10
                            }`
                          );
                          return;
                        }
                        setScore(e.target.value);
                      }}
                      className="w-24 border border-gray-300 rounded px-2 py-1 text-center"
                      min="0"
                      max={task?.totalScore || 10}
                      placeholder={`0-${task?.totalScore || 10}`}
                    />
                    <span className="ml-1 text-gray-600">
                      / {task?.totalScore || 10}
                    </span>
                  </>
                )}
              </div>
            </div>

            {!(
              submissionData?.marking?.taskRemarks
            ) && (
              <div className="flex items-center gap-4">
                <button
                  onClick={handleBack}
                  className="px-4 py-2 bg-gray-400 text-white rounded-md hover:bg-gray-500"
                >
                  Cancel
                </button>
                <button
                  onClick={handleSubmitReview}
                  disabled={
                    !correctionText.trim() || !score || isSubmittingReview
                  }
                  className="px-4 py-2 bg-yellow-500 text-white rounded-md hover:bg-yellow-600 disabled:bg-gray-300 disabled:cursor-not-allowed flex items-center gap-2"
                >
                  {isSubmittingReview ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      Submitting...
                    </>
                  ) : marking ? (
                    'Update'
                  ) : (
                    'Submit'
                  )}
                </button>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Feedback Modal */}
      {/* Feedback Modal */}
      <FeedbackModal
        isOpen={isFeedbackModalOpen}
        feedbacks={submissionData?.feedbacks || []}
        onClose={() => setIsFeedbackModalOpen(false)}
        refetch={refetch}
        entryId={actualSubmissionId}
        marking={marking} // Add this line to pass marking data
      />
    </HecQALayout>
  );
};

export default QASubmissionViewPage;
