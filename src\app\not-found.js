'use client';
import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { Icon } from '@iconify/react';
import { getUserRoleFromCookie } from '@/lib/auth';

const NotFound = () => {
  const [userRole, setUserRole] = useState(null);

  useEffect(() => {
    const role = getUserRoleFromCookie();
    setUserRole(role);
  }, []);

  const getHomeRoute = () => {
    if (userRole === 'admin' || userRole === 'tutor') {
      return '/dashboard';
    } else {
      return '/';
    }
  };

  return (
    <div className="h-screen flex flex-col items-center justify-center bg-gray-100 text-center px-4">
      <h1 className="text-7xl font-bold text-indigo-600 mb-4">
        <Icon icon="material-symbols:construction" width="64" height="64" />
      </h1>
      <h2 className="text-2xl font-semibold text-gray-800">
        This page is under construction.
      </h2>
      <p className="text-gray-600 mt-2">
        We are working hard to bring you this page. Please be patient and check
        back later.
      </p>

      <Link
        href={getHomeRoute()}
        className="flex items-center gap-2 mt-6 px-6 py-3 bg-indigo-600 text-white font-medium rounded-lg shadow-md hover:bg-indigo-700 transition"
      >
        Go Back
        <Icon icon="akar-icons:arrow-back" width="16" height="16" />
      </Link>
    </div>
  );
};

export default NotFound;
