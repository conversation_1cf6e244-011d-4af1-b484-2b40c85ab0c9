'use client';
import React, { useEffect, useState } from 'react';
import Image from 'next/image';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import api from '@/lib/api';
import useDataFetch from '@/hooks/useDataFetch';
import OwnedItemCart from '../_component/OwnedItemCart';
import Link from 'next/link';
import { Icon } from '@iconify/react';
import { useRouter } from 'next/navigation';

const OwnedItemPage = () => {
  const router = useRouter();
  const [previewItem, setPreviewItem] = useState(null);
  const [selectedItem, setSelectedItem] = useState(null);
  const [isItemSelected, setIsItemSelected] = useState(false);
  const [showIntro, setShowIntro] = useState(true);

  // Fetch owned items
  const { data: ownedItemsData, isLoading: ownedLoading } = useDataFetch({
    queryKey: ['owned-items'],
    endPoint: '/student/owned-items',
    params: {},
  });

  // Fetch recommended items
  const { data: recommendedItemsData, isLoading: recommendedLoading } =
    useDataFetch({
      queryKey: ['recommended-items'],
      endPoint: '/shop/items/available',
      params: {},
    });

  const {
    data,
    isLoading,
    refetch: cartRefetch,
  } = useDataFetch({
    queryKey: '/cart-items',
    endPoint: '/shop/cart',
  });

  const cartItems = data?.items || [];

  useEffect(() => {
    const isItemSelected = (item) =>
      cartItems.some((cartItem) => cartItem?.id === item?.id);
    setIsItemSelected(isItemSelected(selectedItem));
  }, [selectedItem, isLoading]);

  // Use API data or fallback to empty arrays
  const ownedItems = ownedItemsData?.items || [];
  const recommendedItems = recommendedItemsData?.items || [];

  const handleMouseEnter = (item) => {
    setPreviewItem(item);
  };

  const handleMouseLeave = () => {
    setPreviewItem(null);
  };

  const handleItemClick = (item, itemType) => {
    if (itemType === 'owned') {
      setSelectedItem({ ...item, isOwned: true });
    } else {
      setSelectedItem({ ...item, isOwned: false });
    }
  };

  const closeModal = () => {
    setSelectedItem(null);
  };

  // Handle download actions
  const handleDownload = (format) => {
    console.log(`Downloading in ${format} format`);
  };

  const handleAddToCart = async () => {
    try {
      const response = await api.post('/shop/cart/add', {
        shopItemId: selectedItem.id,
        quantity: 1,
      });
      cartRefetch();
    } catch (error) {
      console.log(error);
    }
  };

  const handlePurchaseContinue = async () => {
    try {
      const response = await api.post('/shop/cart/add', {
        shopItemId: selectedItem.id,
        quantity: 1,
      });
      cartRefetch();
      router.push('/checkout');
    } catch (error) {
      console.log(error);
    }
  };

  if (ownedLoading || recommendedLoading) {
    return (
      <div className="container mx-auto p-4 flex justify-center items-center min-h-[calc(100vh-180px)]">
        <div className="text-xl">Loading items...</div>
      </div>
    );
  }

  return showIntro ? (
    <div className="bg-[#e6f7f7] flex flex-col items-center justify-center min-h-[calc(100vh-180px)] p-6">
      <div className="max-w-2xl bg-white rounded-xl  p-8 text-center">
        <div className="flex justify-center mb-6">
          <Icon icon="emojione:department-store" width="64" height="64" />
        </div>
        <h1 className="text-3xl font-bold text-[#8B4513] mb-3">
          Welcome to Our Store!
        </h1>

        <p className="text-lg text-gray-700 mb-8">
          Discover amazing items and exclusive deals. Browse through our
          collection of owned items and recommendations just for you!
        </p>

        <div className="flex justify-center gap-4">
          <button
            onClick={() => setShowIntro(false)}
            className="bg-[#8B4513] text-white px-6 py-2 rounded-full font-medium hover:bg-[#A0522D] transition"
          >
            Get Started
          </button>
        </div>
      </div>
    </div>
  ) : (
    <div className="mx-auto p-6 rounded-lg bg-[#e6f7f7] min-h-screen">
      {/* My owned items section */}
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-[#8B4513] mb-6 ml-2">
          My owned item
        </h1>
        <Link
          href="/checkout?type=store"
          className="relative bg-white p-2 rounded-lg shadow-lg cursor-pointer hover:bg-gray-100 transition"
        >
          <Icon icon="mdi:cart-outline" className="w-6 h-6 text-gray-700" />
          {cartItems.length > 0 && (
            <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center">
              {cartItems.length || 0}
            </span>
          )}
        </Link>
      </div>

      <div className="flex flex-wrap gap-4 mb-12">
        {ownedItems.length > 0 ? (
          ownedItems.map((item) => (
            <div
              key={item.id}
              className="bg-[#FFF9FB] rounded-lg shadow-md p-2 flex items-center justify-center cursor-pointer hover:shadow-lg transition-all duration-300 w-[124px] h-[124px]"
              onMouseEnter={() => handleMouseEnter(item)}
              onMouseLeave={handleMouseLeave}
              onClick={() => handleItemClick(item, 'owned')}
            >
              <Image
                src={item?.filePath}
                alt={item.title}
                width={144}
                height={144}
              />
            </div>
          ))
        ) : (
          <div className="w-full text-center py-8">No owned items found</div>
        )}
      </div>

      {/* Decorative floral divider */}
      <div className="flex justify-center mb-8">
        <div className="relative w-full max-w-3xl flex items-center">
          <div className="h-0.5 bg-green-500 w-full absolute"></div>
          <div className="w-full flex justify-between items-center relative px-4">
            {/* Leaf and flower pattern */}
            <div className="flex items-center w-full justify-between">
              <div className="text-green-500 text-2xl">🍃</div>
              <div className="text-pink-400 text-xl">🌸</div>
              <div className="text-green-500 text-2xl transform rotate-45">
                🍃
              </div>
              <div className="text-pink-400 text-xl">🌸</div>
              <div className="text-green-500 text-2xl transform -rotate-45">
                🍃
              </div>
              <div className="text-pink-400 text-xl">🌸</div>
              <div className="text-green-500 text-2xl">🍃</div>
            </div>
          </div>
        </div>
      </div>

      {/* You may also try section */}
      <h2 className="text-2xl text-center font-bold text-[#8B4513] mb-6">
        You may also try
      </h2>

      <div className="flex flex-wrap gap-4">
        {recommendedItems.length > 0 ? (
          recommendedItems.map((item) => (
            <div
              key={item.id}
              className="bg-[#FFF9FB] rounded-lg shadow-md p-3 text-center cursor-pointer hover:shadow-lg transition-all duration-300 w-auto max-h-36 space-y-2 relative"
              onMouseEnter={() => handleMouseEnter(item)}
              onMouseLeave={handleMouseLeave}
              onClick={() => handleItemClick(item)}
            >
              {item?.discountPercentage > 0 && (
                <span className="absolute top-0 left-0 bg-[#C6661D] text-white text-xs font-bold min-w-20 px-4 rounded-tl rounded-br-2xl">
                  {item?.discountPercentage}% off
                </span>
              )}
              <Image
                src={item?.filePath}
                alt={item.title}
                width={100}
                height={100}
                className="object-contain h-24 w-24"
              />
              <div
                className={`flex items-center ${
                  item?.finalPrice > item?.price
                    ? 'justify-between'
                    : 'justify-center'
                } text-sm`}
              >
                {item?.finalPrice > item?.price && (
                  <span className="font-semibold line-through">
                    ${item?.price}
                  </span>
                )}
                <span>${item?.finalPrice}</span>
              </div>
            </div>
          ))
        ) : (
          <div className="w-full text-center py-8">
            No recommended items found
          </div>
        )}
      </div>

      {/* Item Detail Modal - Two cards on top of a background card */}
      {selectedItem && (
        <div
          className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
          onClick={closeModal}
        >
          {/* Background card */}
          <div
            className={`bg-white rounded-xl shadow-xl p-6 ${
              !selectedItem?.isOwned ? 'max-w-5xl' : 'max-w-3xl'
            }  w-full relative`}
            onClick={(e) => e.stopPropagation()}
          >
            {/* Close button */}
            <button
              onClick={closeModal}
              className="absolute top-4 right-4 text-gray-500 hover:text-red-500 z-10"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>

            {/* Two cards container */}
            <div className="flex gap-4 w-full">
              {/* Left Card - Image */}
              <div
                className={`bg-[#FFFDE7] rounded-lg overflow-hidden ${
                  selectedItem?.isOwned ? 'w-full' : 'w-3/5'
                }`}
                style={{ boxShadow: '0 0 10px rgba(0, 0, 0, 0.2)' }}
              >
                <div className="p-4 flex items-center justify-center h-full">
                  <Image
                    src={
                      selectedItem.filePath ||
                      '/assets/images/all-img/avatar.png'
                    }
                    alt={selectedItem.title || 'Item preview'}
                    width={400}
                    height={400}
                    className="object-contain"
                  />
                </div>
              </div>

              {/* Right Card - Details */}
              {!selectedItem?.isOwned && (
                <div
                  className="bg-white rounded-lg overflow-hidden w-2/5 p-6"
                  style={{ boxShadow: '0 0 10px rgba(0, 0, 0, 0.2)' }}
                >
                  <h3 className="text-xl font-bold mb-3">
                    {selectedItem.title || 'Emoji12364'}
                  </h3>{console.log(selectedItem)}

                  <div className="mb-4">
                    <p className="text-sm text-gray-600">
                      Item Type: {selectedItem?.categoryName || 'Illustration'}
                    </p>

                    <p className="text-sm text-gray-600">
                      Details: {selectedItem?.description || 'Illustration'}
                    </p>
                  </div>

                  {/* <div className="mb-2">
                    <p className="text-sm text-gray-600">
                      Size: (512/512 px) (1024/1024 px)
                    </p>
                  </div>
                  <div className="mb-6">
                    <p className="text-sm text-gray-600">
                      Size: (512/512 px) (1024/1024 px)
                    </p>
                  </div>

                  <div className="flex gap-2 mb-8">
                    <button
                      className="bg-gray-500 text-white px-4 py-1 rounded-full text-sm flex items-center shadow-md border border-gray-600"
                      style={{
                        boxShadow:
                          '0 0 3px rgba(0, 0, 0, 0.5), inset 0 0 2px rgba(255, 255, 255, 0.3)',
                      }}
                      onClick={() => handleDownload('PNG')}
                    >
                      Png <span className="ml-1">↓</span>
                    </button>
                    <button
                      className="bg-gray-500 text-white px-4 py-1 rounded-full text-sm flex items-center shadow-md border border-gray-600"
                      style={{
                        boxShadow:
                          '0 0 3px rgba(0, 0, 0, 0.5), inset 0 0 2px rgba(255, 255, 255, 0.3)',
                      }}
                      onClick={() => handleDownload('JPG')}
                    >
                      Jpg <span className="ml-1">↓</span>
                    </button>
                    <button
                      className="bg-gray-500 text-white px-4 py-1 rounded-full text-sm flex items-center shadow-md border border-gray-600"
                      style={{
                        boxShadow:
                          '0 0 3px rgba(0, 0, 0, 0.5), inset 0 0 2px rgba(255, 255, 255, 0.3)',
                      }}
                      onClick={() => handleDownload('Vector')}
                    >
                      Vector <span className="ml-1">↓</span>
                    </button>
                  </div> */}

                  <div className="border-t border-dashed border-gray-300 pt-6 mb-6"></div>

                  <div className="flex flex-col gap-3">
                    <button
                      onClick={handlePurchaseContinue}
                      className="bg-[#FFF8DC] text-black-800 rounded-full py-2 font-medium border border-yellow-950"
                      style={{ boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)' }}
                    >
                      Continue To Purchase
                    </button>
                    <button
                      onClick={handleAddToCart}
                      disabled={isItemSelected}
                      className={`${
                        isItemSelected
                          ? 'bg-gray-300 cursor-not-allowed'
                          : 'bg-yellow-500'
                      } text-black-800 rounded-full py-2 font-medium border border-yellow-950 w-3/4 mx-auto `}
                      style={{
                        boxShadow:
                          '0 4px 6px rgba(0, 0, 0, 0.2), inset 0 -2px 5px rgba(0, 0, 0, 0.1), inset 0 2px 3px rgba(255, 255, 255, 0.5)',
                        transform: 'translateY(-1px)',
                      }}
                    >
                      {isItemSelected ? 'Added To Cart' : 'Add To Cart'}
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* <OwnedItemCart cartItems={cartItems} cartRefetch={cartRefetch} /> */}
    </div>
  );
};

export default OwnedItemPage;
