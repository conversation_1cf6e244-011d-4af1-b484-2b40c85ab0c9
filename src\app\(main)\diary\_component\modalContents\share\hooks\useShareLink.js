import { useState } from 'react';
import { toast } from 'sonner';
import api from '@/lib/api';

const useShareLink = (diaryEntryId) => {
  const [shareLink, setShareLink] = useState('');
  const [qrCodeUrl, setQrCodeUrl] = useState('');
  const [isGeneratingLink, setIsGeneratingLink] = useState(false);
  const [isCopied, setIsCopied] = useState(false);

  // Handle generating a shareable link
  const handleGenerateLink = async () => {
    if (!diaryEntryId) {
      toast.error('No diary entry found to share');
      return;
    }

    setIsGeneratingLink(true);

    try {
      // Make API call to generate a shareable link
      const response = await api.post(`/diary/entries/${diaryEntryId}/share`, {
        entryId: diaryEntryId
      });

      if (response.success) {
        // Update the share link with the generated link from the API
        setShareLink(response.data?.shareUrl || response.data?.shareLink || response.data?.url || '');
        // Set the QR code link if available
        setQrCodeUrl(response.data?.qrCodeUrl || '');
      } else {
        throw new Error(response.message || 'Failed to generate share link');
      }
    } catch (error) {
      console.error('Error generating share link:', error);
      toast.error(error.message || 'Failed to generate share link. Please try again.');
    } finally {
      setIsGeneratingLink(false);
    }
  };

  // Handle copy link button click
  const handleCopyLink = async () => {
    if (!shareLink) {
      toast.error('No link to copy. Please generate a link first.');
      return;
    }

    try {
      // Check if the modern Clipboard API is available
      if (navigator.clipboard && window.isSecureContext) {
        await navigator.clipboard.writeText(shareLink);
      } else {
        // Fallback for older browsers or non-secure contexts
        await fallbackCopyToClipboard(shareLink);
      }

      setIsCopied(true);

      // Reset the copied state after 2 seconds
      setTimeout(() => {
        setIsCopied(false);
      }, 2000);

      toast.success('Link copied to clipboard!');
    } catch (error) {
      console.error('Failed to copy link:', error);
      toast.error('Failed to copy link. Please try again.');
    }
  };

  // Fallback function for copying to clipboard
  const fallbackCopyToClipboard = (text) => {
    return new Promise((resolve, reject) => {
      // Create a temporary textarea element
      const textArea = document.createElement('textarea');
      textArea.value = text;

      // Make the textarea invisible
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      textArea.setAttribute('readonly', '');
      textArea.style.opacity = '0';

      document.body.appendChild(textArea);

      try {
        // Select and copy the text
        textArea.focus();
        textArea.select();
        textArea.setSelectionRange(0, 99999); // For mobile devices

        const successful = document.execCommand('copy');
        document.body.removeChild(textArea);

        if (successful) {
          resolve();
        } else {
          reject(new Error('Copy command failed'));
        }
      } catch (error) {
        document.body.removeChild(textArea);
        reject(error);
      }
    });
  };

  // Handle QR code download
  const handleDownloadQR = async () => {
    if (!qrCodeUrl) {
      toast.error('No QR code available to download');
      return;
    }

    try {
      // Use the actual QR code URL directly for download
      const response = await fetch(qrCodeUrl);

      if (!response.ok) {
        throw new Error(`Failed to download QR code: ${response.statusText}`);
      }

      // Get the blob from the response
      const blob = await response.blob();

      // Create a URL for the blob
      const blobUrl = URL.createObjectURL(blob);

      // Create a temporary anchor element to trigger the download
      const link = document.createElement('a');
      link.href = blobUrl;
      link.download = `diary-qr-${diaryEntryId}.png`;
      document.body.appendChild(link);
      link.click();

      // Clean up
      document.body.removeChild(link);
      URL.revokeObjectURL(blobUrl);

      toast.success('QR code download started');
    } catch (error) {
      console.error('Error downloading QR code:', error);
      toast.error('Failed to download QR code. Please try again.');
    }
  };

  return {
    shareLink,
    qrCodeUrl,
    isGeneratingLink,
    isCopied,
    handleGenerateLink,
    handleCopyLink,
    handleDownloadQR,
  };
};

export default useShareLink;
