'use client';
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import TextStyle from '@tiptap/extension-text-style';
import { Color } from '@tiptap/extension-color';
import TextAlign from '@tiptap/extension-text-align';
import {
  useEffect,
  useImperativeHandle,
  forwardRef,
  useState,
  useRef,
} from 'react';
import { Icon } from '@iconify/react';
import Strike from '@tiptap/extension-strike';

const SimpleTiptapEditor = forwardRef(
  (
    {
      // TinyMCE compatibility props
      editorRef,
      setValue,
      initialValue,
      onAutoSave,
      height = 300,
      maxWords,

      // Direct usage props
      onInit,
      onEditorChange,
    },
    ref
  ) => {
    const [lastContent, setLastContent] = useState('');
    const [typingTimer, setTypingTimer] = useState(null);
    const [wordCount, setWordCount] = useState(0);
    const [isOverLimit, setIsOverLimit] = useState(false);
    // const [showWarning, setShowWarning] = useState(false);
    const [showColorPicker, setShowColorPicker] = useState(false);
    const colorPickerRef = useRef(null);

    // Color palette
    const colors = [
      '#000000',
      '#434343',
      '#666666',
      '#999999',
      '#B7B7B7',
      '#CCCCCC',
      '#D9D9D9',
      '#EFEFEF',
      '#F3F3F3',
      '#FFFFFF',
      '#980000',
      '#FF0000',
      '#FF9900',
      '#FFFF00',
      '#00FF00',
      '#00FFFF',
      '#4A86E8',
      '#0000FF',
      '#9900FF',
      '#FF00FF',
      '#E6B8AF',
      '#F4CCCC',
      '#FCE5CD',
      '#FFF2CC',
      '#D9EAD3',
      '#D0E0E3',
      '#C9DAF8',
      '#CFE2F3',
      '#D9D2E9',
      '#EAD1DC',
      '#DD7E6B',
      '#EA9999',
      '#F9CB9C',
      '#FFE599',
      '#B6D7A8',
      '#A2C4C9',
      '#A4C2F4',
      '#9FC5E8',
      '#B4A7D6',
      '#D5A6BD',
      '#CC4125',
      '#E06666',
      '#F6B26B',
      '#FFD966',
      '#93C47D',
      '#76A5AF',
      '#6D9EEB',
      '#6FA8DC',
      '#8E7CC3',
      '#C27BA0',
    ];

    // Count words in HTML content
    const countWords = (html) => {
      // Remove HTML tags
      const text = html.replace(/<[^>]*>/g, ' ');
      // Remove entities
      const cleanText = text.replace(
        /&nbsp;|&amp;|&lt;|&gt;|&quot;|&#39;/g,
        ' '
      );
      // Remove extra spaces and split by whitespace
      const words = cleanText
        .trim()
        .split(/\s+/)
        .filter((word) => word.length > 0);
      return words.length;
    };

    // Handle content change with debounce for auto-save
    const handleEditorChange = (content) => {
      // Count words
      const count = countWords(content);

      // Check if over limit
      const overLimit = maxWords && count > maxWords;

      // If over limit and we have previous content, restore it
      if (overLimit && editor && lastContent) {
        // Prevent typing by restoring the previous content
        editor.commands.setContent(lastContent);
        // if (!showWarning) {
        // setShowWarning(true);
        // setTimeout(() => setShowWarning(false), 2000);
        // }
        return; // Exit early to prevent further processing
      }

      // Update word count and limit status
      setWordCount(count);
      setIsOverLimit(overLimit);

      // Update content since it's within limits or we don't have previous content yet
      if (setValue) setValue(content);
      if (onEditorChange) onEditorChange(content);

      // Store the content as last content if it's within limits
      if (!overLimit) {
        setLastContent(content);
      }

      // Auto-save functionality
      if (onAutoSave) {
        // Clear previous timer
        if (typingTimer) clearTimeout(typingTimer);

        // Set a new timer to trigger auto-save after 500ms of inactivity
        const timer = setTimeout(() => {
          // Only save if content has changed and contains a space (word completion)
          if (content !== lastContent) {
            onAutoSave({ answer: content });
          }
        }, 500);

        setTypingTimer(timer);
      }
    };

    const editor = useEditor({
      extensions: [
        StarterKit,
        TextStyle,
        Color,
        TextAlign.configure({
          types: ['heading', 'paragraph'],
        }),
        Strike,
        {
          name: 'disablePaste',
          addProseMirrorPlugins() {
            return [
              new Plugin({
                key: new PluginKey('disablePaste'),
                props: {
                  handlePaste(view, event, slice) {
                    event.preventDefault();
                    console.log('Paste prevented!');
                    return true;
                  },
                },
              }),
            ];
          },
        },
      ],
      immediatelyRender: false,
      content: initialValue || '<p></p>',
      onUpdate: ({ editor }) => {
        const content = editor.getHTML();
        handleEditorChange(content);
      },
      editorProps: {
        attributes: {
          class:
            'prose prose-sm sm:prose lg:prose-lg xl:prose-2xl mx-auto focus:outline-none tiptap-editor',
          style: `min-height: ${height}px; font-family: Helvetica, Arial, sans-serif; font-size: 14px; padding: 12px;`,
          spellcheck: 'false',
          autocomplete: 'off',
          autocapitalize: 'off',
          autocorrect: 'off',
          'data-gramm': 'false',
          'data-gramm_editor': 'false',
          'data-enable-grammarly': 'false',
          'data-grammarly-disable': 'true',
          'data-ms-editor': 'false',
          'data-lt-installed': 'false',
          'data-spell-check': 'false',
          'data-grammar-check': 'false',
          'data-auto-correct': 'false',
        },
      },
    });

    // Expose editor methods to parent component via ref (for both editorRef and ref)
    useImperativeHandle(
      ref,
      () => ({
        getContent: () => editor?.getHTML() || '',
        setContent: (content) => editor?.commands.setContent(content),
        focus: () => editor?.commands.focus(),
      }),
      [editor]
    );

    useImperativeHandle(
      editorRef,
      () => ({
        getContent: () => editor?.getHTML() || '',
        setContent: (content) => editor?.commands.setContent(content),
        focus: () => editor?.commands.focus(),
      }),
      [editor]
    );

    // Call onInit when editor is ready
    useEffect(() => {
      if (editor && onInit) {
        onInit(null, {
          getContent: () => editor.getHTML(),
          setContent: (content) => editor.commands.setContent(content),
          focus: () => editor.commands.focus(),
        });
      }
    }, [editor, onInit]);

    // Clean up timer on unmount
    useEffect(() => {
      return () => {
        if (typingTimer) clearTimeout(typingTimer);
      };
    }, [typingTimer]);

    // Calculate initial word count when component mounts
    useEffect(() => {
      if (initialValue && editor) {
        const count = countWords(initialValue);
        setWordCount(count);
        setIsOverLimit(maxWords && count > maxWords);

        // Always set the initial content as lastContent to allow editing
        // even if it's over the limit (for existing submissions)
        setLastContent(initialValue);
      }
    }, [initialValue, maxWords, editor]);

    // Update content when initialValue changes
    useEffect(() => {
      if (editor && initialValue !== undefined) {
        const currentContent = editor.getHTML();
        if (currentContent !== initialValue) {
          editor.commands.setContent(initialValue || '<p></p>');
        }
      }
    }, [initialValue, editor]);

    // Click outside to close color picker
    useEffect(() => {
      const handleClickOutside = (event) => {
        if (
          colorPickerRef.current &&
          !colorPickerRef.current.contains(event.target)
        ) {
          setShowColorPicker(false);
        }
      };

      if (showColorPicker) {
        document.addEventListener('mousedown', handleClickOutside);
      }

      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }, [showColorPicker]);

    return (
      <div className="relative">
        {/* Toolbar */}
        <div className="border border-gray-300 border-b-0 rounded-t-lg bg-gray-50 p-2 flex flex-wrap items-center gap-1">
          {/* Undo/Redo */}
          <button
            type="button"
            onClick={() => editor?.chain().focus().undo().run()}
            disabled={!editor?.can().undo()}
            className="p-1.5 text-sm border rounded hover:bg-gray-200 disabled:opacity-50 flex items-center justify-center"
            title="Undo"
          >
            <Icon icon="mdi:undo-variant" width={20} height={20} />
          </button>
          <button
            type="button"
            onClick={() => editor?.chain().focus().redo().run()}
            disabled={!editor?.can().redo()}
            className="p-1.5 text-sm border rounded hover:bg-gray-200 disabled:opacity-50 flex items-center justify-center"
            title="Redo"
          >
            <Icon icon="mdi:redo-variant" width={20} height={20} />
          </button>

          <div className="w-px h-6 bg-gray-300 mx-1"></div>

          {/* Format Dropdown */}
          <select
            className="px-2 py-1 text-sm border rounded hover:bg-gray-200 bg-white min-w-[100px]"
            onChange={(e) => {
              const value = e.target.value;
              if (value === 'paragraph') {
                editor?.chain().focus().setParagraph().run();
              } else if (value === 'heading1') {
                editor?.chain().focus().toggleHeading({ level: 1 }).run();
              } else if (value === 'heading2') {
                editor?.chain().focus().toggleHeading({ level: 2 }).run();
              } else if (value === 'heading3') {
                editor?.chain().focus().toggleHeading({ level: 3 }).run();
              }
            }}
            value={
              editor?.isActive('heading', { level: 1 })
                ? 'heading1'
                : editor?.isActive('heading', { level: 2 })
                ? 'heading2'
                : editor?.isActive('heading', { level: 3 })
                ? 'heading3'
                : 'paragraph'
            }
          >
            <option value="paragraph">Paragraph</option>
            <option value="heading1">Heading 1</option>
            <option value="heading2">Heading 2</option>
            <option value="heading3">Heading 3</option>
          </select>

          <div className="w-px h-6 bg-gray-300 mx-1"></div>

          {/* Bold/Italic/Strikethrough */}
          <button
            type="button"
            onClick={() => editor?.chain().focus().toggleBold().run()}
            className={`p-1.5 text-sm border rounded hover:bg-gray-200 flex items-center justify-center font-bold ${
              editor?.isActive('bold') ? 'bg-gray-300' : ''
            }`}
            title="Bold"
          >
            <Icon icon="mdi:format-bold" width={20} height={20} />
          </button>
          <button
            type="button"
            onClick={() => editor?.chain().focus().toggleItalic().run()}
            className={`p-1.5 text-sm border rounded hover:bg-gray-200 flex items-center justify-center italic ${
              editor?.isActive('italic') ? 'bg-gray-300' : ''
            }`}
            title="Italic"
          >
            <Icon icon="mdi:format-italic" width={20} height={20} />
          </button>
          <button
            type="button"
            onClick={() => editor?.chain().focus().toggleStrike().run()}
            className={`p-1.5 text-sm border rounded hover:bg-gray-200 flex items-center justify-center ${
              editor?.isActive('strike') ? 'bg-gray-300' : ''
            }`}
            title="Strikethrough"
          >
            <Icon
              icon="mdi:format-strikethrough-variant"
              width={20}
              height={20}
            />
          </button>

          {/* Text Color */}
          <div className="relative">
            <button
              type="button"
              onClick={() => setShowColorPicker(!showColorPicker)}
              className="p-1.5 text-sm border rounded hover:bg-gray-200 flex items-center justify-center"
              title="Text Color"
            >
              <Icon icon="mdi:format-color-text" width={20} height={20} />
              <Icon
                icon="mdi:chevron-down"
                width={16}
                height={16}
                className="ml-1"
              />
            </button>

            {/* Color Picker Dropdown */}
            {showColorPicker && (
              <div
                ref={colorPickerRef}
                className="absolute top-full left-0 mt-1 bg-white border border-gray-300 rounded-lg shadow-lg p-3 z-50 w-64"
              >
                <div className="grid grid-cols-10 gap-1">
                  {colors.map((color, index) => (
                    <button
                      key={index}
                      type="button"
                      className="w-6 h-6 rounded border border-gray-300 hover:scale-110 transition-transform"
                      style={{ backgroundColor: color }}
                      onClick={() => {
                        editor?.chain().focus().setColor(color).run();
                        setShowColorPicker(false);
                      }}
                      title={color}
                    />
                  ))}
                </div>
                <div className="mt-2 pt-2 border-t border-gray-200">
                  <button
                    type="button"
                    className="text-sm text-gray-600 hover:text-gray-800"
                    onClick={() => {
                      editor?.chain().focus().unsetColor().run();
                      setShowColorPicker(false);
                    }}
                  >
                    Remove Color
                  </button>
                </div>
              </div>
            )}
          </div>

          <div className="w-px h-6 bg-gray-300 mx-1"></div>

          {/* Alignment */}
          <button
            type="button"
            onClick={() => editor?.chain().focus().setTextAlign('left').run()}
            className="p-1.5 text-sm border rounded hover:bg-gray-200 flex items-center justify-center"
            title="Align Left"
          >
            <Icon icon="mdi:format-align-left" width={20} height={20} />
          </button>
          <button
            type="button"
            onClick={() => editor?.chain().focus().setTextAlign('center').run()}
            className="p-1.5 text-sm border rounded hover:bg-gray-200 flex items-center justify-center"
            title="Align Center"
          >
            <Icon icon="mdi:format-align-center" width={20} height={20} />
          </button>
          <button
            type="button"
            onClick={() => editor?.chain().focus().setTextAlign('right').run()}
            className="p-1.5 text-sm border rounded hover:bg-gray-200 flex items-center justify-center"
            title="Align Right"
          >
            <Icon icon="mdi:format-align-right" width={20} height={20} />
          </button>
          <button
            type="button"
            onClick={() =>
              editor?.chain().focus().setTextAlign('justify').run()
            }
            className="p-1.5 text-sm border rounded hover:bg-gray-200 flex items-center justify-center"
            title="Justify"
          >
            <Icon icon="mdi:format-align-justify" width={20} height={20} />
          </button>

          <div className="w-px h-6 bg-gray-300 mx-1"></div>

          {/* Lists */}
          <button
            type="button"
            onClick={() => editor?.chain().focus().toggleBulletList().run()}
            className={`p-1.5 text-sm border rounded hover:bg-gray-200 flex items-center justify-center ${
              editor?.isActive('bulletList') ? 'bg-gray-300' : ''
            }`}
            title="Bullet List"
          >
            <Icon icon="mdi:format-list-bulleted" width={20} height={20} />
          </button>
          <button
            type="button"
            onClick={() => editor?.chain().focus().toggleOrderedList().run()}
            className={`p-1.5 text-sm border rounded hover:bg-gray-200 flex items-center justify-center ${
              editor?.isActive('orderedList') ? 'bg-gray-300' : ''
            }`}
            title="Numbered List"
          >
            <Icon icon="mdi:format-list-numbered" width={20} height={20} />
          </button>

          {/* Indent/Outdent */}
          <button
            type="button"
            onClick={() =>
              editor?.chain().focus().sinkListItem('listItem').run()
            }
            disabled={!editor?.can().sinkListItem('listItem')}
            className="p-1.5 text-sm border rounded hover:bg-gray-200 disabled:opacity-50 flex items-center justify-center"
            title="Indent"
          >
            <Icon icon="mdi:format-indent-increase" width={20} height={20} />
          </button>
          <button
            type="button"
            onClick={() =>
              editor?.chain().focus().liftListItem('listItem').run()
            }
            disabled={!editor?.can().liftListItem('listItem')}
            className="p-1.5 text-sm border rounded hover:bg-gray-200 disabled:opacity-50 flex items-center justify-center"
            title="Outdent"
          >
            <Icon icon="mdi:format-indent-decrease" width={20} height={20} />
          </button>

          {/* Remove Format */}
          <button
            type="button"
            onClick={() =>
              editor?.chain().focus().unsetAllMarks().clearNodes().run()
            }
            className="p-1.5 text-sm border rounded hover:bg-gray-200 flex items-center justify-center"
            title="Remove Format"
          >
            <Icon icon="mdi:format-clear" width={20} height={20} />
          </button>
        </div>

        {/* Editor */}
        <div
          className="border border-gray-300 rounded-b-lg bg-white"
          style={{ maxHeight: height, overflowY: 'auto' }}
        >
          <style
            dangerouslySetInnerHTML={{
              __html: `
            .tiptap-editor ul {
              list-style-type: disc !important;
              margin-left: 1.5rem !important;
              padding-left: 0 !important;
            }
            .tiptap-editor ol {
              list-style-type: decimal !important;
              margin-left: 1.5rem !important;
              padding-left: 0 !important;
            }
            .tiptap-editor li {
              display: list-item !important;
              margin-bottom: 0.25rem !important;
            }
            .tiptap-editor ul ul {
              list-style-type: circle !important;
            }
            .tiptap-editor ul ul ul {
              list-style-type: square !important;
            }
            .tiptap-editor ol ol {
              list-style-type: lower-alpha !important;
            }
            .tiptap-editor ol ol ol {
              list-style-type: lower-roman !important;
            }
            .tiptap-editor h1 {
              font-size: 2rem !important;
              font-weight: bold !important;
              margin: 1rem 0 !important;
            }
            .tiptap-editor h2 {
              font-size: 1.5rem !important;
              font-weight: bold !important;
              margin: 0.75rem 0 !important;
            }
            .tiptap-editor h3 {
              font-size: 1.25rem !important;
              font-weight: bold !important;
              margin: 0.5rem 0 !important;
            }
            .tiptap-editor p {
              margin: 0.5rem 0 !important;
            }
            .tiptap-editor strong {
              font-weight: bold !important;
            }
            .tiptap-editor em {
              font-style: italic !important;
            }
          `,
            }}
          />
          <EditorContent
            spellCheck={false}
            autoComplete="off"
            autoCapitalize="off"
            autoCorrect="off"
            data-gramm="false"
            data-gramm_editor="false"
            data-enable-grammarly="false"
            editor={editor}
          />
        </div>

        {/* Word count display - only show if maxWords is provided */}
        {/* {maxWords && (
          <div className="absolute left-2 w-[98.5%] z-20 flex justify-between items-center mt-2 text-sm">
            <div
              className={`font-medium ${
                isOverLimit
                  ? 'text-red-600 bg-red-50 px-2 py-1 rounded-md border border-red-200'
                  : 'text-gray-600'
              }`}
            >
              <span className={isOverLimit ? 'font-bold' : ''}>
                {wordCount} / {maxWords} words
              </span>
              {isOverLimit && (
                <span className="ml-2 text-red-600 font-bold">
                  (Maximum limit reached! Cannot add more words. Please edit or
                  remove content.)
                </span>
              )}
            </div>
          </div>
        )} */}
      </div>
    );
  }
);

SimpleTiptapEditor.displayName = 'SimpleTiptapEditor';

export default SimpleTiptapEditor;
