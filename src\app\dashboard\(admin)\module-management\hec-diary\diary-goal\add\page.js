'use client';
import NewTablePage from "@/components/form/NewTablePage";
import Link from 'next/link';
import React, { useState } from 'react';
import { Formik, Form } from 'formik';
import FormInput from '@/components/form/FormInput';
import * as Yup from 'yup';
import { useRouter } from 'next/navigation';
import api from '@/lib/api';

const createStage = () => {
  const router = useRouter();

  const validationSchema = Yup.object().shape({
    title: Yup.string().required('Title is required'),
    level: Yup.number()
      .required('Level is required')
      .min(1, 'Level must be at least 1')
      .integer('Level must be a whole number'),
    wordLimit: Yup.number()
      .required('Word limit is required')
      .min(1, 'Word limit must be at least 1')
      .integer('Word limit must be a whole number'),
    description: Yup.string().required('Description is required'),
    isActive: Yup.boolean(),
  });

  const initialValues = {
    title: '',
    level: '',
    wordLimit: '',
    description: '',
    isActive: true,
  };

  const handleSubmit = async (values, { setSubmitting, setFieldError }) => {
    try {
      // Prepare data for API
      const apiData = {
        title: values.title,
        level: parseInt(values.level),
        wordLimit: parseInt(values.wordLimit),
        description: values.description,
        isActive: values.isActive,
      };

      console.log('Submitting data:', apiData);

      // Make API call using your existing api utility
      const response = await api.post('/admin/diary/settings', apiData);
      console.log(response);

      // Redirect on success
      router.push('/dashboard/module-management/hec-diary/diary-goal');

    } catch (error) {
      console.error('Error creating stage:', error);
      
      // Handle validation errors from backend
      const validationErrors = error?.response?.data?.validationErrors;
      if (validationErrors) {
        Object.keys(validationErrors).forEach((field) => {
          setFieldError(`${field}`, validationErrors[field][0]);
        });
      }
    } finally {
      setSubmitting(false);
    }
  };

  const handleBack = () => {
    router.back();
  };

  return (
    <div className='min-h-screen bg-white p-5'>
      <div className="flex items-center gap-3 mb-3">
        <button
          onClick={handleBack}
          className="flex items-center justify-center w-8 h-8 rounded-full transition-colors"
          aria-label="Go back"
        >
          <svg 
            className="w-4 h-4 text-gray-600"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M15 19l-7-7 7-7"
            />
          </svg>
        </button>
        <h1 className="card-title text-black text-xl">Create Stage</h1>
      </div>

      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
      >
        {({ isSubmitting, errors, touched, resetForm, values, setFieldValue }) => (
          <Form className="mx-auto bg-gray-100 p-6 rounded-lg shadow-md">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              {/* Title Field */}
              <div className="col-span-1 md:col-span-2">
                <label htmlFor="title" className="block font-bold mb-1">
                 Stage Title <span className="text-red-500">*</span>
                </label>
                <FormInput
                  type="text"
                  name="title"
                  id="title"
                  placeholder="e.g., Beginner Level"
                />
              </div>

              {/* Level Field */}
              <div>
                <label htmlFor="level" className="block font-bold mb-1">
                  Level <span className="text-red-500">*</span>
                </label>
                <FormInput
                  type="number"
                  name="level"
                  id="level"
                  placeholder="e.g., 1"
                  min="1"
                />
              </div>

              {/* Word Limit Field */}
              <div>
                <label htmlFor="wordLimit" className="block font-bold mb-1">
                  Word Count <span className="text-red-500">*</span>
                </label>
                <FormInput
                  type="number"
                  name="wordLimit"
                  id="wordLimit"
                  placeholder="e.g., 100"
                  min="1"
                />
              </div>

              {/* Description Field */}
              <div className="col-span-1 md:col-span-2">
                <label htmlFor="description" className="block font-bold mb-1">
                  Description <span className="text-red-500">*</span>
                </label>
                <textarea
                  name="description"
                  id="description"
                  rows="3"
                  placeholder="e.g., Settings for beginners with basic vocabulary"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  value={values.description}
                  onChange={(e) => setFieldValue('description', e.target.value)}
                />
                {errors.description && touched.description && (
                  <div className="text-red-500 text-sm mt-1">{errors.description}</div>
                )}
              </div>

              {/* Active Status Field */}
              <div className="col-span-1 md:col-span-2">
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    name="isActive"
                    checked={values.isActive}
                    onChange={(e) => setFieldValue('isActive', e.target.checked)}
                    className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <span className="font-bold">Active Stage</span>
                </label>
                <p className="text-sm text-gray-600 mt-1">
                  Check this box to make the stage available to users
                </p>
              </div>
            </div>

            {/* Form Actions */}
            <div className="flex justify-end gap-4 mt-7">
              <button
                type="button"
                onClick={(e) => {
                  e.preventDefault();
                  console.log('Cancel clicked, resetting form');
                  resetForm();
                }}
                className="bg-gray-300 hover:bg-gray-400 text-black font-medium py-2 px-4 rounded transition-colors"
                disabled={isSubmitting}
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isSubmitting}
                className="bg-[#FFDE34] hover:bg-yellow-300 text-black font-medium py-2 px-4 rounded disabled:opacity-50 transition-colors"
              >
                {isSubmitting ? 'Creating...' : 'Create Stage'}
              </button>
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default createStage;