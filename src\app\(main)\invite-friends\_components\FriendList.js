import useDataFetch from '@/hooks/useDataFetch';
import { setFriendActiveTab } from '@/store/features/commonSlice';
import { Icon } from '@iconify/react';
import Image from 'next/image';
import React from 'react';
import { useDispatch } from 'react-redux';

const FriendList = () => {
  const dispatch = useDispatch();

  const { data, isLoading, refetch } = useDataFetch({
    queryKey: ['my-friends'],
    endPoint: '/student/friends/diary-follows',
  });

  const myFriends = data?.items || [];

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">My Followers</h2>

      {isLoading ? (
        <div className="flex justify-center items-center py-20">
          <Icon
            icon="eos-icons:loading"
            width="48"
            height="48"
            className="animate-spin text-yellow-500"
          />
        </div>
      ) : myFriends.length === 0 ? (
        <div className="flex flex-col items-center justify-center py-20">
          <Icon
            icon="mingcute:user-4-line"
            width="64"
            height="64"
            className="text-gray-400 mb-4"
          />
          <h3 className="text-xl font-medium text-gray-600">No Followers yet</h3>
          <p className="text-gray-500">Start adding Followers to see them here</p>
          <button
            className="mt-4 bg-yellow-100 text-gray-700 px-4 py-2 rounded-lg text-sm hover:bg-yellow-200 border border-gray-500"
            onClick={() => dispatch(setFriendActiveTab('search'))}
          >
            Invite diary followers
          </button>
        </div>
      ) : (
        <div className="space-y-4 max-h-[600px] overflow-y-auto">
          {myFriends?.map((friend) => (
            <div
              key={friend.id}
              className="flex items-center justify-between p-3 rounded-lg border border-gray-200"
            >
              <div className="flex items-center">
                <div className="w-12 h-12 rounded-full overflow-hidden mr-4">
                  <Image
                    src={
                      friend.profilePicture ||
                      '/assets/images/all-img/avatar.png'
                    }
                    alt={friend.name}
                    width={100}
                    height={100}
                    className="w-full h-full object-cover"
                  />
                </div>
                <div>
                  <p className="font-medium">{friend.diaryOwnerName}</p>
                  <p className="text-sm text-gray-500">
                    {friend?.requestedEmail}
                  </p>
                </div>
              </div>
              <div className="flex space-x-2">
                <button className="text-yellow-500 hover:text-yellow-600">
                  <Icon icon="bx:chat" width="24" height="24" />
                </button>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default FriendList;
