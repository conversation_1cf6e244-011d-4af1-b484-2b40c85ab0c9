import React from 'react';
import { useSelector } from 'react-redux';
import { Icon } from '@iconify/react';
import Link from 'next/link';
import { selectTotalUnreadCount } from '@/store/features/chatSlice';

const ChatBell = () => {
  const totalUnreadCount = useSelector(selectTotalUnreadCount);
  const { isAuth } = useSelector((state) => state.auth);

  // Don't show chat icon if user is not authenticated
  if (!isAuth) {
    return null;
  }

  return (
    <div className="relative">
      <Link href="/chat-app">
        <button
          className="relative p-2 rounded-full transition-colors hover:bg-gray-100"
          aria-label="Chat"
        >
          <Icon
            icon="icon-park-solid:communication"
            className="cursor-pointer text-gray-500 sm:h-7 sm:w-7 h-5 w-5"
          />

          {totalUnreadCount > 0 && (
            <span className="absolute top-0 right-0 flex items-center justify-center w-5 h-5 text-xs font-bold text-white bg-red-500 rounded-full">
              {totalUnreadCount > 9 ? '9+' : totalUnreadCount}
            </span>
          )}
        </button>
      </Link>
    </div>
  );
};

export default ChatBell;
