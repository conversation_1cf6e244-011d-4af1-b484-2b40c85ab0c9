'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import { motion, AnimatePresence } from 'framer-motion';
import { toast } from 'sonner';
import api from '@/lib/api';

const FeedbackModal = ({
  isOpen,
  onClose,
  entryId, // Keep as entryId to match the parent component
  submissionId, // Also accept submissionId for flexibility
  feedbacks = [],
  refetch,
  marking, // Add marking prop to access submissionFeedback
}) => {
  // Use whichever ID is provided
  const actualSubmissionId = submissionId || entryId;
  const [feedback, setFeedback] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Handle feedback submission - ONLY FOR FEEDBACK
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!feedback.trim()) {
      toast.error('Please enter feedback before submitting');
      return;
    }

    if (!actualSubmissionId) {
      toast.error('Submission ID is missing');
      return;
    }

    setIsSubmitting(true);
    
    console.log('FeedbackModal - Submitting feedback:', {
      submissionId: actualSubmissionId,
      submissionFeedback: feedback.trim(),
      // Only sending feedback, not touching taskRemarks or score
    });

    try {
      // Send ONLY submissionFeedback, don't include taskRemarks at all
      const response = await api.post('/tutor-qa-mission/QAMarking', {
        submissionId: actualSubmissionId,
        submissionFeedback: feedback.trim(), // THIS SHOULD UPDATE FEEDBACK ONLY
        // Don't send taskRemarks or score at all - let backend preserve existing values
      });

      console.log('FeedbackModal - API Response:', response);

      if (response.success) { 
        setFeedback('');
        if (refetch) {
          refetch();
        }
        onClose();
      } else {
        throw new Error(response.message || 'Failed to submit feedback');
      }
    } catch (error) {
      console.error('FeedbackModal - Error submitting feedback:', error);
      console.error('FeedbackModal - Full error:', error.response?.data);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle modal close
  const handleClose = () => {
    if (!isSubmitting) {
      setFeedback('');
      onClose();
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onClick={handleClose}
        >
          <motion.div
            className="bg-white rounded-xl shadow-xl w-full max-w-2xl overflow-hidden relative"
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            transition={{ type: 'spring', damping: 20, stiffness: 300 }}
            onClick={(e) => e.stopPropagation()}
          >
            {/* Wooden sign header */}
            <div className="relative">
              <div className="bg-[#FFF9FB] pt-2 pb-2 px-2 shadow-xl flex justify-center">
                <Image
                  src="/assets/images/all-img/wooden-feedback-sign.png"
                  alt="Teacher's Feedback"
                  width={300}
                  height={80}
                  priority
                />
              </div>
            </div>

            {/* Modal content */}
            <div className="p-6">
              {/* Previous Feedbacks Section */}
              <div className="max-h-60 overflow-y-auto mb-5">
                <h3 className="text-lg font-semibold text-gray-800 mb-3">Previous Feedback:</h3>
                <div className="space-y-3">
                  {/* Show current marking feedback if exists */}
                  {marking?.submissionFeedback && (
                    <div className="p-3 bg-green-50 rounded-lg border-l-4 border-green-400 shadow-sm">
                      <div className="flex justify-between items-center mb-1">
                        <span className="text-xs text-green-600 font-medium">Current Feedback</span>
                        {marking?.updatedAt && (
                          <span className="text-xs text-gray-500">
                            {new Date(marking.updatedAt).toLocaleDateString()}
                          </span>
                        )}
                      </div>
                      <p className="text-green-800 text-sm leading-relaxed mb-2">
                        {marking.submissionFeedback}
                      </p>
                    </div>
                  )}
                  
                  {/* Show other feedbacks if any */}
                  {feedbacks?.length > 0 ? (
                    feedbacks?.map((item, idx) => (
                      <div key={idx} className="p-3 bg-blue-50 rounded-lg border-l-4 border-blue-400 shadow-sm">
                        <div className="flex justify-between items-center mb-1">
                          <span className="text-xs text-blue-600 font-medium">Feedback #{idx + 1}</span>
                          {item?.createdAt && (
                            <span className="text-xs text-gray-500">
                              {new Date(item.createdAt).toLocaleDateString()}
                            </span>
                          )}
                        </div>
                        <p className="text-gray-800 text-sm leading-relaxed">
                          {item?.submissionFeedback || item?.content || 'No feedback content'}
                        </p>
                        {item?.givenBy && (
                          <p className="text-xs text-gray-500 mt-1">
                            By: {item.givenBy}
                          </p>
                        )}
                      </div>
                    ))
                  ) : (
                    !marking?.submissionFeedback && (
                      <div className="text-center py-6">
                        <div className="text-gray-400 text-4xl mb-2">📝</div>
                        <p className="text-gray-500 italic">No feedback given yet.</p>
                        <p className="text-xs text-gray-400 mt-1">Be the first to provide feedback!</p>
                      </div>
                    )
                  )}
                </div>
              </div>
              
              <form onSubmit={handleSubmit}>
                <div className="mb-4">
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    Add New Feedback
                    <span className="text-xs text-gray-500 font-normal block">
                      (This will be saved as submission feedback)
                    </span>
                  </label>
                  <textarea
                    value={feedback}
                    onChange={(e) => setFeedback(e.target.value)}
                    className="w-full p-3 border border-gray-300 rounded-lg h-32 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Write your feedback here..."
                    disabled={isSubmitting}
                  />
                </div>

                {/* Action buttons */}
                <div className="flex justify-end space-x-3 mt-6">
                  <button
                    type="button"
                    onClick={handleClose}
                    disabled={isSubmitting}
                    className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 disabled:opacity-50 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={isSubmitting || !feedback.trim()}
                    className="px-4 py-2 bg-yellow-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50 transition-colors flex items-center gap-2"
                  >
                    {isSubmitting ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                        Submitting...
                      </>
                    ) : (
                      'Submit Feedback'
                    )}
                  </button>
                </div>
              </form>
            </div>

            {/* Close button */}
            <button
              className="absolute top-2 right-2 text-gray-500 hover:text-gray-700 transition-colors"
              onClick={handleClose}
              disabled={isSubmitting}
            >
              <Image
                src="/assets/images/all-img/cross-bg.png"
                alt="Close"
                width={40}
                height={40}
                className="w-full h-auto"
                priority
              />
            </button>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default FeedbackModal;