'use client';

import React, { useState } from 'react';
import NewTablePage from '@/components/form/NewTablePage';
import { useRouter } from 'next/navigation';
import useDataFetch from '@/hooks/useDataFetch';

const MissionEssay = () => {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState('');
  const [searchField, setSearchField] = useState('title');
  const [sortDirection, setSortDirection] = useState('ASC');
  const [activeTab, setActiveTab] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [weekOrMonth, setWeekOrMonth] = useState('');

  const tabs = [
    { name: 'Weekly Mission', frequency: 'weekly' },
    { name: 'Monthly Mission', frequency: 'monthly' },
  ];

  const prepareParams = () => {
    const params = {
      page: currentPage,
      limit: rowsPerPage,
      timeFrequency: tabs[activeTab].frequency,
      sortDirection, // Always use sortField instead of searchField
    };

    if (searchField === 'title' && searchTerm) {
      params.title = searchTerm;
    } else if (
      (searchField === 'week' || searchField === 'month') &&
      weekOrMonth
    ) {
      params.weekOrMonth = weekOrMonth;
    }

    return params;
  };

  const {
    data: missionData,
    isLoading: loading,
    error,
  } = useDataFetch({
    queryKey: [
      'tutor-essay-missions',
      activeTab,
      currentPage,
      rowsPerPage,
      sortDirection,
      searchTerm,
      searchField,
      weekOrMonth,
    ],
    endPoint: '/tutor-essay/list/essay/mission',
    params: prepareParams(),
  });

  const columns = [
    { label: 'SUBMITTED BY', field: 'submittedBy' },
    {
      label: 'STATUS',
      field: 'status',
      cellRenderer: (_, row) => {
        const isReviewed = row.status === 'reviewed' || row?.isResubmission;
        const isSubmitted = row.status === 'submitted';
        const isNew = row.status === 'draft';
        const isConfirmed = row.status === 'confim';

        return (
          <div className="flex items-center">
            <span
              className={`px-3 py-1 rounded-full text-xs font-medium ${
                isReviewed
                  ? 'bg-green-100 text-green-800'
                  : isSubmitted
                  ? 'bg-red-100 text-red-800'
                  : isConfirmed
                  ? 'bg-blue-100 text-blue-800'
                  : 'bg-yellow-100 text-yellow-800'
              }`}
            >
              {isReviewed
                ? 'Reviewed ✓'
                : isSubmitted
                ? 'Not Reviewed Yet ✕'
                : isConfirmed
                ? 'Confirmed'
                : 'Incomplete ⏱'}
            </span>
          </div>
        );
      },
    },
    { label: 'SUBMISSION TITLE', field: 'title' },
    // { label: 'WORD LIMIT', field: 'wordLimit' },
    // { label: 'WORD COUNT', field: 'wordCount' },
  ];

  const actions = [
    {
      icon: 'heroicons-outline:eye',
      className:
        'text-blue-600 hover:text-blue-700 bg-blue-50 p-2 rounded-md cursor-pointer',
      onClick: (submission) =>
        router.push(
          `/dashboard/submission-management/hec-essay/review/mission/${submission?.id}?tab=missionEssay`
        ),
    },
  ];

  if (error) console.error('Error fetching mission essay data:', error);

  const processSubmissions = (submissions = []) => {
    return submissions.map((item) => {
      const latestSubmission =
        item.submissionHistory?.[item.submissionHistory.length - 1];

      return {
        id: item.id,
        title: item.title || 'Untitled',
        status: item.status,
        wordLimit:
          item.task?.wordLimitMinimum && item.task?.wordLimitMaximum
            ? `${item.task.wordLimitMinimum} - ${item.task.wordLimitMaximum}`
            : 'N/A',
        wordCount: latestSubmission?.wordCount || 0,
        submittedBy: item.createdBy || 'Unknown',
        submittedAt: latestSubmission?.submissionDate
          ? new Date(latestSubmission.submissionDate).toLocaleDateString()
          : new Date(item.createdAt).toLocaleDateString(),
        currentRevision: item.currentRevision || 1,
        submissionHistory: item.submissionHistory || [],
        task: item.task,
        diarySkin: item.diarySkin,
      };
    });
  };

  const submissions = processSubmissions(missionData?.items);
  const totalItems = missionData?.totalItems || 0;
  const totalPages = missionData?.totalPages || 1;

  const handlePageChange = (page) => setCurrentPage(page);
  const handleSort = (field) => {
    setSortDirection((prev) =>
      field === sortField ? (prev === 'ASC' ? 'DESC' : 'ASC') : 'ASC'
    );
    setCurrentPage(1);
  };
  const handleTabChange = (index) => {
    setActiveTab(index);
    setCurrentPage(1);
    setSearchTerm('');
    setWeekOrMonth('');
    setSearchField('title'); // Reset to default search field instead of empty
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold">Essay Submissions</h1>
      </div>

      <div className="flex border-b border-gray-200">
        {tabs.map((tab, index) => (
          <button
            key={index}
            className={`py-3 px-6 font-medium text-sm focus:outline-none ${
              activeTab === index
                ? 'text-black border-b-2 border-yellow-500 hover:bg-[#FEFCE8]'
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => handleTabChange(index)}
          >
            {tab.name}
          </button>
        ))}
      </div>

      <NewTablePage
        title=""
        showCreateButton={false}
        columns={columns}
        actions={actions}
        data={submissions}
        loading={loading}
        currentPage={currentPage}
        totalPages={totalPages}
        changePage={handlePageChange}
        totalItems={totalItems}
        rowsPerPage={rowsPerPage}
        setRowsPerPage={setRowsPerPage}
        showSearch={false}
        showNameFilter={false}
        showSortFilter={false}
        onSort={handleSort}
        sortDirection={sortDirection}
        showCheckboxes={false}
      />

      {submissions.length === 0 && !loading && (
        <div className="text-center py-8 text-gray-500">
          No submissions found.{' '}
          {searchTerm || weekOrMonth
            ? 'Try adjusting your search criteria.'
            : ''}
        </div>
      )}
    </div>
  );
};

export default MissionEssay;
