import React, { useState } from 'react';
import { useSelector } from 'react-redux';
import StageSelector from './StageSelector';
import DiaryWithDecorations from '@/components/diary/DiaryWithDecorations';
import HistoryModal from '../../novel/_components/HistoryModal';
import {
  selectIsSubmittingDecoration,
  selectDecorationSubmissionError,
  selectDecorationSubmissionSuccess,
} from '@/store/features/diarySlice';
import DecorationUndoRedo from '@/components/diary/DecorationUndoRedo';

const DiarySkin = ({
  today,
  todayEntry,
  subject,
  message,
  selectedSkin,
  handleStageChange,
  selectedStageTemplateId,
  onSubmitDecoration,
  isDecorating,
}) => {
  const [showHistoryModal, setShowHistoryModal] = useState(false);

  // Redux selectors for decoration submission state
  const isSubmittingDecoration = useSelector(selectIsSubmittingDecoration);
  const decorationSubmissionError = useSelector(
    selectDecorationSubmissionError
  );
  const decorationSubmissionSuccess = useSelector(
    selectDecorationSubmissionSuccess
  );

  return (
    <div className="bg-white h-full  p-2 shadow-xl">
      {/* Stage Selector with the selected stage template ID */}
      <div className="mb-4 flex items-center justify-between">
        <StageSelector
          onStageChange={handleStageChange}
          selectedTemplateId={selectedStageTemplateId}
        />
        <div className='flex items-center gap-3'>
          {todayEntry?.status === 'reviewed' && (
            <div className="text-sm text-[#864D0D] font-medium py-2 px-3  max-sm:py-1 max-sm:px-2 border border-dashed border-[#ECB306] bg-[#FFF189] rounded-md">
              score: {todayEntry?.score}
            </div>
          )}
          {/* Undo/Redo buttons - Only show when decorating */}
          {isDecorating && (
            <div className="flex justify-center my-2">
              <DecorationUndoRedo />
            </div>
          )}
        </div>
      </div>{' '}
      <div className="w-full  flex items-center justify-center overflow-hidden">
        <div className="" style={{ width: '100%', height: '100%' }}>
          <DiaryWithDecorations
            skin={selectedSkin?.templateContent}
            contentData={{
              subject: subject,
              body: message,
              date: today,
            }}
          />
        </div>
      </div>
      {/* Submit Decoration Button - Only show when decorating */}
      {isDecorating && (
        <div className="flex justify-center my-4">
          <button
            className={`text-sm font-medium px-6 py-2 rounded-full transition-colors duration-200 shadow ${
              isSubmittingDecoration
                ? 'bg-gray-300 border border-gray-400 text-gray-600 cursor-not-allowed'
                : 'bg-[#E6F7FF] border border-[#74B9D4] text-[#1366A0] hover:bg-[#D6F5FF] hover:border-[#5BA9C9]'
            }`}
            onClick={onSubmitDecoration}
            disabled={isSubmittingDecoration}
            aria-label="Submit Decoration"
          >
            {isSubmittingDecoration ? 'Submitting...' : 'Save Decoration'}
          </button>
        </div>
      )}
      {decorationSubmissionError && (
        <div className="flex justify-center my-2">
          <div className="text-sm text-red-600 bg-red-50 border border-red-200 px-4 py-2 rounded-full">
            {decorationSubmissionError}
          </div>
        </div>
      )}
      {/* View History Button */}
      {todayEntry?.hasHistory && !isDecorating && (
        <div className="flex justify-center my-4">
          <button
            className="bg-[#FFF9E6] border border-[#D4A574] text-[#8B4513] hover:bg-[#FFF5D6] hover:border-[#C19A5B] text-sm font-medium px-4 py-2 rounded-full transition-colors duration-200 shadow"
            onClick={() => setShowHistoryModal(true)}
            aria-label="View History"
          >
            View History
          </button>
        </div>
      )}
      {/* History Modal */}
      {showHistoryModal && todayEntry?.id && (
        <HistoryModal
          isOpen={showHistoryModal}
          onClose={() => setShowHistoryModal(false)}
          endPoint={`/diary/entries/${todayEntry?.id}/history`}
          moduleKey="diary"
        />
      )}
    </div>
  );
};

export default DiarySkin;
