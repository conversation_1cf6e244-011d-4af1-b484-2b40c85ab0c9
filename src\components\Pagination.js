import { Icon } from '@iconify/react';
import React from 'react';

const Pagination = ({
  changePage,
  currentPage,
  totalItems,
  rowsPerPage,
  setRowsPerPage,
}) => {
  return (
    <div className="flex items-center justify-between py-3">
      {/* Left: Record count */}
      <div className="text-sm text-gray-700">
        {`Showing ${(currentPage - 1) * rowsPerPage + 1} - ${Math.min(
          currentPage * rowsPerPage,
          totalItems
        )} of ${totalItems}`}
      </div>

      {/* Center: Pagination */}
      <div className="flex items-center space-x-2">
        <button
          onClick={() => changePage(currentPage - 1)}
          disabled={currentPage === 1}
          className="p-2 rounded-full hover:bg-gray-100 disabled:opacity-70 bg-[#FCF8EF]"
        >
          <Icon icon="mdi:chevron-left" className="w-5 h-5" />
        </button>

        {/* Show all page numbers */}
        {[...Array(Math.ceil(totalItems / rowsPerPage))].map((_, i) => (
          <button
            key={i + 1}
            onClick={() => changePage(i + 1)}
            className={`px-3 py-1 rounded ${
              currentPage === i + 1 ? 'bg-yellow-400 text-white' : 'text-gray-700'
            }`}
          >
            {i + 1}
          </button>
        ))}

        <button
          onClick={() => changePage(currentPage + 1)}
          disabled={currentPage * rowsPerPage >= totalItems}
          className="p-2 rounded-full hover:bg-gray-100 disabled:opacity-70 bg-[#FCF8EF]"
        >
          <Icon icon="mdi:chevron-right" className="w-5 h-5" />
        </button>
      </div>

      {/* Right: Rows per page */}
      <div className="flex items-center">
        <span className="text-sm text-gray-700 mr-2">Rows per page:</span>
        <select
          value={rowsPerPage}
          onChange={(e) => setRowsPerPage(Number(e.target.value))}
          className="border rounded px-2 py-1 text-sm"
        >
          <option value={10}>10</option>
          <option value={25}>25</option>
          <option value={50}>50</option>
          <option value={100}>100</option>
        </select>
      </div>
    </div>
  );
};

export default Pagination;
