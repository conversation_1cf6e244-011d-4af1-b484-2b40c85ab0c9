'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import api from '@/lib/api';
import { toast } from 'react-toastify';
import { useQuery } from '@tanstack/react-query';
import HecQALayout from '../../review/_components/HecQALayout';

const QASubmissionViewPage = () => {
  const params = useParams();
  const router = useRouter();
  const submissionId = params.id;
  
  const [submissionData, setSubmissionData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch submission details
  const fetchSubmissionDetails = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await api.get(`/tutor-qa-mission/submission-history/${submissionId}`);
      
      if (response?.success) {
        setSubmissionData(response.data);
      } else {
        throw new Error(response?.message || 'Failed to fetch submission details');
      }
    } catch (error) {
      console.error('Error fetching submission details:', error);
      setError(error.message || 'Error fetching submission details');
      toast.error(error.message || 'Error fetching submission details');
    } finally {
      setLoading(false);
    }
  };

  // Use React Query for data fetching
  const { isLoading, isError, error: queryError } = useQuery({
    queryKey: ['qaSubmissionDetails', submissionId],
    queryFn: fetchSubmissionDetails,
    enabled: !!submissionId,
  });

  // Handle back navigation
  const handleBack = () => {
    router.back();
  };

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Parse HTML content to plain text for display
  const parseHtmlContent = (htmlContent) => {
    if (!htmlContent) return 'No content available';
    
    // Create a temporary div to parse HTML
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = htmlContent;
    return tempDiv.textContent || tempDiv.innerText || 'No content available';
  };

  if (loading || isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading submission details...</p>
        </div>
      </div>
    );
  }

  if (error || isError) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-600 text-xl mb-4">⚠️</div>
          <p className="text-gray-600 mb-4">{error || queryError?.message || 'Failed to load submission'}</p>
          <button
            onClick={handleBack}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  const { submissionHistory, submission, marking, task } = submissionData || {};

  return (
     <HecQALayout activeTab={activeTab}>
    <div className="min-h-screen bg-gray-50 py-6">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="bg-white rounded-lg shadow mb-6">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <button
                  onClick={handleBack}
                  className="text-gray-600 hover:text-gray-800 flex items-center space-x-2"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                  <span>Back</span>
                </button>
                <h1 className="text-2xl font-semibold text-gray-900">
                  QA Submission Details
                </h1>
              </div>
              <div className="flex items-center space-x-2">
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                  submission?.status === 'reviewed' 
                    ? 'bg-green-100 text-green-800'
                    : submission?.status === 'submitted'
                    ? 'bg-yellow-100 text-yellow-800'
                    : 'bg-gray-100 text-gray-800'
                }`}>
                  {submission?.status || 'Unknown'}
                </span>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Task Information */}
            <div className="bg-white rounded-lg shadow">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-semibold text-gray-900">Task Information</h2>
              </div>
              <div className="px-6 py-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Task Title</label>
                    <p className="text-gray-900">{task?.title || 'N/A'}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Sequence</label>
                    <p className="text-gray-900">#{task?.sequence || 'N/A'}</p>
                  </div>
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                    <p className="text-gray-900">{task?.description || 'N/A'}</p>
                  </div>
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-1">Instructions</label>
                    <p className="text-gray-900">{task?.instructions || 'N/A'}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Word Limit</label>
                    <p className="text-gray-900">
                      {task?.wordLimitMinimum || 0} - {task?.wordLimitMaximum || 0} words
                    </p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Total Score</label>
                    <p className="text-gray-900">{task?.totalScore || 0} points</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Submission Content */}
            <div className="bg-white rounded-lg shadow">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-semibold text-gray-900">Submission Content</h2>
              </div>
              <div className="px-6 py-4">
                <div className="prose max-w-none">
                  <div className="whitespace-pre-wrap text-gray-900 leading-relaxed">
                    {parseHtmlContent(submissionHistory?.content)}
                  </div>
                </div>
              </div>
            </div>

            {/* Marking & Feedback */}
            {marking && (
              <div className="bg-white rounded-lg shadow">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h2 className="text-lg font-semibold text-gray-900">Marking & Feedback</h2>
                </div>
                <div className="px-6 py-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Score</label>
                      <div className="flex items-center space-x-2">
                        <span className="text-2xl font-bold text-blue-600">{marking.score}</span>
                        <span className="text-gray-500">/ {task?.totalScore || 10}</span>
                        <span className="text-sm text-gray-500">
                          ({Math.round((marking.score / (task?.totalScore || 10)) * 100)}%)
                        </span>
                      </div>
                    </div>
                  </div>
                  
                  {marking.submissionFeedback && (
                    <div className="mb-4">
                      <label className="block text-sm font-medium text-gray-700 mb-1">Submission Feedback</label>
                      <p className="text-gray-900 bg-gray-50 p-3 rounded-md">{marking.submissionFeedback}</p>
                    </div>
                  )}
                  
                  {marking.taskRemarks && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Task Remarks</label>
                      <p className="text-gray-900 bg-gray-50 p-3 rounded-md">{marking.taskRemarks}</p>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Submission Details */}
            <div className="bg-white rounded-lg shadow">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-semibold text-gray-900">Submission Details</h2>
              </div>
              <div className="px-6 py-4 space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Submission ID</label>
                  <p className="text-sm text-gray-900 font-mono break-all">{submissionHistory?.id || 'N/A'}</p>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Word Count</label>
                  <p className="text-gray-900">{submissionHistory?.wordCount || 0} words</p>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Submission Date</label>
                  <p className="text-gray-900">{formatDate(submissionHistory?.submissionDate)}</p>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Sequence Number</label>
                  <p className="text-gray-900">#{submissionHistory?.sequenceNumber || 'N/A'}</p>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Current Revision</label>
                  <p className="text-gray-900">{submission?.currentRevision || 1}</p>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Total Revisions</label>
                  <p className="text-gray-900">{submission?.totalRevisions || 1}</p>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Created At</label>
                  <p className="text-gray-900">{formatDate(submission?.createdAt)}</p>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Updated At</label>
                  <p className="text-gray-900">{formatDate(submission?.updatedAt)}</p>
                </div>
              </div>
            </div>

            {/* Quick Stats */}
            <div className="bg-white rounded-lg shadow">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-semibold text-gray-900">Quick Stats</h2>
              </div>
              <div className="px-6 py-4 space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Status</span>
                  <span className={`px-2 py-1 rounded text-xs font-medium ${
                    submission?.status === 'reviewed' 
                      ? 'bg-green-100 text-green-800'
                      : submission?.status === 'submitted'
                      ? 'bg-yellow-100 text-yellow-800'
                      : 'bg-gray-100 text-gray-800'
                  }`}>
                    {submission?.status || 'Unknown'}
                  </span>
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Is Active</span>
                  <span className={`px-2 py-1 rounded text-xs font-medium ${
                    submission?.isActive 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-gray-100 text-gray-800'
                  }`}>
                    {submission?.isActive ? 'Yes' : 'No'}
                  </span>
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">First Revision</span>
                  <span className={`px-2 py-1 rounded text-xs font-medium ${
                    submission?.isFirstRevision 
                      ? 'bg-blue-100 text-blue-800' 
                      : 'bg-gray-100 text-gray-800'
                  }`}>
                    {submission?.isFirstRevision ? 'Yes' : 'No'}
                  </span>
                </div>
                
                {marking && (
                  <div className="flex justify-between items-center pt-2 border-t">
                    <span className="text-sm text-gray-600">Score</span>
                    <span className="text-lg font-bold text-blue-600">
                      {marking.score}/{task?.totalScore || 10}
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    </HecQALayout>
  );
};

export default QASubmissionViewPage;