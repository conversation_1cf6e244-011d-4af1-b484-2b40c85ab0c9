'use client';

import NewTablePage from "@/components/form/NewTablePage";
import DeleteModal from '@/components/form/modal/DeleteModal';
import ViewNovelTopicModal from './view/page'; // Import the new view modal

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import api from '@/lib/api';

const NovelTopicList = () => {
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const [novelTopics, setNovelTopics] = useState([]);
  const [filteredTopics, setFilteredTopics] = useState([]);
  const [totalItems, setTotalItems] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [isError, setIsError] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  
  // Tab states
  const [activeTab, setActiveTab] = useState('all');
  const [availableCategories, setAvailableCategories] = useState([]);
  
  // Delete states
  const [deleteLoading, setDeleteLoading] = useState(false);
  
  // Delete modal states
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedTopic, setSelectedTopic] = useState(null);
  
  // View modal states
  const [showViewModal, setShowViewModal] = useState(false);
  const [selectedViewTopic, setSelectedViewTopic] = useState(null);
  
  const router = useRouter();

  // Fetch novel topics
  const fetchNovelTopics = async () => {
    try {
      setIsLoading(true);
      const endpoint = `/admin/novel/topics?page=${currentPage}&limit=${rowsPerPage}`;
      
      console.log(`Fetching from: ${endpoint}`);
      
      const response = await api.get(endpoint);
      console.log('Raw API response:', response);
      
      // Improved data extraction to handle different response structures
      if (response?.data) {
        let items = [];
        let totalCount = 0;
        let totalPagesCount = 0;
        
        // Check different possible data structures
        if (response.data.items && Array.isArray(response.data.items)) {
          // Direct items array at top level
          items = response.data.items;
          totalCount = response.data.totalCount || response.data.totalItems || 0;
          totalPagesCount = response.data.totalPages || 0;
        } else if (response.data.data) {
          // Items in nested data property
          if (response.data.data.items && Array.isArray(response.data.data.items)) {
            items = response.data.data.items;
            totalCount = response.data.data.totalCount || response.data.data.totalItems || 0;
            totalPagesCount = response.data.data.totalPages || 0;
          } else if (Array.isArray(response.data.data)) {
            // Direct array in data property
            items = response.data.data;
            totalCount = items.length;
            totalPagesCount = 1;
          }
        } else if (Array.isArray(response.data)) {
          // Response data is directly an array
          items = response.data;
          totalCount = items.length;
          totalPagesCount = 1;
        }
        
        if (items.length > 0) {
          const formattedTopics = items.map(item => ({
            id: item.id,
            sequenceTitle: item.sequenceTitle,
            projectTitle: item.title,
            title: item.title,
            category: item.category,
            createdAt: item.createdAt,
            updatedAt: item.updatedAt,
            instruction: item.instruction,
            isActive: item.isActive,
          }));
          
          setNovelTopics(formattedTopics);
          
          // Extract unique categories for tabs
          const categories = [...new Set(formattedTopics.map(topic => topic.category).filter(Boolean))];
          setAvailableCategories(categories);
          
          setTotalItems(totalCount);
          setTotalPages(totalPagesCount);
          setIsError(false);
          setErrorMessage('');
        } else {
          console.log('No items found in response');
          setNovelTopics([]);
          setFilteredTopics([]);
          setAvailableCategories([]);
          setTotalItems(0);
          setTotalPages(0);
          setIsError(false);
          setErrorMessage('');
        }
      } else {
        console.error('Unexpected data structure:', response);
        setIsError(true);
        setErrorMessage('Unexpected data structure received from API');
        setNovelTopics([]);
        setFilteredTopics([]);
      }
    } catch (err) {
      console.error(`Error fetching novel topics:`, err);
      setIsError(true);
      setErrorMessage(err.message || `An error occurred while fetching novel topics`);
      setNovelTopics([]);
      setFilteredTopics([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Filter topics based on active tab
  useEffect(() => {
    if (activeTab === 'all') {
      setFilteredTopics(novelTopics);
      setTotalItems(novelTopics.length);
    } else {
      const filtered = novelTopics.filter(topic => topic.category === activeTab);
      setFilteredTopics(filtered);
      setTotalItems(filtered.length);
    }
    // Reset to first page when changing tabs
    setCurrentPage(1);
  }, [activeTab, novelTopics]);

  // Handle view topic - Show modal with topic details
  const handleViewTopic = async (row) => {
    try {
      console.log('Viewing topic:', row);
      setSelectedViewTopic(row);
      setShowViewModal(true);
    } catch (error) {
      console.error('Error opening view modal:', error);
      toast.error('Failed to open topic details');
    }
  };

  // Handle edit topic - Navigate to edit page
  const handleEditTopic = async (row) => {
    try {
      console.log('Navigating to edit page for topic:', row);
      // Navigate to the edit/view page with the topic ID
      router.push(`/dashboard/module-management/hec-novel/project-list/edit/${row.id}`);
      
      
    } catch (error) {
      console.error('Error navigating to edit page:', error);
      toast.error('Failed to navigate to edit page');
    }
  };

  // Delete novel topic using DeleteModal
  const handleDeleteTopic = async (row) => {
    // Check if a deletion is already in progress
    if (deleteLoading) return;
    
    try {
      setDeleteLoading(true);
      // Set the selected topic data for the modal
      setSelectedTopic(row);
      setShowDeleteModal(true);
    } catch (error) {
      console.error('Error preparing delete modal:', error);
    } finally {
      setDeleteLoading(false);
    }
  };

  useEffect(() => {
    fetchNovelTopics();
  }, [currentPage, rowsPerPage]);

  // Handle tab change
  const handleTabChange = (tabValue) => {
    setActiveTab(tabValue);
  };

  // Define columns for the novel topics table
  const novelTopicColumns = [
    {
      label: 'Sequence Title',
      field: 'sequenceTitle',
    },
    {
      label: 'Project Title',
      field: 'projectTitle',
    },
    // {
    //   label: 'Instruction',
    //   field: 'instruction',
    // },
  ];

  // Define actions - Updated to include view action
  const novelTopicActions = [
    {
      name: 'view',
      icon: 'material-symbols:visibility',
      className: 'text-green-600 hover:text-green-800',
      onClick: handleViewTopic,
      disabled: deleteLoading,
    },
    {
      name: 'edit',
      icon: 'material-symbols:edit',
      className: 'text-blue-600 hover:text-blue-800',
      onClick: handleEditTopic,
      disabled: deleteLoading,
    },
    {
      name: 'delete',
      icon: 'heroicons-outline:trash',
      className: 'text-red-600 hover:text-red-800',
      onClick: handleDeleteTopic,
      disabled: deleteLoading,
    },
  ];

  // Handle page change
  const handleChangePage = (page) => {
    setCurrentPage(page);
  };

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-6">HEC Novel Topic List</h1>
      
      {/* Tab Navigation */}
      <div className="mb-6">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8" aria-label="Tabs">
            {/* Dynamic Category Tabs */}
            {availableCategories.map((category) => {
              return (
                <button
                  key={category}
                  onClick={() => handleTabChange(category)}
                  className={`py-2 px-4 border-b-2 font-medium text-sm transition-all duration-200 hover:bg-yellow-100 ${
                    activeTab === category
                      ? 'border-yellow-400 text-black bg-yellow-50'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  {/* Category Name */}
                  <span className="capitalize">
                    {category.replace(/([A-Z])/g, ' $1').trim()}
                  </span>
                </button>
              );
            })}
          </nav>
        </div>
      </div>

      {/* Content Area - Novel Topics */}
      <div className="bg-white rounded-lg shadow">
        <NewTablePage
          title=""
          createButton={false}
          columns={novelTopicColumns}
          data={filteredTopics}
          actions={novelTopicActions}
          currentPage={currentPage}
          changePage={handleChangePage}
          totalItems={totalItems}
          rowsPerPage={rowsPerPage}
          setRowsPerPage={setRowsPerPage}
          totalPages={Math.ceil(filteredTopics.length / rowsPerPage)}
          loading={isLoading || deleteLoading}
          error={isError}
          errorMessage={errorMessage}
          showCheckboxes={false}
          showSearch={false}
          showNameFilter={false}
          showSortFilter={false}
          showCreateButton={false}
          hideTitle={true}
        />
      </div>

      {/* ViewNovelTopicModal Integration */}
      {showViewModal && (
        <ViewNovelTopicModal
          isOpen={showViewModal}
          onClose={() => setShowViewModal(false)}
          topicData={selectedViewTopic}
        />
      )}

      {/* DeleteModal Integration */}
      {showDeleteModal && (
        <DeleteModal
          isOpen={showDeleteModal}
          onClose={() => setShowDeleteModal(false)}
          data={selectedTopic}
          endPoint={`/admin/novel/topics/${selectedTopic?.id}`}
          onSuccess={fetchNovelTopics}
        />
      )}
    </div>
  );
};

export default NovelTopicList;