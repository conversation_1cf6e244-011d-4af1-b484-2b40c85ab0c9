'use client';
import HeaderCard from '@/components/HeaderCard';
import Image from 'next/image';
import React, { useRef, useState } from 'react';
import { Icon } from '@iconify/react';
import GoBack from '@/components/shared/GoBack';
import { ButtonIcon } from '@/components/Button';
import Link from 'next/link';
import api from '@/lib/api';
import { queryClient } from '@/lib/queryClient';
import useDataFetch from '@/hooks/useDataFetch';
import { useDispatch, useSelector } from 'react-redux';
import { loginSuccess } from '@/store/features/authSlice';

const ProfileHeader = ({ userDetails }) => {
  const [isBioEditing, setIsBioEditing] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef(null);
  const dispatch = useDispatch();
  const { user, token } = useSelector((state) => state.auth);

  const {
    data: awardsInfo,
    isLoading: isAwardLoading,
    error,
  } = useDataFetch({
    queryKey: ['user-award'],
    endPoint: '/awards/my-awards',
  });

  const [userProfile, setUserProfile] = useState(
    userDetails?.profilePictureUrl || '/assets/images/all-img/avatar.png'
  );

  const handleProfileChange = () => {
    fileInputRef.current?.click();
  };

  const handleFileUpload = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    const formData = new FormData();
    formData.append('file', file);

    try {
      setIsUploading(true);
      const response = await api.post('/users/profile/picture', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      // Assume response contains the new image URL
      const imageUrl = response?.data?.profilePictureUrl;

      dispatch(
        loginSuccess({
          user: { ...user, profilePictureUrl: imageUrl },
          token: token,
        })
      );

      if (imageUrl) {
        setUserProfile(imageUrl);
        queryClient.invalidateQueries('Profile-details');
      }
    } catch (error) {
      console.error('Upload failed:', error);
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className="min-h-[60vh] bg-[#FFFDF5]">
      <div className="relative">
        <div>
          <HeaderCard text="" bgColor="#FFFDF5" textColor="#333" textClass="" />
        </div>

        <div className="max-w-7xl mx-auto px-5 xl:px-0 pt-5 absolute left-1/2 -translate-x-1/2 z-10 w-full max-sm:top-0 top-5">
          <div className="flex flex-col md:flex-row gap-8 md:mt-4">
            {/* Left Content */}
            <div className="w-full md:w-3/5 max-sm:space-y-1 space-y-4">
              <GoBack title={'Profile Details'} linkClass={'max-w-52 '} />
              <div className="space-y-2">
                <h2 className="max-sm:text-2xl text-4xl font-semibold text-gray-800">
                  {userDetails?.name}
                </h2>
                <p className="max-sm:text-base text-xl font-medium text-gray-700">
                  HEC Diary
                </p>

                <div className="mt-4 max-w-64">
                  <Link
                    href={'/diary'}
                    className=" flex items-center gap-2 bg-[#FFF189] hover:bg-yellow-300 text-yellow-800 border border-yellow-600 px-7 py-2 rounded-full"
                  >
                    Write Today's Diary
                    <ButtonIcon
                      icon={'majesticons:arrow-right-line'}
                      innerBtnCls={'h-8 w-8'}
                    />
                  </Link>
                </div>
              </div>

              {/* Bio Section */}
              <div className="pt-12">
                <div className="flex items-center justify-between">
                  <p className="font-semibold text-gray-800">Bio</p>
                  {!isBioEditing && (
                    <ButtonIcon
                      onClick={() => setIsBioEditing(true)}
                      icon={'iconamoon:edit-fill'}
                      innerBtnCls={'h-10 w-10'}
                    />
                  )}
                </div>
                <div>
                  {isBioEditing ? (
                    <form
                      onSubmit={async (e) => {
                        e.preventDefault();
                        const bio = e.target.bio.value;
                        try {
                          setIsSubmitting(true);
                          await api.patch('/users/profile', { bio: bio });
                          setIsBioEditing(false);
                          queryClient.invalidateQueries('Profile-details');
                        } catch (error) {
                          console.log(error);
                        } finally {
                          setIsSubmitting(false);
                        }
                      }}
                    >
                      <textarea
                        name="bio"
                        className="w-full min-h-28 p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-yellow-400"
                        placeholder="Write your bio here..."
                        defaultValue={userDetails?.bio}
                      ></textarea>

                      <div className="flex items-center gap-1 justify-end">
                        <button
                          onClick={() => setIsBioEditing(false)}
                          className="text-xs px-3 py-1 rounded bg-gray-300"
                        >
                          Cancel
                        </button>
                        <button
                          type="submit"
                          disabled={isSubmitting}
                          className="text-xs px-3 py-1 rounded bg-yellow-400 disabled:bg-yellow-300 disabled:cursor-not-allowed"
                        >
                          {isSubmitting ? 'Submitting..' : 'Submit'}
                        </button>
                      </div>
                    </form>
                  ) : (
                    <p
                      className={
                        userDetails?.bio ? 'text-gray-700' : 'text-gray-400'
                      }
                    >
                      {userDetails?.bio ? userDetails.bio : 'Bio not available'}
                    </p>
                  )}
                </div>
              </div>
            </div>

            {/* Right Content - Profile Image */}
            <div className="w-full md:w-2/5 flex justify-center relative rounded-lg">
              <div className="w-full flex justify-center rounded-lg">
                <div className="relative">
                  <div className="relative">
                    <Image
                      src={userProfile}
                      alt="Profile"
                      width={500}
                      height={500}
                      className="rounded-lg shadow-md object-cover min-h-[400px] max-h-[500px]"
                    />

                    {/* Upload Overlay */}
                    {isUploading && (
                      <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center rounded-lg z-20 backdrop-blur-sm">
                        <p className="text-white font-semibold text-sm">
                          Uploading...
                        </p>
                      </div>
                    )}

                    <button
                      onClick={handleProfileChange}
                      className="absolute -bottom-4 -right-4 z-30 cursor-pointer"
                    >
                      <ButtonIcon
                        icon={'mdi:camera-outline'}
                        innerBtnCls={'h-12 w-12'}
                      />
                    </button>

                    <input
                      type="file"
                      accept="image/*"
                      ref={fileInputRef}
                      onChange={handleFileUpload}
                      style={{ display: 'none' }}
                    />

                    {/* Awards Section */}
                    <div className="absolute bottom-5 w-[90%] left-1/2 -translate-x-1/2 rounded-lg">
                      <div className="flex bg-white rounded-lg overflow-hidden shadow-sm">
                        <div className="flex-1 py-4 px-6 text-center border-r border-gray-200">
                          <h3 className="text-sm font-medium text-gray-700">
                            Diary Award
                          </h3>
                          <p className="text-2xl font-bold text-yellow-500 mt-1">
                            {awardsInfo?.awards?.length}
                          </p>
                        </div>
                        <div className="flex-1 py-4 px-6 text-center border-r border-gray-200">
                          <h3 className="text-sm font-medium text-gray-700">
                            Essay Award
                          </h3>
                          <p className="text-2xl font-bold text-yellow-500 mt-1">
                            {awardsInfo?.awards?.length}
                          </p>
                        </div>
                        <div className="flex-1 py-4 px-6 text-center">
                          <h3 className="text-sm font-medium text-gray-700">
                            Novel Award
                          </h3>
                          <p className="text-2xl font-bold text-yellow-500 mt-1">
                            {awardsInfo?.awards?.length}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="h-[700px] lg:h-[300px]"></div>
      </div>
    </div>
  );
};

export default ProfileHeader;
