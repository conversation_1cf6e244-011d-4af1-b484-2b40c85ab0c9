import React, { useState, useEffect, useCallback, memo } from 'react';
import { useField } from 'formik';
import { Icon } from '@iconify/react';

// CSS to completely remove number input spinners
const spinnerStyles = `
  /* Chrome, Safari, Edge, Opera */
  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  /* Firefox */
  input[type=number] {
    -moz-appearance: textfield;
  }
`;

// Memoized icon components to prevent re-rendering
const ChevronUpIcon = memo(() => (
  <Icon icon="mdi:chevron-up" className="w-4 h-4 text-gray-600" />
));
ChevronUpIcon.displayName = 'ChevronUpIcon';

const ChevronDownIcon = memo(() => (
  <Icon icon="mdi:chevron-down" className="w-4 h-4 text-gray-600" />
));
ChevronDownIcon.displayName = 'ChevronDownIcon';

const DurationPicker = ({
  name,
  label,
  required = false,
  className = ''
}) => {
  const [field, meta, helpers] = useField(name);

  // Use local state for immediate UI updates to prevent blinking
  const [localTime, setLocalTime] = useState(() => {
    const totalSeconds = field.value || 0;
    return {
      hours: Math.floor(totalSeconds / 3600),
      minutes: Math.floor((totalSeconds % 3600) / 60),
      seconds: totalSeconds % 60
    };
  });

  // Set default value to 0 seconds if no value is provided
  useEffect(() => {
    if (field.value === undefined || field.value === null) {
      helpers.setValue(0);
    }
  }, [field.value, helpers]);

  // Sync local state with field value when it changes externally
  useEffect(() => {
    const totalSeconds = field.value || 0;
    const newTime = {
      hours: Math.floor(totalSeconds / 3600),
      minutes: Math.floor((totalSeconds % 3600) / 60),
      seconds: totalSeconds % 60
    };
    setLocalTime(newTime);
  }, [field.value]);

  // Convert hours, minutes, seconds to total seconds
  const formatDuration = useCallback((hours, minutes, seconds) => {
    return (parseInt(hours) || 0) * 3600 + (parseInt(minutes) || 0) * 60 + (parseInt(seconds) || 0);
  }, []);

  // Update both local state and formik field
  const updateTime = useCallback((newTime) => {
    // Update local state immediately to prevent flickering
    setLocalTime(newTime);
    const totalSeconds = formatDuration(newTime.hours, newTime.minutes, newTime.seconds);
    // Use setTimeout to batch the formik update and prevent flickering
    setTimeout(() => {
      helpers.setValue(totalSeconds);
    }, 0);
  }, [formatDuration, helpers]);

  const handleTimeChange = useCallback((type, value) => {
    const newValue = Math.max(0, parseInt(value) || 0);
    let newTime = { ...localTime };

    switch (type) {
      case 'hours':
        newTime.hours = Math.min(23, newValue);
        break;
      case 'minutes':
        newTime.minutes = Math.min(59, newValue);
        break;
      case 'seconds':
        newTime.seconds = Math.min(59, newValue);
        break;
    }

    updateTime(newTime);
  }, [localTime, updateTime]);

  // Memoized increment/decrement functions for each type to prevent re-renders
  const incrementHours = useCallback(() => {
    setLocalTime(prevTime => {
      const newTime = { ...prevTime, hours: Math.min(23, prevTime.hours + 1) };
      const totalSeconds = formatDuration(newTime.hours, newTime.minutes, newTime.seconds);
      setTimeout(() => helpers.setValue(totalSeconds), 0);
      return newTime;
    });
  }, [formatDuration, helpers]);

  const decrementHours = useCallback(() => {
    setLocalTime(prevTime => {
      const newTime = { ...prevTime, hours: Math.max(0, prevTime.hours - 1) };
      const totalSeconds = formatDuration(newTime.hours, newTime.minutes, newTime.seconds);
      setTimeout(() => helpers.setValue(totalSeconds), 0);
      return newTime;
    });
  }, [formatDuration, helpers]);

  const incrementMinutes = useCallback(() => {
    setLocalTime(prevTime => {
      const newTime = { ...prevTime, minutes: Math.min(59, prevTime.minutes + 1) };
      const totalSeconds = formatDuration(newTime.hours, newTime.minutes, newTime.seconds);
      setTimeout(() => helpers.setValue(totalSeconds), 0);
      return newTime;
    });
  }, [formatDuration, helpers]);

  const decrementMinutes = useCallback(() => {
    setLocalTime(prevTime => {
      const newTime = { ...prevTime, minutes: Math.max(0, prevTime.minutes - 1) };
      const totalSeconds = formatDuration(newTime.hours, newTime.minutes, newTime.seconds);
      setTimeout(() => helpers.setValue(totalSeconds), 0);
      return newTime;
    });
  }, [formatDuration, helpers]);

  const incrementSeconds = useCallback(() => {
    setLocalTime(prevTime => {
      const newTime = { ...prevTime, seconds: Math.min(59, prevTime.seconds + 1) };
      const totalSeconds = formatDuration(newTime.hours, newTime.minutes, newTime.seconds);
      setTimeout(() => helpers.setValue(totalSeconds), 0);
      return newTime;
    });
  }, [formatDuration, helpers]);

  const decrementSeconds = useCallback(() => {
    setLocalTime(prevTime => {
      const newTime = { ...prevTime, seconds: Math.max(0, prevTime.seconds - 1) };
      const totalSeconds = formatDuration(newTime.hours, newTime.minutes, newTime.seconds);
      setTimeout(() => helpers.setValue(totalSeconds), 0);
      return newTime;
    });
  }, [formatDuration, helpers]);
  
  // Memoized TimeInput component to prevent unnecessary re-renders
  const TimeInput = memo(({ type, value, max, onIncrement, onDecrement, onTimeChange }) => (
    <div className="flex flex-col items-center">
      <label className="text-xs font-medium text-gray-600 mb-1 capitalize">
        {type}
      </label>
      <div className="flex flex-col items-center bg-gray-50 rounded-lg border border-gray-200 overflow-hidden">
        <button
          type="button"
          onClick={onIncrement}
          className="p-1 hover:bg-gray-200 transition-colors duration-150 w-full flex items-center justify-center"
        >
          <ChevronUpIcon />
        </button>
        <div className="relative w-12 h-10 flex items-center justify-center">
          <input
            type="text"
            inputMode="numeric"
            pattern="[0-9]*"
            value={value.toString().padStart(2, '0')}
            onChange={(e) => {
              // Only allow numeric input
              const numericValue = e.target.value.replace(/[^0-9]/g, '');
              if (numericValue === '' || (parseInt(numericValue) >= 0 && parseInt(numericValue) <= max)) {
                onTimeChange(type, numericValue);
              }
            }}
            className="w-full h-8 text-center text-lg font-mono bg-transparent border-0 focus:outline-none focus:ring-0 absolute inset-0 select-all"
            style={{
              appearance: 'none',
              MozAppearance: 'textfield',
              WebkitAppearance: 'none',
              caretColor: 'transparent'
            }}
            onFocus={(e) => e.target.select()}
            autoComplete="off"
          />
        </div>
        <button
          type="button"
          onClick={onDecrement}
          className="p-1 hover:bg-gray-200 transition-colors duration-150 w-full flex items-center justify-center"
        >
          <ChevronDownIcon />
        </button>
      </div>
    </div>
  ));
  TimeInput.displayName = 'TimeInput';
  
  return (
    <div className={`space-y-2 ${className}`}>
      {/* Inject CSS to remove number input spinners */}
      <style dangerouslySetInnerHTML={{ __html: spinnerStyles }} />

      {label && (
        <label className="block text-sm font-medium text-gray-700">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      
      <div className="flex items-center justify-center gap-4 p-4 bg-white border border-gray-300 rounded-lg h-40">
        <TimeInput
          type="hours"
          value={localTime.hours}
          max={23}
          onIncrement={incrementHours}
          onDecrement={decrementHours}
          onTimeChange={handleTimeChange}
        />

        <div className="flex flex-col items-center justify-center h-16">
          <span className="text-2xl font-bold text-gray-400">:</span>
        </div>

        <TimeInput
          type="minutes"
          value={localTime.minutes}
          max={59}
          onIncrement={incrementMinutes}
          onDecrement={decrementMinutes}
          onTimeChange={handleTimeChange}
        />

        <div className="flex flex-col items-center justify-center h-16">
          <span className="text-2xl font-bold text-gray-400">:</span>
        </div>

        <TimeInput
          type="seconds"
          value={localTime.seconds}
          max={59}
          onIncrement={incrementSeconds}
          onDecrement={decrementSeconds}
          onTimeChange={handleTimeChange}
        />
      </div>
      
      {/* Display total duration */}
      <div className="text-center">
        <span className="text-sm text-gray-500">
          Total: {field.value || 0} seconds
          {field.value > 0 && (
            <span className="ml-2 text-gray-400">
              ({Math.floor(field.value / 60)}m {field.value % 60}s)
            </span>
          )}
        </span>
      </div>
      
      {meta.touched && meta.error && (
        <div className="text-red-500 text-sm mt-1">{meta.error}</div>
      )}
    </div>
  );
};

export default DurationPicker;
