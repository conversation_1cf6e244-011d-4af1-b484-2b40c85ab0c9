import useDataFetch from '@/hooks/useDataFetch';
import api from '@/lib/api';
import { Icon } from '@iconify/react';
import { useQueryClient } from '@tanstack/react-query';
import Image from 'next/image';
import React from 'react';

const RequestList = () => {
    const queryClient = useQueryClient();
  const {
    data: requests,
    isLoading: isLoadingRequests,
    refetch: refetchRequests,
  } = useDataFetch({
    queryKey: ['friend-requests'],
    endPoint: '/student/friends/diary-follow/pending',
  });
  const friendRequests = requests || [];

  // Handle responding to friend request
  const handleRespondToRequest = async (requestId, action) => {
    try {
      await api.post(`/student/friends/diary-follow/${requestId}/respond`, {
        status: action,
      });

      // Refresh friend requests and friends list
      refetchRequests();
      if (action === 'accepted') {
        queryClient.invalidateQueries(['my-friends']);
      }
    } catch (error) {
      console.error(`Error ${action}ing friend request:`, error);
    }
  };

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">Follow Requests</h2>

      {isLoadingRequests ? (
        <div className="flex justify-center items-center py-20">
          <Icon
            icon="eos-icons:loading"
            width="48"
            height="48"
            className="animate-spin text-yellow-500"
          />
        </div>
      ) : friendRequests.length === 0 ? (
        <div className="flex flex-col items-center justify-center py-20">
          <Icon
            icon="mdi:email-outline"
            width="64"
            height="64"
            className="text-gray-400 mb-4"
          />
          <h3 className="text-xl font-medium text-gray-600">
            No pending follower requests
          </h3>
          <p className="text-gray-500">
            When someone sends you a follower request, it will appear here
          </p>
        </div>
      ) : (
        <div className="space-y-4 max-h-[600px] overflow-y-auto">
          {friendRequests?.map((request) => (
            <div
              key={request.id}
              className="flex items-center justify-between p-3 rounded-lg shadow-lg border-gray-200 border"
            >
              <div className="flex items-center">
                <div className="w-12 h-12 rounded-full overflow-hidden mr-4">
                  <Image
                    src={
                      request.sender?.profilePicture ||
                      '/assets/images/all-img/avatar.png'
                    }
                    alt={request.sender?.name}
                    width={100}
                    height={100}
                    className="w-full h-full object-cover"
                  />
                </div>
                <div>
                  <p className="font-medium">{request?.requesterName}</p>
                  <p className="text-sm text-gray-500">
                    {request.sender?.email}
                  </p>
                </div>
              </div>
              <div className="flex space-x-2">
                <button
                  className="bg-green-100 text-green-700 px-3 py-1 rounded-lg text-sm border border-green-500 hover:bg-green-200"
                  onClick={() => handleRespondToRequest(request.id, 'accepted')}
                >
                  Accept
                </button>
                <button
                  className="bg-red-100 text-red-700 px-3 py-1 rounded-lg text-sm border border-red-500 hover:bg-red-200"
                  onClick={() => handleRespondToRequest(request.id, 'rejected')}
                >
                  Reject
                </button>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default RequestList;
