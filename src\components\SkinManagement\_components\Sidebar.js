'use client';
import { useState, useEffect } from 'react';
import Image from 'next/image';
import { useDispatch, useSelector } from 'react-redux';
import {
  backgroundColors,
  selectCanvasBackground,
  selectSelectedItem,
  addImageToCanvas,
  changeBackground
} from '@/store/features/canvasSlice';
import PropertyEditor from './PropertyEditor';
import api from '@/lib/api';

const Sidebar = ({isAdmin}) => {
  const dispatch = useDispatch();
  const canvasBackground = useSelector(selectCanvasBackground);
  const selectedItem = useSelector(selectSelectedItem);

  // State for categories and images
  const [categories, setCategories] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [images, setImages] = useState([]);
// console.log(images, 'ff')
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Fetch categories on component mount
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const url = isAdmin ? '/admin/shop/categories' : '/shop/categories';
        const response = await api.get(url);
        if (response.success && response.data && response.data.items) {
          setCategories([{ id: 'all', name: 'All' }, ...response.data.items]);
        }
      } catch (err) {
        console.error('Error fetching categories:', err);
        // Create a default 'All' category if API fails
        setCategories([{ id: 'all', name: 'All' }]);
        setError('Failed to load categories');
      }
    };

    fetchCategories();
  }, []);

  // Fetch images based on selected category
  useEffect(() => {
    const fetchImages = async () => {
      setLoading(true);
      setError(null);

      try {
        let url = isAdmin ? '/admin/shop/items' : '/student/owned-items';
        if (selectedCategory && selectedCategory !== 'all') {
          url += `?categoryId=${selectedCategory}`;
        }

        const response = await api.get(url);
        if (response.success && response.data && response.data.items) {
          setImages(response.data.items);
        }
      } catch (err) {
        console.error('Error fetching images:', err);
        setError('Failed to load images');
        setImages([]);
      } finally {
        setLoading(false);
      }
    };

    fetchImages();
  }, [selectedCategory]);

  const handleCategorySelect = (categoryId) => {
    setSelectedCategory(categoryId);
    setError(null);
  };

  const handleAddImage = (imageSrc) => {
    // Just add image to canvas without selecting it
    dispatch(addImageToCanvas(imageSrc));
  };

  const handleChangeBackground = (color) => {
    dispatch(changeBackground(color));
  };

  // We can add a clear selection button if needed in the future
  // const handleClearSelection = () => {
  //   dispatch(clearSelection());
  // };
  return (
    <div className="h-full p-4 overflow-hidden">
      <div className="h-full overflow-y-auto custom-scrollbar pr-1">
      {/* Show images section only when no text element is selected */}
      {(!selectedItem || selectedItem.type === 'image') && (
        <div className="mb-6 p-3 bg-white rounded-md border border-[#E6D7A9] shadow-sm">
          <h3 className="text-md font-medium mb-3 text-[#723F11] border-b pb-2 border-[#E6D7A9]">Images</h3>

          {/* Categories horizontal scrollable list */}
          <div className="mb-4 overflow-x-auto">
            <div className="flex space-x-2 pb-2">
              {categories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => handleCategorySelect(category.id)}
                  className={`px-3 py-1.5 text-sm whitespace-nowrap rounded-full transition-colors ${selectedCategory === category.id ? 'bg-yellow-500 text-white' : 'bg-gray-100 hover:bg-gray-200 text-gray-700'}`}
                >
                  {category.name}
                </button>
              ))}
            </div>
          </div>

          {/* Loading state */}
          {loading && (
            <div className="flex justify-center items-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-yellow-500"></div>
            </div>
          )}

          {/* Error state with fallback notice */}
          {error && (
            <div className="bg-yellow-50 text-yellow-700 p-3 rounded-md mb-3 text-center text-sm">
              {error}
            </div>
          )}

          {/* Images grid with max height and scroll */}
          {!loading && !error && (
            <div className="max-h-[300px] overflow-y-auto pr-1 custom-scrollbar">
              <div className="grid grid-cols-2 gap-2">
                {images.length > 0 ? (
                  images.filter(item => item.filePath).map((item, index) => (
                    <div
                      key={item.id || index}
                      className="cursor-pointer hover:opacity-80 transition-opacity border border-gray-100 rounded overflow-hidden"
                      draggable
                      onClick={() => handleAddImage(item.filePath)}
                      onDragStart={(e) => {
                        e.dataTransfer.setData('text/plain', JSON.stringify({
                          type: 'image',
                          src: item.filePath,
                          title: item.title || `Image ${index + 1}`
                        }));
                      }}
                    >
                      <div className="relative h-24 w-full">
                        {item.filePath ? (
                          <Image
                            src={item.filePath}
                            alt={item.title || `Image ${index + 1}`}
                            fill
                            sizes="(max-width: 100px) 100vw"
                            style={{ objectFit: 'cover' }}
                          />
                        ) : (
                          <div className="w-full h-full flex items-center justify-center bg-gray-100 text-gray-400 text-xs">
                            No image
                          </div>
                        )}
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="col-span-2 py-6 text-center text-gray-500">
                    No images found in this category
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Background Color Selector - Always visible */}
      <div className="mb-6 p-3 bg-white rounded-md border border-[#E6D7A9] shadow-sm">
        <h3 className="text-md font-medium mb-3 text-[#723F11] border-b pb-2 border-[#E6D7A9]">Canvas Background</h3>

        <div className="grid grid-cols-6 gap-2 mb-3">
          {backgroundColors.slice(0, 18).map((color, index) => (
            <button
              key={index}
              onClick={() => handleChangeBackground(color.value)}
              className={`p-0 rounded-sm border ${canvasBackground === color.value ? 'border-yellow-500 ring-1 ring-yellow-400' : 'border-gray-300'}`}
              title={color.name}
            >
              <div
                className="w-full h-5 rounded-sm"
                style={{ backgroundColor: color.value }}
              ></div>
            </button>
          ))}

          {/* Custom color picker button with input type color */}
          <div className="p-0 rounded-sm border border-gray-300 overflow-hidden relative">
            <input
              type="color"
              onChange={(e) => handleChangeBackground(e.target.value)}
              className="absolute inset-0 opacity-0 cursor-pointer w-full h-full"
              title="Choose Custom Background Color"
            />
            <div className="w-full h-5 bg-white flex items-center justify-center text-xs font-medium">
              <span className="text-gray-600">+</span>
            </div>
          </div>
        </div>
      </div>

      {/* Property editor when an item is selected */}
      {selectedItem && (
        <PropertyEditor
          selectedItem={selectedItem}
        />
      )}
      </div>
    </div>
  );
};

export default Sidebar;
