import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Icon } from '@iconify/react';
import WordBlock from './WordBlock';
import DropZone from './DropZone';

const SentenceBlock = ({
  show = true,
  hide = false,
  type = 'start', // 'start' or 'expand'
  currentStep,
  gameData,
  startingSentences,
  expandingSentences,
  usedWords,
  handleDragStart,
  handleDrop,
  removeWord,
  onNext,
  onSubmit,
  allComplete = false,
  title,
  showCompletedSentences = false,
  allSentencesBuilt = false,
  onStartExpansion,
  onSentenceComplete,
}) => {
  const [selectedWord, setSelectedWord] = useState(null);
  const [isMobile, setIsMobile] = useState(false);

  // Detect mobile device
  useEffect(() => {
    const checkMobile = () => {
      if (window.innerWidth <= 768 || 'ontouchstart' in window) {
        setIsMobile(true);
      } else {
        setIsMobile(false);
      }
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Handle mobile word selection
  const handleWordClick = (word) => {
    if (!isMobile) return;

    if (selectedWord === word) {
      setSelectedWord(null);
    } else {
      setSelectedWord(word);
      if (handleDragStart) {
        handleDragStart(word, dragType);
      }
    }
  };

  // Handle mobile drop zone click
  const handleDropZoneClick = (dropIndex, dropBlockIndex) => {
    if (!isMobile || !selectedWord) return;

    if (handleDrop) {
      handleDrop(dragType, dropIndex, dropBlockIndex);
      setSelectedWord(null);
    }
  };
  if (hide || !show || !gameData) return null;

  const isStartingType = type === 'start';
  const words = isStartingType
    ? gameData.word_blocks.starting_words
    : gameData.word_blocks.expanding_words;

  const sentences = isStartingType ? startingSentences : expandingSentences;
  const dragType = isStartingType ? 'start' : 'expand';

  const getTitle = () => {
    if (title) return title;
    return isStartingType
      ? `Starting Sentence Block set #${currentStep + 1}`
      : 'Expanding Sentence Block Set';
  };

  // const renderProgressIndicator = () => {
  //   const totalSentences = gameData.sentences.length;

  //   if (isStartingType) {
  //     const completedSentences = startingSentences.filter(s => s.every(w => w !== null)).length;
  //     const progressPercentage = totalSentences > 0 ? (completedSentences / totalSentences) * 100 : 0;

  //     return (
  //       <motion.div
  //         initial={{ opacity: 0, y: -10 }}
  //         animate={{ opacity: 1, y: 0 }}
  //         transition={{ duration: 0.5 }}
  //         className="mb-6"
  //       >
  //         <div className="bg-white rounded-2xl p-4 shadow-md border-2 border-orange-200">
  //           <div className="flex items-center justify-between mb-3">
  //             <h3 className="text-lg font-bold text-[#8B4513] flex items-center gap-2">
  //               <Icon icon="mdi:progress-check" className="w-5 h-5" />
  //               Sentence Building Progress
  //             </h3>
  //             <span className="text-sm font-medium text-[#5A3D1A]">
  //               {completedSentences}/{totalSentences} Complete
  //             </span>
  //           </div>

  //           <div className="w-full bg-gray-200 rounded-full h-3 mb-2">
  //             <motion.div
  //               className="bg-gradient-to-r from-orange-400 to-orange-500 h-full rounded-full transition-all duration-500"
  //               initial={{ width: 0 }}
  //               animate={{ width: `${progressPercentage}%` }}
  //               transition={{ duration: 0.8, delay: 0.2 }}
  //             />
  //           </div>

  //           <div className="flex justify-between text-xs text-[#5A3D1A]">
  //             <span>Current: Sentence {currentStep + 1}</span>
  //             <span>{Math.round(progressPercentage)}% Complete</span>
  //           </div>
  //         </div>
  //       </motion.div>
  //     );
  //   } else {
  //     // Word expansion progress
  //     const completedExpansions = expandingSentences.filter(s => s.every(w => w !== null)).length;
  //     const progressPercentage = (completedExpansions / totalSentences) * 100;

  //     return (
  //       <motion.div
  //         initial={{ opacity: 0, y: -10 }}
  //         animate={{ opacity: 1, y: 0 }}
  //         transition={{ duration: 0.5 }}
  //         className="mb-6"
  //       >
  //         <div className="bg-white rounded-2xl p-4 shadow-md border-2 border-green-200">
  //           <div className="flex items-center justify-between mb-3">
  //             <h3 className="text-lg font-bold text-[#8B4513] flex items-center gap-2">
  //               <Icon icon="mdi:text-box-plus" className="w-5 h-5" />
  //               Word Expansion Progress
  //             </h3>
  //             <span className="text-sm font-medium text-[#5A3D1A]">
  //               {completedExpansions}/{totalSentences} Expanded
  //             </span>
  //           </div>

  //           <div className="w-full bg-gray-200 rounded-full h-3 mb-2">
  //             <motion.div
  //               className="bg-gradient-to-r from-green-400 to-green-500 h-full rounded-full transition-all duration-500"
  //               initial={{ width: 0 }}
  //               animate={{ width: `${progressPercentage}%` }}
  //               transition={{ duration: 0.8, delay: 0.2 }}
  //             />
  //           </div>

  //           <div className="flex justify-between text-xs text-[#5A3D1A]">
  //             <span>Expanding all sentences</span>
  //             <span>{Math.round(progressPercentage)}% Complete</span>
  //           </div>
  //         </div>
  //       </motion.div>
  //     );
  //   }
  // };

  const renderWordBlocks = () => (
    <WordBlock
      words={words}
      usedWords={usedWords}
      title={getTitle()}
      hide={false}
      disable={false}
      disableDrag={false}
      disableClick={!isMobile}
      dragType={dragType}
      onDragStart={handleDragStart}
      onClick={handleWordClick}
      selectedWord={selectedWord}
      showImage={true}
      imageSrc="/assets/images/all-img/footer_butterfly.png"
      imageWidth={50}
      imageHeight={50}
      imageAlt="Icon"
    />
  );

  const renderDropZones = () => {
    if (isStartingType) {
      return (
        <div className="bg-white rounded-2xl sm:rounded-[32px] border-2 sm:border-4 border-yellow-300 p-3 sm:p-4 flex flex-wrap gap-2 sm:gap-3">
          {sentences[currentStep].map((word, i) => (
            <DropZone
              key={i}
              word={word}
              index={i}
              blockIndex={currentStep}
              hide={false}
              disable={false}
              disableDrop={false}
              disableRemove={false}
              dragType={dragType}
              onDrop={handleDrop}
              onRemoveWord={removeWord}
              onClick={() => handleDropZoneClick(i, currentStep)}
              isMobile={isMobile}
              selectedWord={selectedWord}
            />
          ))}
        </div>
      );
    } else {
      // Expanding sentences - show all sentences
      return gameData.sentences.map((_, blockIndex) => (
        <div
          key={blockIndex}
          className="w-full flex flex-col sm:flex-row gap-2 sm:gap-4 items-start sm:items-center bg-white rounded-xl p-3 sm:p-4 border border-gray-200"
        >
          <p className="text-sm sm:text-base font-medium text-gray-700 min-w-0 flex-shrink">
            <span className="font-bold text-orange-600">{blockIndex + 1}.</span>{' '}
            {startingSentences[blockIndex].join(' ')}
          </p>
          <div className="flex flex-wrap gap-2 sm:gap-3 flex-1">
            {sentences[blockIndex].map((word, i) => (
              <DropZone
                key={i}
                word={word}
                index={i}
                blockIndex={blockIndex}
                hide={false}
                disable={false}
                disableDrop={false}
                disableRemove={false}
                dragType={dragType}
                onDrop={handleDrop}
                onRemoveWord={removeWord}
                onClick={() => handleDropZoneClick(i, blockIndex)}
                isMobile={isMobile}
                selectedWord={selectedWord}
              />
            ))}
          </div>
        </div>
      ));
    }
  };

  const renderCompletedStartingSentences = () => {
    if (!isStartingType) return null;

    const completedSentences = [];
    for (let i = 0; i < currentStep; i++) {
      if (
        startingSentences[i] &&
        startingSentences[i].every((w) => w !== null)
      ) {
        completedSentences.push({
          index: i,
          sentence: startingSentences[i].join(' '),
        });
      }
    }

    if (completedSentences.length === 0) return null;

    return (
      <div className="w-full space-y-3 sm:space-y-4">
        <h3 className="text-base sm:text-lg font-bold text-green-700">
          Completed Starting Sentences
        </h3>
        {completedSentences.map(({ index, sentence }) => (
          <div
            key={index}
            className="bg-white rounded-xl sm:rounded-[32px] border-2 sm:border-4 border-green-300 p-3 sm:p-4"
          >
            <p className="text-xs sm:text-sm font-medium text-green-700">
              <span className="font-bold">{index + 1}.</span> {sentence}
            </p>
          </div>
        ))}
      </div>
    );
  };

  const renderCompletedSentences = () => {
    // Only show completed sentences when in expanding mode and all sentences are complete
    if (isStartingType || !allComplete) return null;

    return (
      <div className="w-full bg-white border-2 sm:border-4 border-green-300 rounded-xl sm:rounded-[32px] px-4 sm:px-6 py-3 sm:py-4">
        <h3 className="text-base sm:text-lg font-bold mb-3 sm:mb-4 text-green-700">
          All Completed Sentences
        </h3>
        <div className="space-y-3 sm:space-y-4">
          {startingSentences.map((s, i) => {
            const start = s.join(' ');
            const expand = expandingSentences[i].join(' ');
            return (
              <div
                key={i}
                className="flex flex-wrap gap-1 sm:gap-2 items-center p-2 sm:p-3 bg-gray-50 rounded-lg"
              >
                <span className="font-bold text-orange-600 text-xs sm:text-sm">
                  {i + 1}.
                </span>
                <span className="text-xs sm:text-sm text-[#5A3D1A] font-medium">
                  {start}
                </span>
                {expand.split(' ').map((w, idx) => (
                  <span
                    key={idx}
                    className="text-xs sm:text-sm text-[#EA7D00] font-medium bg-orange-100 px-1 sm:px-2 py-0.5 sm:py-1 rounded"
                  >
                    {w}
                  </span>
                ))}
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  const currentSentenceComplete = sentences[currentStep].every(
    (w) => w !== null
  );
  const isLastSentence = currentStep + 1 >= gameData.sentences.length;


  const renderActionButton = () => {
    if (isStartingType) {
      {
        console.log(currentSentenceComplete);
      }
      if (currentSentenceComplete) {
        if (isLastSentence) {
          // All sentences built - show transition to expansion button
          return (
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5 }}
              className="text-center"
            >
              <motion.div transition={{ duration: 2, repeat: Infinity }}>
                <motion.button
                  whileTap={{ scale: 0.95 }}
                  onClick={onNext}
                  className="bg-gradient-to-b from-yellow-500 to-yellow-400 hover:from-yellow-600 hover:to-yellow-600 text-white rounded-full font-bold shadow-lg transition-all duration-300 flex items-center gap-2 px-6 py-4 text-lg"
                >
                  <Icon icon="mdi:arrow-right-bold" className="w-5 h-5" />
                  <span>Start Expanding Words</span>
                  <motion.div
                    animate={{ x: [0, 5, 0] }}
                    transition={{ duration: 1.5, repeat: Infinity }}
                  >
                    <Icon icon="mdi:chevron-double-right" className="w-5 h-5" />
                  </motion.div>
                </motion.button>
              </motion.div>
            </motion.div>
          );
        } else {
          // More sentences to build
          return (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="text-center "
            >
              {/* Celebration effect */}
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: [0, 1.2, 1] }}
                transition={{ duration: 0.6 }}
                className="mb-2"
              >
                {/* <div className="flex justify-center gap-2">
                  {[...Array(3)].map((_, i) => (
                    <motion.div
                      key={i}
                      animate={{
                        y: [0, -20, 0],
                        rotate: [0, 360, 0]
                      }}
                      transition={{
                        duration: 1.5,
                        repeat: Infinity,
                        delay: i * 0.2
                      }}
                    >
                      <Icon icon="mdi:star" className="w-6 h-6 text-yellow-400" />
                    </motion.div>
                  ))}
                </div> */}
              </motion.div>
            </motion.div>
          );
        }
      }
    } else if (!isStartingType && allComplete && onSubmit) {
      return (
        <motion.button
          // whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={onSubmit}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700 text-white rounded-full font-bold shadow-xl transition-all duration-300 flex items-center gap-2 px-4 py-3 sm:px-6 sm:py-4"
        >
          <Icon icon="mdi:check-circle" className="w-4 h-4 sm:w-5 sm:h-5" />
          <span className="hidden sm:inline">Complete Game</span>
          <span className="sm:hidden">Submit</span>
          <motion.div
            animate={{ scale: [1, 1.2, 1] }}
            transition={{ duration: 1.5, repeat: Infinity }}
          >
            <Icon icon="mdi:trophy" className="w-4 h-4 sm:w-5 sm:h-5" />
          </motion.div>
        </motion.button>
      );
    }
    return null;
  };

  return (
    <div className="relative w-full space-y-4 sm:space-y-6">
      {/* Progress indicator - only show outside modal */}
      {/* <div className="block lg:hidden">
        {renderProgressIndicator()}
      </div> */}

      {/* Stage transition banner */}
      {/* {!isStartingType && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 rounded-lg p-4 text-center mb-4"
        >
          <div className="flex items-center justify-center gap-2 mb-2">
            <Icon icon="mdi:check-circle" className="w-5 h-5 text-green-600" />
            <h3 className="font-bold text-green-700">
              Sentence Building Complete!
            </h3>
          </div>
          <p className="text-sm text-gray-600">
            Now expand each sentence by adding more words from the word blocks
            below.
          </p>
        </motion.div>
      )} */}

      {/* Mobile instruction banner */}
      {isMobile && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 text-center">
          <p className="text-sm text-blue-700">
            {selectedWord ? (
              <>
                <span className="font-semibold">"{selectedWord}"</span>{' '}
                selected. Tap a drop zone to place it.
              </>
            ) : (
              'Tap a word to select it, then tap a drop zone to place it.'
            )}
          </p>
        </div>
      )}

      {/* Main content area */}
      <div className="space-y-4 sm:space-y-6 pb-20">
        {renderWordBlocks()}
        {renderDropZones()}
        {renderCompletedStartingSentences()}
        {renderCompletedSentences()}
      </div>

      {/* Fixed bottom-right action button - positioned relative to modal */}
      <div className="absolute bottom-0 right-6 z-10">
        {/* {renderActionButton()} */}
        {(isLastSentence || isStartingType) && (
          <motion.button
            whileTap={{ scale: 0.95 }}
            onClick={() => {
              if (onSentenceComplete) {
                onSentenceComplete(currentStep + 1);
              }
              onNext();
            }}
            disabled={!currentSentenceComplete}
            className="bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700 text-white rounded-full font-bold shadow-lg transition-all duration-300 flex items-center gap-2 px-4 py-3 sm:px-6 sm:py-3 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <span className="">Next</span>
            <Icon icon="mage:arrow-right" className="w-4 h-4 sm:w-5 sm:h-5" />
          </motion.button>
        )}

        {!isStartingType && allComplete && onSubmit && (
          <motion.button
            // whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={onSubmit}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700 text-white rounded-full font-bold shadow-xl transition-all duration-300 flex items-center gap-2 px-4 py-3 sm:px-6 sm:py-4"
          >
            <Icon icon="mdi:check-circle" className="w-4 h-4 sm:w-5 sm:h-5" />
            <span className="hidden sm:inline">Complete Game</span>
            <span className="sm:hidden">Submit</span>
            <motion.div
              animate={{ scale: [1, 1.2, 1] }}
              transition={{ duration: 1.5, repeat: Infinity }}
            >
              <Icon icon="mdi:trophy" className="w-4 h-4 sm:w-5 sm:h-5" />
            </motion.div>
          </motion.button>
        )}
      </div>
    </div>
  );
};

export default SentenceBlock;
