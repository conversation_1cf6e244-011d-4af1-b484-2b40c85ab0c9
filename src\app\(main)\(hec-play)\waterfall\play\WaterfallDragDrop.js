'use client';
import React, { useState, useEffect } from 'react';
import {
  DndContext,
  rectIntersection,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragOverlay,
} from '@dnd-kit/core';
import { restrictToWindowEdges } from '@dnd-kit/modifiers';
import { CSS } from '@dnd-kit/utilities';
import { useDraggable, useDroppable } from '@dnd-kit/core';
import { motion } from 'framer-motion';
import { Icon } from '@iconify/react';

// Draggable option component with one-time falling animation and grid-based positioning
export const DraggableOption = ({ id, option, isActive, index, totalOptions }) => {
  // Calculate grid-based positioning to avoid overlaps
  const calculatePosition = () => {
    // Create a grid layout based on the total number of options
    const columns = Math.ceil(Math.sqrt(totalOptions));
    const rows = Math.ceil(totalOptions / columns);

    // Calculate which row and column this option should be in
    const row = Math.floor(index / columns);
    const col = index % columns;

    // Add some randomness within the cell to make it look more natural
    const cellWidth = 100 / columns;
    const cellHeight = 250 / rows;

    const xOffset = (Math.random() * 0.5 - 0.25) * cellWidth; // Small random offset within cell
    const yOffset = (Math.random() * 0.5 - 0.25) * cellHeight;

    // Calculate final position
    const x = col * cellWidth + cellWidth * 0.5 + xOffset; // Center in cell + offset
    const y = row * cellHeight + 50 + yOffset; // Start from 50px down + offset

    return { x, y };
  };

  // Use useRef to store stable values that won't change on re-renders
  const stableValues = React.useRef({
    position: calculatePosition(),
    delay: index * 0.15, // Stagger the start times
    duration: 3 + Math.random() * 2, // Between 3-5 seconds to fall
    rotation: Math.random() * 10 - 5, // Small random rotation (-5 to 5 degrees)
  });

  const { attributes, listeners, setNodeRef, transform, isDragging } =
    useDraggable({
      id: id,
      data: { option },
    });

  // Apply the transform from dnd-kit when dragging
  const style = transform
    ? {
        transform: CSS.Translate.toString(transform),
        touchAction: 'none',
        zIndex: 10, // Ensure dragged item is above others
      }
    : {
        touchAction: 'none',
      };

  // If the item is being dragged, don't render the falling animation
  if (isDragging || isActive) {
    return (
      <div
        ref={setNodeRef}
        style={style}
        {...listeners}
        {...attributes}
        className="bg-yellow-100 border-2 border-yellow-300 rounded-md px-4 py-2 cursor-grab shadow-lg ring-2 ring-yellow-400 absolute opacity-0"
      >
        {option}
      </div>
    );
  }

  // Bubble-like continuous falling animation
  const [fallKey, setFallKey] = React.useState(0);

  return (
    <motion.div
      key={fallKey}
      initial={{
        y: -50,
        opacity: 1,
        rotate: stableValues.current.rotation * 2,
      }}
      animate={{
        y: stableValues.current.position.y + 180, // fall to bottom (adjust as needed)
        opacity: 1,
        rotate: stableValues.current.rotation,
      }}
      transition={{
        type: 'linear',
        duration: stableValues.current.duration,
        delay: stableValues.current.delay,
        repeat: Infinity,
        repeatType: 'loop',
        repeatDelay: 0,
      }}
      onUpdate={(latest) => {
        // If the option is being dragged, stop the animation
        // (framer-motion will handle this by unmounting this div)
      }}
      onAnimationComplete={() => {
        // Reset the animation by changing the key
        setFallKey((k) => k + 1);
      }}
      className="absolute"
      style={{
        left: `${stableValues.current.position.x}%`,
        position: 'absolute',
        pointerEvents: 'auto',
      }}
    >
      <div
        ref={setNodeRef}
        style={style}
        {...listeners}
        {...attributes}
        className="bg-yellow-100 border-2 border-yellow-300 rounded-md px-4 py-2 cursor-grab shadow-sm hover:shadow-md transition-shadow"
      >
        {option}
      </div>
    </motion.div>
  );
};

// Droppable blank component
const DroppableBlank = ({ id, value, index, onReset }) => {
  const { isOver, setNodeRef } = useDroppable({
    id: id,
    data: { index },
  });

  // console.log(isOver)

  return (
    <div className="inline-flex items-center relative">
      <div
        ref={setNodeRef}
        className={`inline-flex items-center justify-center min-w-20 h-8 mx-1 border ${
          isOver
            ? 'border-yellow-500 bg-yellow-200'
            : value
            ? 'border-green-500 bg-green-50'
            : 'border-yellow-600  bg-yellow-100'
        } rounded px-2`}
      >
        {value || ''}
      </div>

      {/* Reset button for individual blank */}
      {value && (
        <button
          onClick={() => onReset(index)}
          className="absolute -right-2 -top-2 bg-white rounded-full w-5 h-5 flex items-center justify-center text-gray-500 hover:text-red-500 shadow-sm border border-gray-200"
          title="Reset this answer"
        >
          <Icon icon="mdi:close" width={12} />
        </button>
      )}
    </div>
  );
};

// Drag overlay component
const DragOverlayContent = ({ option }) => {
  return (
    <div className="bg-yellow-200 border-2 border-yellow-400 rounded-md px-4 py-2 shadow-lg">
      {option}
    </div>
  );
};

const WaterfallDragDrop = ({ question, title, onCorrect, onNext, isLastQuestion, handleSubmit }) => {
  const [blanks, setBlanks] = useState([]);
  const [options, setOptions] = useState([]);
  const [activeId, setActiveId] = useState(null);
  const [activeOption, setActiveOption] = useState(null);
  const [isComplete, setIsComplete] = useState(false);
  const [isCorrect, setIsCorrect] = useState(false);
  const [hasAwardedPoints, setHasAwardedPoints] = useState(false);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor)
  );

  // Initialize blanks and options
  useEffect(() => {
    if (question) {
      // Count the number of blanks in the question
      const blankCount = (
        question.question_text_plain.match(/\[\[gap\]\]/g) || []
      ).length;

      // Initialize blanks array with empty values
      setBlanks(Array(blankCount).fill(null));

      // Initialize options array with the provided options
      setOptions(
        question.options.map((option, index) => ({
          id: `option-${index}`,
          text: option,
          used: false,
        }))
      );
    }
  }, [question]);

  // Handle drag start
  const handleDragStart = (event) => {
    const { active } = event;
    setActiveId(active.id);

    // If dragging from options
    if (active.id.startsWith('option-')) {
      const optionIndex = parseInt(active.id.split('-')[1]);
      setActiveOption(options[optionIndex].text);
    }
    // If dragging from blanks (allowing rearrangement)
    else if (active.id.startsWith('blank-')) {
      const blankIndex = parseInt(active.id.split('-')[1]);
      if (blanks[blankIndex]) {
        setActiveOption(blanks[blankIndex]);
      }
    }
  };

  // Handle drag end
  const handleDragEnd = (event) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      // If dropping onto a blank
      if (over.id.startsWith('blank-')) {
        const blankIndex = parseInt(over.id.split('-')[1]);
        const newBlanks = [...blanks];

        // If dragging from options
        if (active.id.startsWith('option-')) {
          const optionIndex = parseInt(active.id.split('-')[1]);
          const newOptions = [...options];

          // Check if the blank already has a value
          const currentBlankValue = newBlanks[blankIndex];

          // If the blank already has a value, we need to free up that option first
          if (currentBlankValue !== null) {
            const currentOptionIndex = newOptions.findIndex(
              (opt) => opt.text === currentBlankValue
            );

            if (currentOptionIndex !== -1) {
              newOptions[currentOptionIndex] = {
                ...newOptions[currentOptionIndex],
                used: false,
              };
            }
          }

          // Count how many blanks are already filled
          const filledBlanksCount = blanks.filter(blank => blank !== null).length;

          // If this is a new option (not replacing an existing one in a blank)
          // and we've already reached the maximum number of blanks, don't allow it
          if (currentBlankValue === null && filledBlanksCount >= blanks.length) {
            // Don't allow more selections than blanks
            console.log("Maximum number of selections reached");
            setActiveId(null);
            setActiveOption(null);
            return;
          }

          // Update the blank with the option text
          newBlanks[blankIndex] = newOptions[optionIndex].text;

          // Mark the option as used
          newOptions[optionIndex] = {
            ...newOptions[optionIndex],
            used: true,
          };

          setOptions(newOptions);
        }
        // If dragging from another blank (swap)
        else if (active.id.startsWith('blank-')) {
          const sourceBlankIndex = parseInt(active.id.split('-')[1]);
          const temp = newBlanks[sourceBlankIndex];
          newBlanks[sourceBlankIndex] = newBlanks[blankIndex];
          newBlanks[blankIndex] = temp;
        }

        setBlanks(newBlanks);

        // Check if all blanks are filled
        const allFilled = newBlanks.every((blank) => blank !== null);
        setIsComplete(allFilled);

        // Check if answers are correct
        if (allFilled) {
          const isAnswerCorrect = checkAnswers(newBlanks);
          setIsCorrect(isAnswerCorrect);
        }
      }
      // If dropping back to options area (to unuse an option)
      else if (
        over.id === 'options-container' &&
        active.id.startsWith('blank-')
      ) {
        const blankIndex = parseInt(active.id.split('-')[1]);
        const blankValue = blanks[blankIndex];

        // Find the option that matches this value and mark it as unused
        const newOptions = [...options];
        const optionIndex = newOptions.findIndex(
          (opt) => opt.text === blankValue
        );

        if (optionIndex !== -1) {
          newOptions[optionIndex] = {
            ...newOptions[optionIndex],
            used: false,
          };
          setOptions(newOptions);
        }

        // Clear the blank
        const newBlanks = [...blanks];
        newBlanks[blankIndex] = null;
        setBlanks(newBlanks);
        setIsComplete(false);
      }
    }

    setActiveId(null);
    setActiveOption(null);
  };

  // Check if the answers are correct
  const checkAnswers = (filledBlanks) => {
    if (!question || !question.correct_answers) return false;

    // Compare each blank with the corresponding correct answer
    return filledBlanks.every(
      (answer, index) => answer === question.correct_answers[index]
    );
  };

  // Reset all selections and return options to their original state
  const handleResetSelections = () => {
    // Reset blanks to empty
    setBlanks(Array(blanks.length).fill(null));

    // Reset all options to unused
    const resetOptions = options.map((option) => ({
      ...option,
      used: false,
    }));
    setOptions(resetOptions);

    // Reset completion state
    setIsComplete(false);
    setIsCorrect(false);
  };

  // Reset a single blank and return its option to the available options
  const handleResetBlank = (index) => {
    // Get the value of the blank being reset
    const blankValue = blanks[index];

    if (blankValue) {
      // Create a new blanks array with the specified blank set to null
      const newBlanks = [...blanks];
      newBlanks[index] = null;
      setBlanks(newBlanks);

      // Find the option that matches this value and mark it as unused
      const newOptions = [...options];
      const optionIndex = newOptions.findIndex(
        (opt) => opt.text === blankValue
      );

      if (optionIndex !== -1) {
        newOptions[optionIndex] = {
          ...newOptions[optionIndex],
          used: false,
        };
        setOptions(newOptions);
      }

      // Update completion state
      setIsComplete(newBlanks.every((blank) => blank !== null));
      setIsCorrect(false);
    }
  };

  // Effect to handle correct answers and award points
  useEffect(() => {
    if (isCorrect && !hasAwardedPoints && typeof onCorrect === 'function') {
      // Calculate points - for simplicity, we'll award 10 points per correct answer
      const pointsPerQuestion = 10;

      // Pass the points and the answers for this question
      onCorrect(pointsPerQuestion, question?.id, blanks);
      setHasAwardedPoints(true);
    }
  }, [isCorrect, hasAwardedPoints, onCorrect, question?.id, blanks]);

  // Render the question text with blanks
  const renderQuestionWithBlanks = () => {
    if (!question) return null;

    // Split the question text by the [[gap]] markers
    const parts = question.question_text_plain.split(/\[\[gap\]\]/g);
    return (
      <div className="text-lg mb-8 flex items-center gap-1">
        {parts.map((part, index) => (
          <React.Fragment key={index}>
            {part}
            {index < parts.length - 1 && (
              <DroppableBlank
                id={`blank-${index}`}
                value={blanks[index]}
                index={index}
                onReset={handleResetBlank}
              />
            )}
          </React.Fragment>
        ))}
      </div>
    );
  };

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={rectIntersection} // Use rectIntersection for more precise collision detection
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
      modifiers={[restrictToWindowEdges]}
    >
      <div className="p-6 bg-white ">
        <h2 className="text-xl font-bold mb-6">
          Drag and drop blocks falling from the sky to match the words in the
          given sentence
        </h2>

        {/* Options container with reset button */}
        <div className="mb-8">
          <div className="flex justify-between items-center mb-3">
            <h3 className="text-md font-medium text-gray-700">
              {title}
            </h3>
            {blanks.some((blank) => blank !== null) && (
              <button
                onClick={handleResetSelections}
                className="flex items-center text-sm px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-md transition-colors"
              >
                <Icon icon="material-symbols:refresh" className="mr-1" />
                Reset Question
              </button>
            )}
          </div>

          {/* Waterfall container with fixed height and width */}
          <div className="relative h-[300px] border border-gray-100 rounded-lg bg-gradient-to-b from-blue-50 to-transparent overflow-hidden mb-4">
            {/* Water pool at bottom */}
            {/* <div className="absolute bottom-0 left-0 w-full h-12 bg-blue-100/30 rounded-b-lg"></div> */}

            {/* Options container with fixed dimensions */}
            <div
              id="options-container"
              className="w-full h-full relative"
              style={{
                touchAction: 'none', // Improve touch handling
                position: 'relative',
                overflow: 'hidden',
              }}
            >
              {/* This key ensures the options are only rendered once when the component mounts */}
              <div key="stable-options-container">
                {(() => {
                  // Count unused options to calculate grid layout
                  const unusedOptions = options.filter((opt) => !opt.used);
                  const totalUnusedOptions = unusedOptions.length;

                  // Render options with proper positioning
                  return options.map(
                    (option) =>
                      !option.used && (
                        <DraggableOption
                          key={option.id}
                          id={option.id}
                          option={option.text}
                          isActive={activeId === option.id}
                          index={unusedOptions.findIndex(
                            (opt) => opt.id === option.id
                          )}
                          totalOptions={totalUnusedOptions}
                        />
                      )
                  );
                })()}
              </div>
            </div>
          </div>

          {/* Selected options display */}
          <div className="flex flex-wrap gap-2 mt-2">
            {options.map(
              (option) =>
                option.used && (
                  <div
                    key={option.id}
                    className="bg-green-100 border border-green-300 text-green-800 rounded-md px-3 py-1 text-sm"
                  >
                    {option.text}
                  </div>
                )
            )}
          </div>
        </div>

        {/* Question with blanks */}
        <div className="mb-8">{renderQuestionWithBlanks()}</div>

        {/* Feedback message */}
        {isComplete && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className={`p-4 rounded-md ${
              isCorrect
                ? 'bg-green-100 text-green-800'
                : 'bg-red-100 text-red-800'
            }`}
          >
            <div className="flex justify-between items-center">
              <div>
                {isCorrect
                  ? `Great job! ${
                      question?.currect_answers?.length > 0
                        ? 'All answers are correct!'
                        : 'Your answer is correct!'
                    }`
                  : `Try again! ${
                      question?.currect_answers?.length > 0
                        ? 'Some answers are not correct.'
                        : 'Your answer is not correct.'
                    }`}
              </div>

              {(typeof onNext === 'function' && !isLastQuestion) ? (
                <button
                  onClick={onNext}
                  className={`px-4 py-1 bg-green-500 hover:bg-green-600 text-white rounded-md ml-4 flex items-center`}
                >
                  Next{' '}
                  <span className="ml-1">→</span>
                </button>
              ) : (
                <button
                  onClick={handleSubmit}
                  className={`px-4 py-1 bg-yellow-500 hover:bg-yellow-600 text-white rounded-md ml-4 flex items-center`}
                >
                  Submit{' '}
                  <span className="ml-1">→</span>
                </button>
              )}
            </div>
          </motion.div>
        )}

        {/* Drag overlay */}
        <DragOverlay>
          {activeOption ? <DragOverlayContent option={activeOption} /> : null}
        </DragOverlay>
      </div>
    </DndContext>
  );
};

export default WaterfallDragDrop;
