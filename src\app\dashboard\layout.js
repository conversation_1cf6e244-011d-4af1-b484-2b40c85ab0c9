'use client';
import { useState, useEffect } from 'react';
import { Icon } from '@iconify/react';
import {
  NotificationBell,
  NotificationPanel,
} from '@/components/notifications';
import Sidebar from '@/components/dashboard/Sidebar';
import Breadcrumbs from '@/components/dashboard/Breadcrumbs';
import PrivateRoute from '@/components/shared/PrivateRoute';
import { useRouter } from 'next/navigation';
import { useDispatch, useSelector } from 'react-redux';
import { logoutUser } from '@/store/features/authSlice';
import Image from 'next/image';
import { IMAGE_BASE_URL } from '@/lib/config';
import ChatBell from '@/components/ChatBell';
import { getUserRoleFromCookie } from '@/lib/auth';

const DashboardLayout = ({ children }) => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  const [isMobile, setIsMobile] = useState(false);
  const [showDropdown, setShowDropdown] = useState(false);
  const { user } = useSelector((state) => state.auth);
  const router = useRouter();
  const dispatch = useDispatch();
  const [previewImg, setPreviewImg] = useState(null);

  useEffect(() => {
    if (user?.profilePictureUrl) {
      setPreviewImg(user?.profilePictureUrl);
    } else {
      setPreviewImg('/assets/images/all-img/avatar.png');
    }
  }, [user]);

  // Handle window resize and set mobile state
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
      if (window.innerWidth < 768) {
        setIsSidebarOpen(false);
      } else {
        setIsSidebarOpen(true);
      }
    };

    // Set initial state
    handleResize();

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  return (
    // <PrivateRoute>
    <div className="min-h-screen bg-gray-50">
      {/* Overlay for mobile */}
      {isMobile && isSidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-30"
          onClick={toggleSidebar}
        />
      )}

      {/* Sidebar */}
      <Sidebar
        isOpen={isSidebarOpen}
        toggleSidebar={toggleSidebar}
        isMobile={isMobile}
      />

      {/* Main Content */}
      <div
        className={`transition-all duration-300 pt-5 bg-white
          ${
            isMobile
              ? isSidebarOpen
                ? 'ml-0'
                : 'ml-0'
              : isSidebarOpen
              ? 'ml-72'
              : 'ml-20'
          }
        `}
      >
        {/* Header */}
        <div className="bg-white relative z-30">
          {/* fixed */}
          <header
            className={`fixed right-0 py-3 z-20 bg-[#FEFCE8] shadow-md flex items-center justify-between px-6 max-sm:px-2 mx-5 rounded-xl
              ${
                isMobile
                  ? 'w-[calc(100%-2.5rem)]'
                  : isSidebarOpen
                  ? 'w-[calc(100%-20rem)]'
                  : 'w-[calc(100%-6.5rem)]'
              }
            `}
          >
            <div className="flex items-center gap-4">
              {isMobile && (
                // Toggle button for mobile only
                <button
                  onClick={toggleSidebar}
                  className="p-2 rounded-lg hover:bg-gray-100 md:hidden"
                  aria-label="Toggle Sidebar"
                >
                  <Icon
                    icon={isSidebarOpen ? 'lucide:x' : 'material-symbols:menu'}
                    className="w-6 h-6"
                  />
                </button>
              )}
              <h1 className="hidden sm:block text-xl font-semibold truncate">
                Hello English Coaching - HEC
              </h1>
            </div>

            <div className="flex items-center space-x-4 max-sm:space-x-2">
              {/* <button
                className="p-2 rounded-lg hover:bg-gray-100"
                aria-label="Search"
              >
                <Icon icon="lucide:search" className="w-5 h-5" />
              </button> */}
              <ChatBell />
              {/* Dashboard layout is already protected, but adding isAuth check for consistency */}
              {user && (
                <div className="relative">
                  <NotificationBell />
                  <NotificationPanel />
                </div>
              )}
              <div className="relative">
                <div
                  onClick={() => setShowDropdown(!showDropdown)}
                  className="h-10 w-10 rounded-full cursor-pointer"
                >
                  <Image
                    src={previewImg || '/assets/images/all-img/avatar.png'}
                    alt="User"
                    width={40}
                    height={40}
                    onError={() =>
                      setPreviewImg('/assets/images/all-img/avatar.png')
                    }
                    className="w-full h-full rounded-full border"
                  />
                </div>

                {/* Dropdown Menu */}
                {showDropdown && (
                  <>
                    {/* Overlay to close dropdown when clicking outside */}
                    <div
                      className="fixed inset-0 z-10"
                      onClick={() => setShowDropdown(false)}
                    />

                    <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-20 py-1 border border-gray-200">
                      <button
                        onClick={() => {
                          const userRole = getUserRoleFromCookie();
                          if (userRole === 'admin' && user?.id) {
                            router.push(`/dashboard/admin-profile/${user.id}`);
                          } else if (userRole === 'tutor' && user?.id) {
                            router.push(`/dashboard/tutor-profile/${user.id}`);
                          } else {
                            // Fallback for unknown role or missing ID
                            router.push('/dashboard');
                          }
                          setShowDropdown(false);
                        }}
                        className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                      >
                        <Icon icon="lucide:user" className="w-4 h-4 mr-2" />
                        Profile
                      </button>

                      <button
                        onClick={() => {
                          router.push('/change-password');
                          setShowDropdown(false);
                        }}
                        className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                      >
                        <Icon icon="lucide:lock" className="w-4 h-4 mr-2" />
                        Change Password
                      </button>

                      <div className="border-t border-gray-200 my-1" />

                      <button
                        onClick={() => {
                          dispatch(logoutUser());
                          router.push('/');
                          setShowDropdown(false);
                        }}
                        className="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100 flex items-center"
                      >
                        <Icon icon="lucide:log-out" className="w-4 h-4 mr-2" />
                        Logout
                      </button>
                    </div>
                  </>
                )}
              </div>
            </div>
          </header>
          <div className="h-14"></div>
        </div>

        {/* Breadcrumbs */}
        <div className="px-6 pt-4">
          <Breadcrumbs />
        </div>

        {/* Page Content */}
        <main className="px-6 ">{children}</main>
      </div>
    </div>
    // </PrivateRoute>
  );
};

export default DashboardLayout;
