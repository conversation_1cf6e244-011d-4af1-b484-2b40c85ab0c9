import React, { useState } from 'react';
import { Icon } from '@iconify/react';
import useDataFetch from '@/hooks/useDataFetch';
import { useSelector } from 'react-redux';

const StudentsTable = ({
  students = [],
  title = 'Students subscription status',
  showSeeAll = true,
  className = '',
}) => {
  const [activePlanIdx, setActivePlanIdx] = useState(0);
  const { user } = useSelector((state) => state.auth);
  const isTutor = user?.type === 'tutor';

  const { data: subscriptionStatus, isLoading: isSubscriptionLoading } =
    useDataFetch({
      queryKey: 'dashboard-subscription-stats',
      endPoint: '/admin/dashboard/subscription-status',
      enabled: !isTutor,
    });

  // Sample data if none provided
  const defaultStudents = [
    {
      id: 1,
      name: '<PERSON>',
      userId: '253614957535',
      email: '<EMAIL>',
      phone: '01680605860',
      gender: 'Male',
      plan: 'Premium Plan',
    },
    {
      id: 2,
      name: '<PERSON>',
      userId: '253614957535',
      email: '<EMAIL>',
      phone: '01680605860',
      gender: 'Male',
      plan: 'Trial Plan',
    },
    {
      id: 3,
      name: 'MD Rashed Khan',
      userId: '253614957535',
      email: '<EMAIL>',
      phone: '01680605860',
      gender: 'Male',
      plan: 'Free Plan',
    },
    {
      id: 4,
      name: 'MD Rashed Khan',
      userId: '253614957535',
      email: '<EMAIL>',
      phone: '01680605860',
      gender: 'Male',
      plan: 'Premium Plan',
    },
    {
      id: 5,
      name: 'MD Rashed Khan',
      userId: '253614957535',
      email: '<EMAIL>',
      phone: '01680605860',
      gender: 'Male',
      plan: 'Trial Plan',
    },
    {
      id: 6,
      name: 'MD Rashed Khan',
      userId: '253614957535',
      email: '<EMAIL>',
      phone: '01680605860',
      gender: 'Male',
      plan: 'Free Plan',
    },
  ];

  const tableData = students.length > 0 ? students : defaultStudents;

  const getPlanBadge = (plan) => {
    const planStyles = {
      'Premium Plan': 'bg-yellow-100 text-yellow-800',
      'Trial Plan': 'bg-blue-100 text-blue-800',
      'Free Plan': 'bg-gray-100 text-gray-800',
    };

    return (
      <span
        className={`px-3 py-1 rounded-full text-xs font-medium ${
          planStyles[plan] || 'bg-gray-100 text-gray-800'
        }`}
      >
        {plan}
      </span>
    );
  };

  return (
    <div
      className={`bg-white rounded-lg shadow-lg border border-gray-200 p-2 ${className}`}
    >
      {/* Header */}
      <div className="flex items-center justify-between p-3 px-6 border-b border-gray-100">
        <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
        {showSeeAll && (
          <button className="text-sm text-blue-600 hover:text-blue-700 font-medium">
            See all
          </button>
        )}
      </div>

      {/* Plan tabs */}
      <div className="flex border-b border-gray-100">
        {isSubscriptionLoading ? (
          <div className="flex-1 flex justify-center">
            <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-yellow-500 mx-auto"></div>
          </div>
        ) : (
          subscriptionStatus?.planBreakdown?.map((plan, idx) => (
            <button
              key={idx}
              className={`px-6 py-3 text-sm font-medium ${
                activePlanIdx === idx
                  ? 'text-black border-b-2 border-yellow-400 bg-yellow-50'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
              onClick={() => setActivePlanIdx(idx)}
            >
              {plan?.planName} ({plan?.studentCount})
            </button>
          ))
        )}
      </div>

      {/* Table */}
      <div className="overflow-x-auto opacity-25">
        <table className="w-full">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Student Name
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                User ID
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Email Address
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Phone Number
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Gender
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {tableData.map((student, index) => (
              <tr key={student.id || index} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 h-8 w-8">
                      <div className="h-8 w-8 rounded-full bg-orange-100 flex items-center justify-center">
                        <Icon
                          icon="mdi:account"
                          className="h-4 w-4 text-orange-600"
                        />
                      </div>
                    </div>
                    <div className="ml-3">
                      <div className="text-sm font-medium text-gray-900">
                        {student.name}
                      </div>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {student.userId}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {student.email}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {student.phone}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {student.gender}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default StudentsTable;
