import React from 'react';
import dynamic from 'next/dynamic';
import useDataFetch from '@/hooks/useDataFetch';

// Dynamically import ApexCharts to avoid SSR issues
const Chart = dynamic(() => import('react-apexcharts'), { ssr: false });

const PerformanceChart = ({
  title = "Student Activity Performance",
  className = ''
}) => {
  // Fetch student daily activity data
  const { data: activityData, isLoading, error } = useDataFetch({
    queryKey: ['admin-dashboard-student-daily-activity'],
    endPoint: '/admin/dashboard/student-daily-activity',
  });

  // Process the API data for ApexCharts
  const processChartData = () => {
    if (!activityData?.yearlyStats?.length) {
      return { categories: [], series: [] };
    }

    // Get the latest year's data
    const latestYear = activityData.yearlyStats[0];
    const monthlyStats = latestYear.monthlyStats || [];

    // Sort by month to ensure correct order
    const sortedStats = monthlyStats.sort((a, b) => a.month - b.month);

    const categories = sortedStats.map(stat => stat.monthName);

    // Create series for each activity type
    const series = [
      {
        name: 'Diary Entries',
        data: sortedStats.map(stat => stat.diaryEntries || 0)
      },
      {
        name: 'QA Submissions',
        data: sortedStats.map(stat => stat.qaSubmissions || 0)
      },
      {
        name: 'Essay Submissions',
        data: sortedStats.map(stat => stat.essaySubmissions || 0)
      },
      {
        name: 'Novel Entries',
        data: sortedStats.map(stat => stat.novelEntries || 0)
      },
      {
        name: 'Story Maker Participations',
        data: sortedStats.map(stat => stat.storyMakerParticipations || 0)
      }
    ];

    return { categories, series };
  };

  const { categories, series } = processChartData();

  // ApexCharts configuration
  const chartOptions = {
    chart: {
      type: 'line',
      height: 350,
      toolbar: {
        show: false
      }
    },
    stroke: {
      curve: 'smooth',
      width: 3
    },
    dataLabels: {
      enabled: false
    },
    colors: ['#FBBF24', '#10B981', '#3B82F6', '#8B5CF6', '#F59E0B'], // Different colors for each line
    xaxis: {
      categories: categories,
      labels: {
        style: {
          colors: '#6B7280',
          fontSize: '12px'
        }
      }
    },
    yaxis: {
      labels: {
        style: {
          colors: '#6B7280',
          fontSize: '12px'
        }
      }
    },
    grid: {
      borderColor: '#F3F4F6',
      strokeDashArray: 3
    },
    legend: {
      position: 'top',
      horizontalAlign: 'left',
      fontSize: '12px',
      fontFamily: 'Inter, sans-serif',
      markers: {
        width: 8,
        height: 8,
        radius: 4
      }
    },
    tooltip: {
      y: {
        formatter: function (val) {
          return `<b>${val}<b/>`;
        }
      }
    }
  };

  if (isLoading) {
    return (
      <div className={`bg-white rounded-lg p-6 shadow border border-gray-200 w-full ${className}`}>
        <h3 className="text-lg font-semibold text-gray-900 mb-6">{title}</h3>
        <div className="flex justify-center items-center h-64">
          <div className="text-gray-500">Loading chart data...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-white rounded-lg p-6 shadow border border-gray-200 w-full ${className}`}>
        <h3 className="text-lg font-semibold text-gray-900 mb-6">{title}</h3>
        <div className="flex justify-center items-center h-64">
          <div className="text-red-500">Error loading chart data</div>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg p-6 shadow border border-gray-200 w-full ${className}`}>
      <h3 className="text-lg font-semibold text-gray-900 mb-6">{title}</h3>

      {series.length > 0 ? (
        <Chart
          options={chartOptions}
          series={series}
          type="line"
          height={350}
        />
      ) : (
        <div className="flex justify-center items-center h-64">
          <div className="text-gray-500">No data available</div>
        </div>
      )}
    </div>
  );
};

export default PerformanceChart;
