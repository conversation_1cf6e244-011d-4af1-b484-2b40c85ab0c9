'use client';
import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Formik, Form, Field } from 'formik';
import * as Yup from 'yup';
import { Icon } from '@iconify/react';
import { toast } from 'react-hot-toast';
import Image from 'next/image';
import { format } from 'date-fns';
import useDataFetch from '@/hooks/useDataFetch';
import api from '@/lib/api';
import FormSelect from '@/components/form/FormSelect';
import FormInput from '@/components/form/FormInput';
import { IMAGE_BASE_URL } from '@/lib/config';

const TutorProfile = () => {
  const router = useRouter();
  const { id } = useParams();
  const [editingId, setEditingId] = useState(null);
  const [isAddingNew, setIsAddingNew] = useState(false);

  // Add state for file upload
  const [selectedFile, setSelectedFile] = useState(null);
  const [preview, setPreview] = useState(null);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = React.useRef(null);

  // Add state for coaching experience editing
  const [isEditingExperience, setIsEditingExperience] = useState(false);

  // Fetch tutor profile data
  const {
    data: response,
    isLoading,
    error,
    refetch,
  } = useDataFetch({
    queryKey: ['tutor-profile', id],
    endPoint: `/profiles/tutor/${id}`,
  });

  // Extract the tutor profile data from the response
  const tutorProfile = response?.data || response;

  // Handle file selection for profile picture
  const handleFileSelect = (e) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Check if file type is allowed (only jpeg, png, gif)
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
    if (!allowedTypes.includes(file.type)) {
      toast.error('Please select a JPEG, PNG, or GIF image file');
      return;
    }

    if (file.size > 5 * 1024 * 1024) {
      toast.error('File size must be less than 5MB');
      return;
    }

    setSelectedFile(file);
    const reader = new FileReader();
    reader.onloadend = () => setPreview(reader.result);
    reader.readAsDataURL(file);
  };

  // Handle profile picture upload
  const handleUpload = async () => {
    if (!selectedFile) return;

    setIsUploading(true);
    try {
      const formData = new FormData();
      formData.append('file', selectedFile); // Using 'file' as the field name

      const response = await api.post('/users/profile/picture', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      console.log('Upload response:', response); // Log the response for debugging
      setSelectedFile(null);
      setPreview(null);
      refetch(); // Refresh the profile data
    } catch (error) {
      console.error('Error uploading image:', error);
    } finally {
      setIsUploading(false);
    }
  };

  // Cancel profile picture upload
  const cancelUpload = () => {
    setSelectedFile(null);
    setPreview(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Education validation schema
  const educationSchema = Yup.object().shape({
    degree: Yup.string().required('Degree/Title is required'),
    institution: Yup.string().required('Institution is required'),
    fieldOfStudy: Yup.string().required('Field of study is required'),
    startDate: Yup.date().required('Start date is required'),
    isCurrent: Yup.boolean(),
    endDate: Yup.date().when('isCurrent', {
      is: false,
      then: (schema) => schema.required('End date is required'),
      otherwise: (schema) => schema.nullable(),
    }),
    grade: Yup.string(),
    activities: Yup.string(),
    description: Yup.string(),
    location: Yup.string(),
  });

  const completionStatusOptions = [
    { value: 'true', label: 'Currently Enrolled' },
    { value: 'false', label: 'Completed' },
  ];

  const newEducationInitialValues = {
    degree: '',
    institution: '',
    fieldOfStudy: '',
    startDate: '',
    endDate: '',
    isCurrent: 'false',
    grade: '',
    activities: '',
    description: '',
    location: '',
  };

  // Handle education form submission
  const handleEducationSubmit = async (
    values,
    { setSubmitting, resetForm }
  ) => {
    try {
      // Extract only the allowed fields for the API request
      const educationData = {
        degree: values.degree,
        institution: values.institution,
        fieldOfStudy: values.fieldOfStudy,
        startDate: values.startDate,
        endDate:
          values.isCurrent === 'true' || values.isCurrent === true
            ? null
            : values.endDate,
        isCurrent: values.isCurrent === 'true' || values.isCurrent === true,
        description: values.description || '',
        location: values.location || '',
        grade: values.grade || '',
        activities: values.activities || '',
      };

      // If editing existing education
      if (editingId) {
        // Call API to update education - using the correct endpoint
        await api.patch(`/tutors/education/${editingId}`, educationData);
        setEditingId(null);
      } else {
        // Add new education - using the correct endpoint
        await api.post('/tutors/my-education', educationData);
        setIsAddingNew(false);
      }

      resetForm();
      refetch(); // Refresh the data
    } catch (error) {
      console.error('Error updating education:', error);

      // More detailed error handling
      if (error.response?.data?.validationErrors) {
        const validationErrors = error.response.data.validationErrors;
        const errorMessage = Object.entries(validationErrors)
          .map(([field, errors]) => `${field}: ${errors.join(', ')}`)
          .join('\n');
      } else {
        console.log(
          error.response?.data?.message || 'Failed to update education'
        );
      }
    } finally {
      setSubmitting(false);
    }
  };

  const handleDelete = async (id) => {
    if (!confirm('Are you sure you want to delete this education?')) return;

    try {
      // Use the correct endpoint for deletion as well
      await api.delete(`/tutors/education/${id}`);
      refetch(); // Refresh the data
    } catch (error) {
      console.error('Error deleting education:', error);
    }
  };

  // Format dates
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    try {
      return format(new Date(dateString), 'MMM dd, yyyy');
    } catch (e) {
      console.error('Date formatting error:', e);
      return 'Invalid Date';
    }
  };

  // Render education item with inline editing
  const renderEducationItem = (edu, index) => {
    if (editingId === edu.id) {
      return (
        <div
          key={index}
          className="mt-8 bg-white border rounded-md shadow-lg p-4"
        >
          <Formik
            initialValues={{
              ...edu,
              isCurrent: edu.isCurrent ? 'true' : 'false',
            }}
            validationSchema={educationSchema}
            onSubmit={handleEducationSubmit}
          >
            {({ isSubmitting, values }) => (
              <Form>
                <div className="grid md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <FormInput label="DEGREE/TITLE" name="degree" type="text" />
                    <FormInput
                      label="INSTITUTION"
                      name="institution"
                      type="text"
                    />
                    <FormSelect
                      label="COMPLETION STATUS"
                      name="isCurrent"
                      options={completionStatusOptions}
                    />
                    <FormInput
                      label="START DATE"
                      name="startDate"
                      type="date"
                    />
                    {values.isCurrent === 'false' && (
                      <FormInput label="END DATE" name="endDate" type="date" />
                    )}
                  </div>
                  <div className="space-y-4">
                    <FormInput
                      label="FIELD OF STUDY"
                      name="fieldOfStudy"
                      type="text"
                    />
                    <FormInput label="GRADE" name="grade" type="text" />
                    <FormInput
                      label="ACTIVITIES"
                      name="activities"
                      type="text"
                    />
                    <FormInput label="LOCATION" name="location" type="text" />
                    <FormInput
                      label="DESCRIPTION"
                      name="description"
                      type="textarea"
                      rows={3}
                    />
                  </div>
                </div>
                <div className="flex justify-end gap-2 mt-4">
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="px-4 py-2 bg-yellow-500 text-white rounded-md hover:bg-yellow-600"
                  >
                    {isSubmitting ? 'Saving...' : 'Save'}
                  </button>
                  <button
                    type="button"
                    onClick={() => setEditingId(null)}
                    className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300"
                  >
                    Cancel
                  </button>
                </div>
              </Form>
            )}
          </Formik>
        </div>
      );
    }

    return (
      <div
        key={index}
        className="mt-8 bg-white border rounded-md shadow-lg p-4"
      >
        <div className="flex justify-between items-center mb-4">
          <h3 className="font-medium text-amber-800">
            Education {tutorProfile.education.length - index}
          </h3>
          <div className="flex-1 h-0.5 bg-gray-200"></div>
          <div className="flex gap-2">
            <button
              type="button"
              onClick={() => setEditingId(edu.id)}
              className="p-2 hover:bg-gray-100 rounded-full"
            >
              <Icon icon="material-symbols:edit-outline" className="w-5 h-5" />
            </button>
            <button
              type="button"
              onClick={() => handleDelete(edu.id)}
              className="p-2 hover:bg-gray-100 rounded-full text-red-500"
            >
              <Icon
                icon="material-symbols:delete-outline"
                className="w-5 h-5"
              />
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div>
            <div className="flex items-center mb-4">
              <div className="bg-yellow-100 p-2 rounded mr-2">
                <Icon icon="mdi:school" className="text-yellow-600" />
              </div>
              <div>
                <h5 className="font-medium text-gray-700 text-sm">TITLE</h5>
                <p className="text-sm text-gray-700 mt-1">
                  {edu.degree || "Bachelor's in Science"}
                </p>
              </div>
            </div>
            <div className="mt-4 mb-2">
              <h5 className="font-medium text-gray-700 text-sm">INSTITUTE</h5>
            </div>
            <p className="text-sm text-gray-700">
              {edu.institution ||
                'Bangladesh University of Business & Technology (BUBT)'}
            </p>

            <div className="mt-4 mb-2">
              <h5 className="font-medium text-gray-700 text-sm">
                COMPLETION STATUS
              </h5>
            </div>
            <p className="text-sm text-gray-700">
              {edu.isCurrent ? 'Enrolled' : 'Passed'}
            </p>
          </div>

          <div>
            <div className="mb-4">
              <h5 className="font-medium mb-1 text-gray-700 text-sm">
                DEPARTMENT
              </h5>
              <p className="text-sm text-gray-700">
                {edu.fieldOfStudy || 'CSE'}
              </p>
            </div>

            <div className="mb-4">
              <h5 className="font-medium mb-1 text-gray-700 text-sm">
                PASSED YEAR
              </h5>
              <p className="text-sm text-gray-700">
                {edu.endDate
                  ? formatDate(edu.endDate).split(',')[1].trim()
                  : '2022'}
              </p>
            </div>

            {edu.grade && (
              <div className="mb-4">
                <h5 className="font-medium mb-1 text-gray-700 text-sm">
                  GRADE
                </h5>
                <p className="text-sm text-gray-700">{edu.grade}</p>
              </div>
            )}

            {edu.activities && (
              <div className="mb-4">
                <h5 className="font-medium mb-1 text-gray-700 text-sm">
                  ACTIVITIES
                </h5>
                <p className="text-sm text-gray-700">{edu.activities}</p>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  // Add new education form
  const renderAddEducationForm = () => {
    return (
      <div className="mt-8 bg-white border rounded-md shadow-lg p-4">
        <Formik
          initialValues={newEducationInitialValues}
          validationSchema={educationSchema}
          onSubmit={handleEducationSubmit}
        >
          {({ isSubmitting, values }) => (
            <Form>
              <h3 className="font-medium text-amber-800 mb-4">
                Add New Education
              </h3>
              <div className="grid md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <FormInput label="DEGREE/TITLE" name="degree" type="text" />
                  <FormInput
                    label="INSTITUTION"
                    name="institution"
                    type="text"
                  />
                  <FormSelect
                    label="COMPLETION STATUS"
                    name="isCurrent"
                    options={completionStatusOptions}
                  />
                  <FormInput label="START DATE" name="startDate" type="date" />
                  {values.isCurrent === 'false' && (
                    <FormInput label="END DATE" name="endDate" type="date" />
                  )}
                </div>
                <div className="space-y-4">
                  <FormInput
                    label="FIELD OF STUDY"
                    name="fieldOfStudy"
                    type="text"
                  />
                  <FormInput label="GRADE" name="grade" type="text" />
                  <FormInput label="ACTIVITIES" name="activities" type="text" />
                  <FormInput label="LOCATION" name="location" type="text" />
                  <FormInput
                    label="DESCRIPTION"
                    name="description"
                    type="textarea"
                    rows={3}
                  />
                </div>
              </div>
              <div className="flex justify-end gap-2 mt-4">
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="px-4 py-2 bg-yellow-500 text-white rounded-md hover:bg-yellow-600"
                >
                  {isSubmitting ? 'Adding...' : 'Add Education'}
                </button>
                <button
                  type="button"
                  onClick={() => setIsAddingNew(false)}
                  className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300"
                >
                  Cancel
                </button>
              </div>
            </Form>
          )}
        </Formik>
      </div>
    );
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-[60vh]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-yellow-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <Icon
          icon="mdi:alert-circle"
          className="text-6xl text-red-500 mx-auto mb-4"
        />
        <h2 className="text-2xl font-bold text-gray-800 mb-2">
          Error Loading Profile
        </h2>
        <p className="text-gray-600 mb-6">
          {error.message || 'There was an error loading the tutor profile.'}
        </p>
        <button
          onClick={() => router.back()}
          className="px-4 py-2 bg-yellow-500 text-white rounded-lg hover:bg-yellow-600 transition-colors"
        >
          Go Back
        </button>
      </div>
    );
  }

  if (!tutorProfile) {
    return (
      <div className="text-center py-12">
        <Icon
          icon="mdi:account-alert"
          className="text-6xl text-yellow-500 mx-auto mb-4"
        />
        <h2 className="text-2xl font-bold text-gray-800 mb-2">
          Tutor Not Found
        </h2>
        <p className="text-gray-600 mb-6">
          The tutor profile you're looking for doesn't exist or you don't have
          access to it.
        </p>
        <button
          onClick={() => router.back()}
          className="px-4 py-2 bg-yellow-500 text-white rounded-lg hover:bg-yellow-600 transition-colors"
        >
          Go Back
        </button>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto min-h-screen pb-8">
      {/* Back button */}
      <div
        className="flex items-center p-3 text-gray-600  bg-white"
        onClick={() => router.back()}
      >
        <Icon icon="mdi:arrow-left" className="mr-2" />
        <span>Tutor Details</span>
      </div>

      {/* Tutor Header Card */}
      <div className="border rounded-md m-4 p-4 shadow-sm bg-white">
        <div className="flex items-center">
          <div className="relative">
            {/* Profile Image - Increased size from h-16 w-16 to h-24 w-24 */}
            <div className="relative h-24 w-24 rounded-full bg-gray-200 overflow-hidden">
              {preview ? (
                <Image
                  src={preview}
                  alt="Preview"
                  fill
                  className="object-cover"
                />
              ) : tutorProfile?.profilePictureUrl ||
                tutorProfile?.profilePicture ? (
                <Image
                  src={
                    (
                      tutorProfile.profilePictureUrl ||
                      tutorProfile.profilePicture
                    ).startsWith('http')
                      ? tutorProfile.profilePictureUrl ||
                        tutorProfile.profilePicture
                      : `${IMAGE_BASE_URL}/${
                          tutorProfile.profilePictureUrl ||
                          tutorProfile.profilePicture
                        }`
                  }
                  alt={tutorProfile.name || 'Tutor'}
                  fill
                  className="object-cover"
                />
              ) : (
                <Image
                  src="/assets/images/all-img/avatar.png"
                  alt={tutorProfile.name || 'Tutor'}
                  fill
                  className="object-cover"
                />
              )}
            </div>

            {/* Upload Controls - Changed icon color to gray */}
            {!selectedFile ? (
              <label
                htmlFor="profileImage"
                className="absolute bottom-0 right-0 bg-white p-2 rounded-full shadow-md cursor-pointer hover:bg-gray-100 border-2 border-gray-300"
              >
                <Icon
                  icon="solar:camera-bold"
                  className="w-5 h-5 text-gray-600"
                />
                <input
                  type="file"
                  id="profileImage"
                  ref={fileInputRef}
                  accept="image/jpeg,image/png,image/gif"
                  className="hidden"
                  onChange={handleFileSelect}
                />
              </label>
            ) : (
              <div className="absolute bottom-0 right-0 flex gap-1">
                
                <button
                  onClick={cancelUpload}
                  className="bg-gray-400 p-1 rounded-full shadow-md cursor-pointer hover:bg-gray-500 transition-colors"
                >
                  <Icon
                    icon="material-symbols:close"
                    className="w-4 h-4 text-white"
                  />
                </button>
                <button
                  onClick={handleUpload}
                  disabled={isUploading}
                  className="bg-yellow-500 p-1 rounded-full shadow-md cursor-pointer hover:bg-yellow-600 disabled:bg-yellow-300 transition-colors"
                >
                  <Icon
                    icon={
                      isUploading
                        ? 'eos-icons:loading'
                        : 'mdi:content-save-check-outline'
                    }
                    className="w-4 h-4 text-white"
                  />
                </button>
              </div>
            )}
          </div>

          <div className="ml-6">
            <h2 className="font-medium text-xl">
              {tutorProfile.name || 'Tutor Full Name'}
            </h2>
            <p className="text-gray-500 text-sm">
              User Id: {tutorProfile.userId || '123456789'}
            </p>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="m-4">
        {/* Basic Information Section */}
        <div className="mb-4">
          <div className="flex items-center gap-2 mb-2">
            <h3 className="font-medium text-lg text-amber-800 whitespace-nowrap">
              Basic Information
            </h3>
            <div className="flex-1 h-0.5 bg-gray-300"></div>
          </div>
        </div>

        <div className="bg-white border rounded-md shadow-lg p-4">
          <div className="flex items-center justify-between mb-4">
            <div className="flex flex-1 gap-4">
              <h4 className="font-medium text-amber-800 flex-1">
                Personal Details
              </h4>
              <h4 className="font-medium text-amber-800 flex-1 hidden md:block ml-28 ">
                Contact Details
              </h4>
            </div>
            <button
              onClick={() =>
                router.push(
                  `/dashboard/tutor-profile/edit/${id}?section=personal`
                )
              }
              className="text-blue-500 border border-blue-500 rounded p-1 ml-4"
            >
              <Icon icon="mdi:pencil" className="w-4 h-4" />
            </button>
          </div>
          <div className="flex flex-col md:flex-row gap-0 md:gap-8">
            {/* Personal Details */}
            <div className="flex-1">
              {/* Biography */}
              <div className="mb-4">
                <h5 className="font-bold mb-1 text-gray-700 text-sm">
                  Biography
                </h5>
                <p className="text-sm text-gray-700">
                  {tutorProfile.bio ||
                    'Hi, I have 5 years experience in teaching profession. I can make easier students lesson what is very effective.'}
                </p>
              </div>
              {/* First & Last Name Side by Side */}
              <div className="mb-4 flex gap-8">
                <div className="flex-1">
                  <h5 className="font-bold mb-1 text-gray-700 text-sm">
                    FIRST NAME
                  </h5>
                  <p className="text-sm text-gray-700">
                    {tutorProfile.name
                      ? tutorProfile.name.split(' ')[0]
                      : 'Enter your first name here'}
                  </p>
                </div>
                <div className="flex-1">
                  <h5 className="font-bold mb-1 text-gray-700 text-sm">
                    LAST NAME
                  </h5>
                  <p className="text-sm text-gray-700">
                    {tutorProfile.name
                      ? tutorProfile.name.split(' ').slice(1).join(' ')
                      : 'Enter your last name here'}
                  </p>
                </div>
              </div>
              {/* Father's Name */}
              <div className="mb-4">
                <h5 className="font-bold mb-1 text-gray-700 text-sm">
                  FATHER'S NAME
                </h5>
                <p className="text-sm text-gray-700">
                  {tutorProfile.fatherName || 'Full Name'}
                </p>
              </div>
              {/* Mother's Name */}
              <div className="mb-4">
                <h5 className="font-bold mb-1 text-gray-700 text-sm">
                  MOTHER'S NAME
                </h5>
                <p className="text-sm text-gray-700">
                  {tutorProfile.motherName || 'Full Name'}
                </p>
              </div>
              {/* Gender */}
              <div className="mb-4">
                <h5 className="font-bold mb-1 text-gray-700 text-sm">GENDER</h5>
                <p className="text-sm text-gray-700 capitalize">
                  {tutorProfile.gender || 'Male'}
                </p>
              </div>
              {/* Age */}
              <div className="mb-4">
                <h5 className="font-bold mb-1 text-gray-700 text-sm">AGE</h5>
                <p className="text-sm text-gray-700">
                  {tutorProfile.age ? `${tutorProfile.age} years` : '34 years'}
                </p>
              </div>
              {/* Blood Group */}
              <div className="mb-4">
                <h5 className="font-bold mb-1 text-gray-700 text-sm">
                  BLOOD GROUP
                </h5>
                <p className="text-sm text-gray-700">
                  {tutorProfile.bloodGroup || 'O+'}
                </p>
              </div>
            </div>
            {/* Vertical Dotted Line */}
            <div className="hidden md:flex flex-col items-center justify-center mx-4">
              <div
                className="h-full w-0.5 border-r-2 border-dotted border-gray-300"
                style={{ minHeight: '300px' }}
              ></div>
            </div>
            {/* Contact Details */}
            <div className="flex-1">
              <h4 className="font-medium text-amber-800 mb-4 md:hidden">
                Contact Details
              </h4>
              {/* Address */}
              <div className="mb-4">
                <h5 className="font-bold mb-1 text-gray-700 text-sm">
                  ADDRESS
                </h5>
                <p className="text-sm text-gray-700">
                  {tutorProfile.address ||
                    'Placeholder address placeholder address'}
                </p>
              </div>
              {/* Phone Number */}
              <div className="mb-4">
                <h5 className="font-bold mb-1 text-gray-700 text-sm">
                  PHONE NUMBER
                </h5>
                <p className="text-sm text-gray-700">
                  {tutorProfile.phoneNumber || '+880 1900000000'}
                </p>
              </div>
              {/* Alternate Number */}
              <div className="mb-4">
                <h5 className="font-bold mb-1 text-gray-700 text-sm">
                  ALTERNATE NUMBER
                </h5>
                <p className="text-sm text-gray-700">
                  {tutorProfile.alternateNumber || '+880 1900000000'}
                </p>
              </div>
              {/* Email */}
              <div className="mb-4">
                <h5 className="font-bold mb-1 text-gray-700 text-sm">EMAIL</h5>
                <p className="text-sm text-gray-700">
                  {tutorProfile.email || '<EMAIL>'}
                </p>
              </div>
              {/* Tutor ID */}
              <div className="mb-4">
                <h5 className="font-bold mb-1 text-gray-700 text-sm">
                  TUTOR ID
                </h5>
                <p className="text-sm text-gray-700">
                  {tutorProfile.userId || '123456789'}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Coaching Experience Card */}
        <div className="mt-8 bg-white border rounded-md shadow-lg p-4 hidden">
          <div className="flex justify-between items-center mb-4">
            <h3 className="font-medium text-amber-800">Coaching experience</h3>
            {!isEditingExperience && (
              <button
                onClick={() => setIsEditingExperience(true)}
                className="text-blue-500 border border-blue-500 rounded p-1"
              >
                <Icon icon="mdi:pencil" className="w-4 h-4" />
              </button>
            )}
          </div>

          {isEditingExperience ? (
            <Formik
              initialValues={{ experience: tutorProfile?.experience || '' }}
              onSubmit={handleExperienceSubmit}
            >
              {({ isSubmitting }) => (
                <Form>
                  <div className="space-y-4">
                    <div>
                      <label
                        htmlFor="experience"
                        className="block text-sm font-medium text-gray-700 mb-1"
                      >
                        Coaching Experience
                      </label>
                      <Field
                        as="textarea"
                        id="experience"
                        name="experience"
                        placeholder="Write your coaching experience here"
                        rows="3"
                        className="w-full p-2 border border-gray-300 rounded-md"
                      />
                    </div>
                    <div className="flex justify-end gap-2">
                      <button
                        type="submit"
                        disabled={isSubmitting}
                        className="px-4 py-2 bg-yellow-500 text-white rounded-md hover:bg-yellow-600"
                      >
                        {isSubmitting ? 'Saving...' : 'Save'}
                      </button>
                      <button
                        type="button"
                        onClick={() => setIsEditingExperience(false)}
                        className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300"
                      >
                        Cancel
                      </button>
                    </div>
                  </div>
                </Form>
              )}
            </Formik>
          ) : (
            <p className="text-sm text-gray-700">
              {tutorProfile.experience ||
                "I've 5 years coaching experience in teaching profession."}
            </p>
          )}
        </div>

        {/* Education Cards */}
        {tutorProfile.education && tutorProfile.education.length > 0 ? (
          <div className="mt-8 bg-white border rounded-md shadow-lg p-4">
            <div className="mb-4">
              <h3 className="font-medium text-amber-800">Education</h3>
            </div>

            {tutorProfile.education.map((edu, index) =>
              renderEducationItem(edu, index)
            )}

            {/* Add New Education Form */}
            {isAddingNew && renderAddEducationForm()}

            {/* Add Education button at the bottom */}
            {!isAddingNew && (
              <div className="mt-4 flex ">
                <button
                  onClick={() => setIsAddingNew(true)}
                  className="flex items-center text-blue-500 border border-blue-500 rounded-md px-4 py-2"
                >
                  <Icon icon="mdi:plus" className="mr-2" />
                  Add Education
                </button>
              </div>
            )}
          </div>
        ) : (
          <div className="mt-8 bg-white border rounded-md shadow-lg p-4">
            <div className="mb-4">
              <h3 className="font-medium text-amber-800">Education</h3>
            </div>
            <p className="text-sm text-gray-700 mb-4">
              No education information available
            </p>

            {/* Add New Education Form */}
            {isAddingNew && renderAddEducationForm()}

            {/* Add Education button at the bottom */}
            {!isAddingNew && (
              <div className="mt-4 flex justify-center">
                <button
                  onClick={() => setIsAddingNew(true)}
                  className="flex items-center text-blue-500 border border-blue-500 rounded-md px-4 py-2"
                >
                  <Icon icon="mdi:plus" className="mr-2" />
                  Add Education
                </button>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default TutorProfile;

// Handle coaching experience update
const handleExperienceSubmit = async (values) => {
  try {
    await api.patch('/tutors/profile', { experience: values.experience });
    setIsEditingExperience(false);
    refetch(); // Refresh the data
  } catch (error) {
    console.error('Error updating coaching experience:', error);
  }
};

// Coaching experience form
const renderExperienceForm = () => {
  return (
    <Formik
      initialValues={{ experience: tutorProfile?.experience || '' }}
      onSubmit={handleExperienceSubmit}
    >
      {({ isSubmitting }) => (
        <Form>
          <div className="space-y-4">
            <div>
              <label
                htmlFor="experience"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Coaching Experience
              </label>
              <Field
                as="textarea"
                id="experience"
                name="experience"
                placeholder="Write your coaching experience here"
                rows="3"
                className="w-full p-2 border border-gray-300 rounded-md"
              />
            </div>
            <div className="flex justify-end gap-2">
              <button
                type="submit"
                disabled={isSubmitting}
                className="px-4 py-2 bg-yellow-500 text-white rounded-md hover:bg-yellow-600"
              >
                {isSubmitting ? 'Saving...' : 'Save'}
              </button>
              <button
                type="button"
                onClick={() => setIsEditingExperience(false)}
                className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300"
              >
                Cancel
              </button>
            </div>
          </div>
        </Form>
      )}
    </Formik>
  );
};
