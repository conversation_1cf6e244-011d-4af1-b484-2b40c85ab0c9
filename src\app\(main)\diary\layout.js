'use client';
import React, { useState, useEffect, useMemo } from 'react';
import { usePathname } from 'next/navigation';
import Sidebar from './_component/Sidebar';
import Topbar from './_component/Topbar';
import PrivateRoute from '@/components/shared/PrivateRoute';
import api from '@/lib/api';
import useDataFetch from '@/hooks/useDataFetch';
import { Icon } from '@iconify/react';
import { ButtonIcon } from '@/components/Button';

const DiaryLayout = ({ children }) => {
  const [searchParams, setSearchParams] = useState({
    subject: '',
    date: '',
  });
  const [isSearching, setIsSearching] = useState(false);
  const [showSidebar, setShowSidebar] = useState(false);

  const [sortOption, setSortOption] = useState('newest');
  const [pageTitle, setPageTitle] = useState("Today's Diary");
  const [layoutBackground, setLayoutBackground] = useState('#FFFFFF');
  const pathname = usePathname();

  // Handle responsive sidebar behavior
  useEffect(() => {
    const handleResize = () => {
      const isDesktop = window.innerWidth >= 1280; // xl breakpoint
      if (isDesktop) {
        setShowSidebar(true); // Always show sidebar on desktop
      } else {
        setShowSidebar(false); // Hide sidebar on mobile/tablet by default
      }
    };

    // Initial check
    handleResize();

    // Add event listener
    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Only fetch background color if we're on the main diary page
  useEffect(() => {
    const fetchBackgroundColor = async () => {
      // Only fetch background color for the main diary page or my diary page
      if (pathname !== '/diary' && pathname !== '/diary/my') return;

      try {
        console.log('Fetching background color for diary layout page');
        const response = await api.get('/diary/entries/today');
        if (
          response.success &&
          response.data &&
          response.data.backgroundColor
        ) {
          console.log(
            'Setting background color to:',
            response.data.backgroundColor
          );
          setLayoutBackground(response.data.backgroundColor);
        }
      } catch (error) {
        console.error('Error fetching background color:', error);
      }
    };

    fetchBackgroundColor();
  }, [pathname]);

  const handleBackgroundChange = (color) => {
    // Only apply background change if we're on the main diary page
    if (pathname !== '/diary' && pathname !== '/diary/my') return;

    // Apply the selected color directly
    if (color) {
      console.log(`Background changed to: ${color}`);
      setLayoutBackground(color);

      // Store the color in localStorage for persistence
      localStorage.setItem('diaryLayoutBackground', color);

      // Make the color available to the WriteDiary component
      window.diaryLayoutBackground = color;

      // Dispatch a custom event that WriteDiary can listen for
      const event = new CustomEvent('layoutBackgroundChanged', {
        detail: color,
      });
      window.dispatchEvent(event);
    }
  };

  // Prepare search query parameters for API call
  const prepareSearchParams = () => {
    const params = {};

    // Add subject parameter if provided
    if (searchParams.subject && searchParams.subject.trim() !== '') {
      params.subject = searchParams.subject;
    }

    // Add date parameter if provided
    if (searchParams.date && searchParams.date.trim() !== '') {
      params.date = searchParams.date;
    }

    return params;
  };

  // Check if we should enable the search query using useMemo
  const shouldEnableSearch = useMemo(() => {
    const hasSubject = Boolean(searchParams.subject?.trim());
    const hasDate = Boolean(searchParams.date?.trim());
    const shouldEnable = isSearching === true && (hasSubject || hasDate);

    // console.log('shouldEnableSearch check:', {
    //   isSearching,
    //   hasSubject,
    //   hasDate,
    //   shouldEnable,
    //   searchParams,
    // });

    return shouldEnable;
  }, [isSearching, searchParams]);

  // Use useDataFetch for search API call
  const {
    data: searchResults,
    isLoading: searchLoading,
    error: searchError,
    refetch: performSearch,
  } = useDataFetch({
    queryKey: ['diary-search', searchParams],
    endPoint: '/diary/entries/search',
    params: prepareSearchParams(),
    enabled: shouldEnableSearch,
  });

  // Handle search
  const handleSearch = (searchData) => {
    console.log('Search initiated with params:', searchData);
    console.log('Current isSearching state:', isSearching);
    console.log('Current searchParams state:', searchParams);

    // Only proceed if we have valid search data
    if (searchData && (searchData.subject || searchData.date)) {
      console.log('Setting search params and enabling search');
      setSearchParams(searchData);
      setIsSearching(true);
    } else {
      console.warn('Invalid search data:', searchData);
    }
  };

  // Log search results for debugging
  useEffect(() => {
    if (searchResults) {
      console.log('Search results received:', searchResults);
    }
    if (searchError) {
      console.error('Search error:', searchError);
    }
  }, [searchResults, searchError]);

  // Clear search results
  const clearSearch = () => {
    setIsSearching(false);
    setSearchParams({ subject: '', date: '' });
  };

  // Handle sort change
  const handleSortChange = (option) => {
    setSortOption(option);
  };

  const getTitle = () => {
    if (pathname === '/diary') {
      return "Today's Diary";
    } else if (pathname === '/diary/my') {
      return 'My Diary';
    } else if (pathname === '/diary/award') {
      return 'Awards';
    } else if (pathname === '/diary/owned-item') {
      return 'Owned Items';
    } else if (pathname.startsWith('/diary/shared')) {
      return 'Shared Diary';
    } else if (pathname === '/diary/mission/attendance') {
      return 'Attendance';
    } else {
      return 'Diary';
    }
  };

  return (
    <div className="min-h-[calc(100vh-200px)]">
      {/* Sidebar */}
      <Sidebar showSidebar={showSidebar} setShowSidebar={setShowSidebar} />

      {/* Main Content */}
      <div
        className="flex flex-col min-h-[calc(100vh-200px)] xl:ml-64" // Add left margin for desktop to account for fixed sidebar
        style={{
          background:
            pathname === '/diary' || pathname === '/diary/my'
              ? layoutBackground
              : '#FFFFFF',
          transition: 'background-color 0.3s',
        }}
      >
        {/* Topbar */}
        <Topbar
          title={getTitle()}
          onSearch={handleSearch}
          onSortChange={handleSortChange}
          onBackgroundChange={
            pathname === '/diary' || pathname === '/diary/my'
              ? handleBackgroundChange
              : null
          }
          searchResults={searchResults}
          searchLoading={searchLoading}
          isSearching={isSearching}
          onClearSearch={clearSearch}
          showSidebar={showSidebar}
          setShowSidebar={setShowSidebar}
        />

        {/* Content */}
        <div>
          <div className="p-4 pb-0 xl:hidden">
            <ButtonIcon
              icon={'gg:menu-left'}
              innerBtnCls="h-10 w-10"
              btnIconCls="h-3 w-3"
              aria-label="show sidebar"
              onClick={() => setShowSidebar(!showSidebar)}
            />
          </div>
          <div className="flex-1">{children}</div>
        </div>
      </div>
    </div>
  );
};

export default DiaryLayout;
