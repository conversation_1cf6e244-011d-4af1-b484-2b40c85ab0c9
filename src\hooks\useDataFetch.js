import api from '../lib/api';
import { useQuery } from '@tanstack/react-query';

// Updated useDataFetch hook
const useDataFetch = ({
  queryKey,
  endPoint,
  params = {},
  enabled = true,
  method = 'GET',
  apiConfig = {} // New parameter for API configuration options
}) => {
  return useQuery({
    queryKey: [queryKey, params],
    queryFn: async () => {
      const { id, ...otherParams } = params;

      try {
        const response = method === 'POST'
          ? await api.post(endPoint, otherParams, apiConfig)
          : id
            ? await api.get(`${endPoint}/${id}`, apiConfig)
            : await api.get(endPoint, { params: otherParams, ...apiConfig });

        return response?.data;
      } catch (error) {
        throw new Error(
          error?.response?.data?.message || 'Data fetching error'
        );
      }
    },
    enabled,
  });
};

export default useDataFetch;
