'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import HecNovelLayout from '../_components/HecNovelLayout';
import NovelCanvas from '../_components/NovelCanvas';
import NovelReviewSection from '../_components/NovelReviewSection';

import { toast } from 'sonner';
import api from '@/lib/api';
import Image from 'next/image';
import { Icon } from '@iconify/react';

const NovelEntryView = () => {
  const [entryData, setEntryData] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isError, setIsError] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');

  const searchParams = useSearchParams();
  const tabParam = searchParams.get('tab');
  const [activeTab, setActiveTab] = useState(tabParam || 'NovelSubmissionList');

  const router = useRouter();
  const params = useParams();
  const entryId = params.id;

  // Handle back navigation
  const handleBack = () => {
    router.back();
  };

  // Fetch novel entry details
  const fetchNovelEntry = async () => {
    try {
      setIsLoading(true);
      const endpoint = `/tutor/novel/entries/${entryId}`;

      console.log(`Fetching from: ${endpoint}`);

      const response = await api.get(endpoint);
      console.log('API response:', response);

      // Fixed: Check for success at root level and handle response.data structure
      if (response?.success && response.data) {
        setEntryData(response.data);
        setIsError(false);
        setErrorMessage('');
      } else if (response?.data?.success && response.data.data) {
        // Fallback for nested structure
        setEntryData(response.data.data);
        setIsError(false);
        setErrorMessage('');
      } else {
        setIsError(true);
        setErrorMessage('Failed to load novel entry details');
      }
    } catch (err) {
      console.error('Error fetching novel entry:', err);
      setIsError(true);
      setErrorMessage(
        err.message || 'An error occurred while fetching novel entry details'
      );
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (entryId) {
      fetchNovelEntry();
    }
  }, [entryId]);

  if (isLoading) {
    return (
      <div className="container mx-auto p-4">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="container mx-auto p-4">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
          <h2 className="text-lg font-semibold text-red-800 mb-2">
            Error Loading Entry
          </h2>
          <p className="text-red-600 mb-4">{errorMessage}</p>
          <button
            onClick={handleBack}
            className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  if (!entryData) {
    return (
      <div className="container mx-auto p-4">
        <div className="text-center py-8">
          <p className="text-gray-600">No entry data found.</p>
          <button
            onClick={handleBack}
            className="mt-4 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  return (
    <HecNovelLayout activeTab={activeTab}>
      <div className="bg-white min-h-screen w-full">
        {/* User info and date section */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 mb-2">
          <div className="flex flex-col sm:flex-row items-center gap-3">
            <span className="text-sm font-normal">Submitted by:</span>
            <div className="flex items-center space-x-2">
              <div className="w-6 h-6 rounded-full bg-amber-100 flex items-center justify-center text-amber-800 overflow-hidden">
                {/* User Avatar Circle */}
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <span className="text-xs text-gray-700">
                {entryData.studentName}
              </span>
            </div>
          </div>

          <div className="flex flex-col items-start sm:items-center text-xs text-gray-700 space-y-1">
            {/* <span className="text-sm font-normal">Date:</span> */}
            <div className="flex items-center space-x-2">
              <div className="text-yellow-500 bg-yellow-50 rounded-full p-1 flex items-center justify-center">
                {/* Calendar icon */}
                <svg
                  className="h-4 w-4"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    fillRule="evenodd"
                    d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              {/* <span>{entryData.topic?.createdAt}</span> */}

              <span className="text-xs">
                Date:{' '}
                {entryData.topic?.createdAt
                  ? new Date(entryData.topic?.createdAt).toLocaleDateString(
                      'en-US',
                      {
                        day: 'numeric',
                        month: 'short',
                        year: '2-digit',
                      }
                    )
                  : 'Unknown'}
              </span>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 items-center bg-[#FDE7E9] gap-2 p-1 shadow-xl">
          {/* Diary Canvas */}
          <div className="bg-white h-full flex items-center justify-center p-2 overflow-hidden shadow-xl">
            <div className="w-full h-[500px] flex items-center justify-center overflow-hidden">
              <div
                className="canvas-container-wrapper"
                style={{ width: '100%', height: '100%', padding: '20px' }}
              >
                <NovelCanvas data={entryData} />
              </div>
            </div>
          </div>

          {/* Review Section */}
          <NovelReviewSection data={entryData} entryId={entryId} />
        </div>
      </div>
    </HecNovelLayout>
  );
};

export default NovelEntryView;
