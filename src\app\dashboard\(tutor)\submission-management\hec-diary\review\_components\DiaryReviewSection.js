'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import ContentEditable from 'react-contenteditable';
import api from '@/lib/api';
import { formatDate } from '@/utils/dateFormatter';
import FeedbackModal from '@/components/FeedbackModal';
import SimpleTiptapEditor from '@/components/form/SimpleTiptapEditor';
import HistoryModal from '@/app/(main)/novel/_components/HistoryModal';

const isHtmlEmpty = (html) => {
  if (!html) return true;
  const strippedHtml = html.replace(/<[^>]*>?/gm, '').trim();
  return strippedHtml.length === 0;
};

const DiaryReviewSection = ({ data, entryId, refetch }) => {
  const router = useRouter();
  const [correctionHtml, setCorrectionHtml] = useState(
    data?.correction?.correctionText || data?.content || ''
  );
  const [score, setScore] = useState('');
  const [isFeedbackModalOpen, setIsFeedbackModalOpen] = useState(false);
  const [isSubmittingReview, setIsSubmittingReview] = useState(false);
  const [isCompletingReview, setIsCompletingReview] = useState(false);
  const [showHistoryModal, setShowHistoryModal] = useState(false);
  const editorRef = useRef(null);
  const isCorrectionReviewed = !!data?.correction?.correctionText;

  useEffect(() => {
    setCorrectionHtml(data?.correction?.correctionText || data?.content || '');
  }, [data]);

  useEffect(() => {
    if (data?.correction?.score && !score) {
      setScore(data.correction.score.toString());
    }
  }, [data?.correction?.score, score]);

  const handleCorrectionChange = (evt) => {
    setCorrectionHtml(evt.target.value);
  };

  const prepareToTypeBlue = () => {
    document.execCommand('foreColor', false, 'blue');
  };

  const submitReview = async () => {
    try {
      setIsSubmittingReview(true);
      const scoreToSubmit = score || data?.correction?.score;
      const response = await api.post(
        `/tutor/diary/entries/${entryId}/correction`,
        {
          correctionText: correctionHtml,
          score: parseInt(scoreToSubmit),
        }
      );
      if (response.success) {
        router.push('/dashboard/submission-management/hec-diary');
      } else {
        throw new Error(response.message);
      }
    } catch (err) {
      console.log(err.message || 'Failed to submit review');
    } finally {
      setIsSubmittingReview(false);
    }
  };

  const completeReview = async () => {
    try {
      setIsCompletingReview(true);
      const response = await api.post(
        `/tutor/diary/entries/${entryId}/complete-review`
      );
      if (response.success) {
        router.push('/dashboard/submission-management/hec-diary');
      } else {
        throw new Error(response.message);
      }
    } catch (err) {
      console.log(err.message || 'Failed to complete review');
    } finally {
      setIsCompletingReview(false);
    }
  };

  return (
    <>
      <div className="p-2 shadow-xl h-full bg-white">
        <div className="mb-4 rounded-md shadow-lg p-3">
          <div className="flex justify-between items-center border-b-2 border-dashed border-gray-300 mb-3">
            <h3 className="text-lg font-semibold mb-2">Original Content</h3>
            <div className="flex items-center gap-3 text-sm">
              Date: {formatDate(data.entryDate, 'ordinal')}
            </div>
          </div>
          <div
            dangerouslySetInnerHTML={{
              __html: data?.originalReviewedVersion?.content || data?.content,
            }}
            className="whitespace-pre-wrap text-sm text-[#314158] h-[204px] overflow-y-auto"
          ></div>
          <div className="flex justify-end ">
            <button
              className="bg-[#FFF9E6] border border-[#D4A574] text-[#8B4513] hover:bg-[#FFF5D6] hover:border-[#C19A5B] text-xs font-medium px-4 py-1 rounded-full transition-colors duration-200 shadow-[0px_4px_6px_-1px_rgba(0,0,0,0.1),0px_2px_4px_-2px_rgba(0,0,0,0.1),-4px_8px_12px_0px_#00000026] "
              onClick={() => setShowHistoryModal(true)}
              aria-label="View History"
            >
              View History
            </button>
          </div>
        </div>

        <div className="overflow-auto shadow-lg p-4 border rounded-md">
          <p className="text-sm text-[#864D0D] text-center font-medium">
            Tutor Review Zone
          </p>
          <div className="flex justify-between items-center border-b-2 border-dashed border-gray-300 mb-3">
            <h3 className="text-lg font-semibold mb-2">Make Corrections</h3>
            <div className="flex items-center gap-3 text-sm">
              Date: {formatDate(data.entryDate, 'ordinal')}
            </div>
          </div>

          <div className="mb-4">
            {data?.content && data?.isResubmission ? (
              <p className="h-[200px] overflow-y-auto">{data?.content}</p>
            ) : (
              <SimpleTiptapEditor
                editorRef={editorRef}
                initialValue={correctionHtml}
                setValue={setCorrectionHtml}
                height={200}
              />
            )}

            {data?.content && data?.isResubmission && (
              <p className="text-green-500 text-center">
                {data?.isResubmission && 'Reviewed'}
              </p>
            )}
          </div>

          <div className="flex flex-wrap items-center justify-between gap-4">
            <div className="flex flex-wrap items-center gap-3">
              <button
                type="button"
                onClick={() => setIsFeedbackModalOpen(true)}
                className="px-4 py-1 bg-[#FEFCE8] text-base text-[#723F11] rounded-md border border-[#723F11] font-medium hover:bg-[#FFF8D6] flex-shrink-0"
              >
                Give feedback
              </button>
              <div className="flex flex-wrap items-center border border-[#723F11] rounded-md overflow-hidden">
                <label className="px-3 py-1 bg-[#FEFCE8] text-[#723F11] text-base font-medium">
                  Score
                </label>
                {isCorrectionReviewed ? (
                  <div className="px-3 py-1 text-gray-700 bg-white min-w-0">
                    {data.correction.score}
                  </div>
                ) : (
                  <input
                    type="number"
                    value={score}
                    onChange={(e) => setScore(e.target.value)}
                    className="w-16 px-3 py-1 focus:outline-none text-gray-700 min-w-0"
                    min="0"
                    max="100"
                    placeholder="---"
                  />
                )}
              </div>
            </div>

            {data?.status === 'submit' &&
              !data?.isResubmission &&
              data?.status !== 'reviewed' && (
                <button
                  type="button"
                  onClick={submitReview}
                  disabled={
                    isHtmlEmpty(correctionHtml) ||
                    (!score && !data?.correction?.score) ||
                    isSubmittingReview
                  }
                  className="px-4 py-2 bg-yellow-500 text-white rounded-md hover:bg-yellow-600 disabled:bg-gray-300 disabled:cursor-not-allowed flex-shrink-0"
                >
                  {isSubmittingReview ? 'Submitting...' : 'Submit'}
                </button>
              )}
          </div>
        </div>
      </div>

      {isFeedbackModalOpen && entryId && (
        <FeedbackModal
          isOpen={isFeedbackModalOpen}
          onClose={() => setIsFeedbackModalOpen(false)}
          studentsSubmission={data?.content}
          getEndpoint={`/tutor/diary/entries/${entryId}/feedbacks`}
          onSubmit={async (feedback) => {
            const response = await api.post(
              `/tutor/diary/entries/${entryId}/feedback`,
              { feedback }
            );
            if (!response.success) {
              throw new Error(response.message || 'Failed to submit feedback');
            }
          }}
          title="Teachers Feedback"
          placeholder="Write here"
          submitButtonText="Submit"
          submitButtonColor="bg-yellow-500 hover:bg-yellow-600"
        />
      )}

      {showHistoryModal && data?.id && (
        <HistoryModal
          isOpen={showHistoryModal}
          onClose={() => setShowHistoryModal(false)}
          endPoint={`/tutor/diary/entries/${data?.id}/history`}
          mainComRefetch={refetch}
          moduleKey="diary"
        />
      )}
    </>
  );
};

export default DiaryReviewSection;
