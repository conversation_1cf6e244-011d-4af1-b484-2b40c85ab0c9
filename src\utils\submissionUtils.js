
/**
 * Get the last submission content from response data
 * @param {Object} responseData - The response data from API
 * @returns {string} - The last submission content or empty string
 */

export const getLastSubmissionContent = (responseData) => {
  if (!responseData) return '';

  // Try to get content from submissions array (taskId response structure)
  if (responseData?.submission) {
    const submissionContent = responseData?.submission?.submissionHistory?.content

    if (submissionContent) return submissionContent;
  }

  // Fallback to direct submissionHistory content (activeTask response structure)
  if (responseData?.submissionHistory?.content) {
    return responseData.submissionHistory.content;
  }

  return '';
};





/**
 * Get truncated submission content for display
 * @param {Object} responseData - The response data from API
 * @param {number} maxLength - Maximum length before truncation (default: 400)
 * @returns {string} - Truncated content with ellipsis if needed
 */

export const getTruncatedSubmissionContent = (responseData, maxLength = 400) => {
  const content = getLastSubmissionContent(responseData);
  
  if (!content) return '';
  
  if (content.length > maxLength) {
    return content.slice(0, maxLength) + '...';
  }
  
  return content;
};





/**
 * Check if there is existing submission content
 * @param {Object} responseData - The response data from API
 * @returns {boolean} - True if there is existing submission content
 */

export const hasExistingSubmission = (responseData) => {
  const content = getLastSubmissionContent(responseData);
  return content.length > 0;
};




/**
 * Count words in HTML content
 * @param {string} html - HTML content to count words from
 * @returns {number} - Number of words
 */

export const countWords = (html) => {
  if (!html) return 0;
  // Remove HTML tags
  const text = html?.replace(/<[^>]*>/g, ' ');
  // Remove entities
  const cleanText = text.replace(/&nbsp;|&amp;|&lt;|&gt;|&quot;|&#39;/g, ' ');
  // Remove extra spaces and split by whitespace
  const words = cleanText
    .trim()
    .split(/\s+/)
    .filter((word) => word.length > 0);
  return words.length;
};
