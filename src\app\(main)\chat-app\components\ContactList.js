'use client';

import React, { useEffect } from 'react';
import Image from 'next/image';
import { useDispatch, useSelector } from 'react-redux';
import { useRouter, useSearchParams } from 'next/navigation';
import {
  selectFilteredContacts,
  selectConversationId,
  selectIsLoadingContacts,
  selectContacts,
  setActiveContact,
  setConversationId,
} from '@/store/features/chatSlice';
import SearchBar from './SearchBar';

const ContactList = ({ onContactSelect, showChat, isMobile }) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const searchParams = useSearchParams();

  const contacts = useSelector(selectFilteredContacts);
  const allContacts = useSelector(selectContacts);
  const currentConversationId = useSelector(selectConversationId);
  const isLoading = useSelector(selectIsLoadingContacts);

  // Handle URL conversation ID parameter
  useEffect(() => {
    if (!isMobile) {
      const urlConversationId = searchParams.get('conversationId');
      if (
        urlConversationId &&
        urlConversationId !== currentConversationId &&
        allContacts.length > 0
      ) {
        const contact = allContacts.find(
          (c) => c.conversationId === urlConversationId
        );
        if (contact && onContactSelect) {
          onContactSelect(contact);
        }
      }
    }
  }, [
    searchParams,
    currentConversationId,
    allContacts,
    onContactSelect,
    isMobile,
  ]);

  const handleContactClick = (contact) => {
    // Update Redux state
    // console.log('changing from contact list');
    // dispatch(setActiveContact(contact));
    // dispatch(setConversationId(contact.conversationId));
    
    // Update URL with conversation ID
    const params = new URLSearchParams(searchParams);
    params.set('conversationId', contact.conversationId);
    router.push(`/chat-app?${params.toString()}`, { scroll: false });

    // Call parent callback if provided
    if (onContactSelect) {
      onContactSelect(contact);
    }
  };

  const formatTime = (timestamp) => {
    if (!timestamp) return '';

    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now - date) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false,
      });
    } else if (diffInHours < 168) {
      // Less than a week
      return date.toLocaleDateString('en-US', { weekday: 'short' });
    } else {
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
      });
    }
  };

  if (isLoading) {
    return (
      <aside
        className={`
        w-full sm:w-[270px] bg-white border-r border-gray-200 flex items-center justify-center
        ${showChat ? 'hidden sm:flex' : 'flex'}
      `}
      >
        <div className="text-gray-500 text-sm">Loading contacts...</div>
      </aside>
    );
  }

  return (
    <section
      className={`
        w-full sm:w-[270px] bg-white border-r border-gray-200 relative flex flex-col overflow-y-auto 
        ${showChat ? 'hidden sm:flex' : 'flex'}
      `}
    >
      <div className="sticky bg-white z-10 left-0 right-0 top-0">
        <SearchBar />
      </div>

      <div style={{ flex: 1 }}>
        {contacts.length === 0 ? (
          <div className="p-5 text-center text-gray-500 text-sm">
            No contacts found
          </div>
        ) : (
          contacts.map((contact) => (
            <div
              key={contact.conversationId}
              onClick={() => handleContactClick(contact)}
              className={`
                p-3.5 cursor-pointer border-b border-gray-100 transition-colors duration-200
                ${
                  contact.conversationId === currentConversationId
                    ? 'bg-slate-100'
                    : 'hover:bg-gray-50'
                }
              `}
            >
              <div className="flex justify-between items-start">
                <div className="flex items-center gap-2 flex-1 min-w-0">
                  <div className="w-8 h-8 rounded-full overflow-hidden">
                    <div
                      className="w-full h-full bg-cover bg-center bg-no-repeat rounded-full relative"
                      style={{
                        backgroundImage: `url(${
                          contact?.id === 'virtual-admin'
                            ? '/assets/images/all-img/Logo.png'
                            : contact.profilePicture ||
                              '/assets/images/all-img/avatar.png'
                        })`,
                      }}
                    >
                      <Image
                        src={
                          contact.profilePicture ||
                          '/assets/images/all-img/avatar.png'
                        }
                        alt={contact.name}
                        width={32}
                        height={32}
                        className="object-cover absolute top-0 left-0 opacity-0"
                        onError={(e) => {
                          e.target.style.opacity = 0;
                          e.target.parentElement.style.backgroundImage = `url('/assets/images/all-img/avatar.png')`;
                        }}
                      />
                    </div>
                  </div>
                  <div className="flex-1 min-w-0">
                    <div
                      className={`
                      text-sm text-gray-900 mb-1 overflow-hidden text-ellipsis whitespace-nowrap
                      ${contact.unreadCount > 0 ? 'font-bold' : 'font-medium'}
                    `}
                    >
                      {contact.name}
                    </div>
                    <div className="text-xs text-gray-500 overflow-hidden text-ellipsis whitespace-nowrap">
                      {contact.lastMessage || 'No messages yet'}
                    </div>
                  </div>
                  {contact.unreadCount > 0 && (
                    <div
                      style={{
                        backgroundColor: '#ef4444',
                        color: 'white',
                        borderRadius: '50%',
                        width: '20px',
                        height: '20px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        fontSize: '11px',
                        fontWeight: 'bold',
                        marginLeft: '8px',
                        flexShrink: 0,
                      }}
                    >
                      {contact.unreadCount > 9 ? '9+' : contact.unreadCount}
                    </div>
                  )}
                </div>
                {contact.lastMessageTime && (
                  <div
                    style={{
                      fontSize: '11px',
                      color: '#9ca3af',
                      marginLeft: '8px',
                      flexShrink: 0,
                    }}
                  >
                    {formatTime(contact.lastMessageTime)}
                  </div>
                )}
              </div>
            </div>
          ))
        )}
      </div>
    </section>
  );
};

export default ContactList;
