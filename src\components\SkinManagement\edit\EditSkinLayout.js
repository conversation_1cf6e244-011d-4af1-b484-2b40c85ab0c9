'use client';
import { useState } from 'react';
import Topbar from './Topbar';
import Sidebar from '../_components/Sidebar';
import { usePathname } from 'next/navigation';

export default function EditSkinLayout({ children }) {
  const pathname = usePathname();
  const [isLoading] = useState(false);
  const [error, setError] = useState(null);
  const isAdmin = pathname.split('/').includes('dashboard');

  return (
    <div className="min-h-screen bg-[#FFFDF5] overflow-hidden">
      {/* Topbar - Full width */}
      <div className="asbsolute top-26 left-72 right-0 text-white shadow-md z-30">
        <Topbar isAdmin={isAdmin} />
      </div>

      <div className="flex h-screen">
        {/* Left Sidebar */}
        <div className="asbsolute left-0 top-0 bottom-0 w-72 bg-[#FFFAC2] border-r border-gray-300 overflow-hidden">
          <Sidebar isAdmin={isAdmin} />
        </div>

        {/* Main Content Area with margin for sidebar */}
        <div className="ml-50 flex-1 overflow-y-auto p-4">
          {/* Loading overlay */}
          {isLoading && (
            <div className="asbsolute inset-0 bg-white bg-opacity-70 z-50 flex items-center justify-center">
              <div className="bg-white p-6 rounded-lg shadow-lg text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#8B4513] mx-auto mb-4"></div>
                <p className="text-[#8B4513] font-medium">Loading...</p>
              </div>
            </div>
          )}

          {/* Error message */}
          {error && (
            <div className="asbsolute inset-0 bg-white bg-opacity-70 z-50 flex items-center justify-center">
              <div className="bg-white p-6 rounded-lg shadow-lg text-center max-w-md">
                <div className="text-red-500 text-5xl mb-4">⚠️</div>
                <h3 className="text-xl font-bold text-red-600 mb-2">Error</h3>
                <p className="text-gray-700 mb-4">{error}</p>
                <button
                  onClick={() => setError(null)}
                  className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
                >
                  Dismiss
                </button>
              </div>
            </div>
          )}

          {/* Main content */}
          {children}
        </div>
      </div>
    </div>
  );
}
