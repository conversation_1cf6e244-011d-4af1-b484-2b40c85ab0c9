'use client';
import { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Icon } from '@iconify/react';
import {
  adminSidebarData,
  tutorSidebarData,
  userSidebarData,
} from '@/data/MenuItems';
import { getUserRoleFromCookie } from '@/lib/auth';
import { motion } from 'framer-motion';

const Sidebar = ({ isOpen, toggleSidebar, isMobile }) => {
  const pathname = usePathname();
  const [expandedItems, setExpandedItems] = useState({});
  const [userRole, setUserRole] = useState(null);

  // Get user role from cookie on component mount
  useEffect(() => {
    const role = getUserRoleFromCookie();
    setUserRole(role);
  }, []);

  const menus =
    userRole === 'admin'
      ? adminSidebarData
      : userRole === 'tutor'
      ? tutorSidebarData
      : userSidebarData;

  // Check if any submenu item is active
  const isSubmenuActive = (children) => {
    return children?.some(
      (child) =>
        pathname === child.path || pathname.startsWith(`${child.path}/`)
    );
  };

  const toggleExpand = (index) => {
    setExpandedItems((prev) => {
      const newExpandedItems = {};
      newExpandedItems[index] = !prev[index];
      return newExpandedItems;
    });
  };

  return (
    <div
      className={`fixed left-0 top-0 h-full bg-[#FEFCE8] transition-all duration-300 rounded-r-xl z-40
        ${
          isMobile
            ? `${isOpen ? 'translate-x-0' : '-translate-x-full'} w-64`
            : `${isOpen ? 'w-72' : 'w-20'}`
        } border-r`}
    >
      {/* Logo */}
      <div className="h-16 flex items-center px-4 justify-between">
        {isOpen && (
          <>
            <Link
              href={'/'}
              className="text-lg text-center font-semibold pl-4 text-yellow-600"
            >
              Hello English Coaching
            </Link>
          </>
        )}
        {!isMobile && (
          // Toggle button for desktop only
          <button
            onClick={toggleSidebar}
            className={`p-2 rounded-lg hover:bg-gray-100 ${
              !isOpen && 'mx-auto'
            }`}
            aria-label="Toggle Sidebar"
          >
            <Icon
              icon={
                isOpen ? 'material-symbols:menu-open' : 'material-symbols:menu'
              }
              className="w-6 h-6"
            />
          </button>
        )}
      </div>

      {/* Navigation Items */}
      <nav className="mt-4 overflow-y-auto h-[calc(100vh-4rem)] px-4 space-y-1">
        {menus?.map((item, index) => (
          <div key={index}>
            {item.children?.length > 0 ? (
              // If item has children, render as button
              <button
                className={`w-full flex items-center py-3 cursor-pointer hover:bg-yellow-200 transition-colors rounded-lg
                  ${isSubmenuActive(item.children) ? 'bg-yellow-300' : ''}
                  ${isOpen ? 'justify-start px-4' : 'justify-center px-1'}`}
                onClick={() => toggleExpand(index)}
              >
                <Icon icon={item.icon} className="w-5 h-5" />
                {isOpen && (
                  <>
                    <span className="ml-3 text-sm font-medium">
                      {item.title}
                    </span>
                    <Icon
                      icon={
                        expandedItems[index]
                          ? 'lucide:chevron-down'
                          : 'lucide:chevron-right'
                      }
                      className="ml-auto w-5 h-5"
                    />
                  </>
                )}
              </button>
            ) : (
              // If item has no children, render as Link
              <Link
                href={item.path}
                className={`flex items-center py-3 cursor-pointer hover:bg-yellow-200 transition-colors rounded-lg
                  ${pathname === item.path ? 'bg-yellow-300' : ''}
                  ${isOpen ? 'justify-start px-4' : 'justify-center px-1'}`}
                onClick={() => isMobile && toggleSidebar()}
              >
                <Icon icon={item.icon} className="w-5 h-5" />
                {isOpen && (
                  <span className="ml-3 text-sm font-medium">{item.title}</span>
                )}
              </Link>
            )}

            {/* Submenu */}
            {isOpen && item.children?.length > 0 && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{
                  opacity: expandedItems[index] ? 1 : 0,
                  height: expandedItems[index] ? 'auto' : 0,
                }}
                transition={{
                  duration: 0.3,
                  ease: 'easeInOut',
                }}
                className="bg-gray-50 px-4 rounded-lg overflow-hidden"
              >
                {item.children.map((child, childIndex) => (
                  <motion.div
                    key={childIndex}
                    initial={{ x: -20, opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    transition={{
                      duration: 0.2,
                      delay: childIndex * 0.05,
                    }}
                  >
                    <Link
                      href={child.path}
                      className={`block py-2 text-sm hover:text-yellow-600
                        ${
                          pathname.startsWith(`${child.path}/`) ||
                          pathname === child.path
                            ? 'text-yellow-700 font-medium'
                            : 'text-gray-700'
                        }`}
                      onClick={() => isMobile && toggleSidebar()}
                    >
                      <Icon icon={'ei:plus'} className="w-4 h-4 inline mr-1" />
                      {child.title}
                    </Link>
                  </motion.div>
                ))}
              </motion.div>
            )}
          </div>
        ))}
      </nav>
    </div>
  );
};

export default Sidebar;
