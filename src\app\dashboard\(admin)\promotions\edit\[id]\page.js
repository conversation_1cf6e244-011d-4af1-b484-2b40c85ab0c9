'use client';
import DateTimePicker from '@/components/form/DateTimePicker';
import FormInput from '@/components/form/FormInput';
import FormRadio from '@/components/form/FormRadio';
import FormSelect from '@/components/form/FormSelect';
import NumberInput from '@/components/form/NumberInput';
import RegularGoBack from '@/components/shared/RegularGoBack';
import useDataFetch from '@/hooks/useDataFetch';
import api from '@/lib/api';
import { Form, Formik } from 'formik';
import { useParams, useRouter } from 'next/navigation';
import React from 'react';
import * as Yup from 'yup';
import MultiSelectComponent from '@/components/form/MultiSelectComponent';

const validationSchema = Yup.object().shape({
  name: Yup.string().required('Promotion name is required'),
  discountType: Yup.string().required('Discount type is required'),
  discountValue: Yup.number().required('Discount value is required'),
  applicableType: Yup.string().required('Please select applicable type'),
  applicableCategoryIds: Yup.array()
    .min(1, 'Please select at least one category')
    .required('Please select applicable category'),
  promotionCode: Yup.string().required('Promotion code is required'),
  startDate: Yup.date().required('Start date is required').min(new Date(), 'Start date cannot be in the past'),
  endDate: Yup.date()
    .required('End date is required')
    .min(Yup.ref('startDate'), 'End date cannot be before start date'),
  isActive: Yup.boolean().required('Active status is required'),
  usageLimit: Yup.number().min(1, 'Limit should not be less than 1'),
  minimumPurchaseAmount: Yup.number(),
  maximumDiscountAmount: Yup.number(),
  promotionId: Yup.string().matches(
    /^[0-9a-f]{8}-[0-9a-f]{4}-[4][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i,
    'Must be a valid UUID'
  ),
});

const discountTypeOptions = [
  { value: 'fixed_amount', label: 'Fixed amount' },
  { value: 'percentage', label: 'Percentage amount' },
];

const activeStatusOptions = [
  { label: 'Yes', value: true },
  { label: 'No', value: false },
];

const EditPromotion = () => {
  const { id } = useParams();
  const router = useRouter();

  const { data: promotionDetails } = useDataFetch({
    queryKey: 'promotion-details',
    endPoint: `/promotions/admin/${id}`,
  });

  const { data: categories, isLoading } = useDataFetch({
    queryKey: 'categories',
    endPoint: '/admin/shop/categories',
  });

  const { data: plans } = useDataFetch({
    queryKey: 'plans',
    endPoint: '/plans',
  });

  const handleSubmit = async (values, { setSubmitting, setFieldError }) => {
    try {
      const response = await api.patch(`/promotions/${id}`, values);
      router.push('/dashboard/promotions');
    } catch (error) {
      console.log(error);
      if (error?.response?.data?.validationErrors) {
        const validationErrors = error.response.data.validationErrors;
        Object.keys(validationErrors).forEach((field) => {
          setFieldError(`${field}`, validationErrors[field][0]);
        });
      }
    } finally {
      setSubmitting(false);
    }
  };
  console.log(promotionDetails);
  return (
    <div className="">
      <RegularGoBack className={'pb-5 max-w-32'} title={'Promotions'} />

      <Formik
        enableReinitialize={true}
        initialValues={{
          name: promotionDetails?.name || '',
          description: promotionDetails?.description || '',
          discountType: promotionDetails?.discountType || '',
          discountValue: promotionDetails?.discountValue || '',
          applicableType: 'shop_item',
          applicableCategoryIds: promotionDetails?.applicableCategoryIds || [],
          applicablePlanIds: promotionDetails?.applicablePlanIds || [],
          promotionCode: promotionDetails?.promotionCode || '',
          startDate: promotionDetails?.startDate || '',
          endDate: promotionDetails?.endDate || '',
          isActive: promotionDetails?.isActive || true,
          usageLimit: promotionDetails?.usageLimit || '',
          minimumPurchaseAmount: promotionDetails?.minimumPurchaseAmount || '',
          maximumDiscountAmount: promotionDetails?.maximumDiscountAmount || '',
        }}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
      >
        {({ isSubmitting, values, setFieldValue, errors }) => (
          <Form className="space-y-6 bg-gray-50 border p-5 rounded-xl">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <FormInput
                label="Promotion Name"
                name="name"
                placeholder="Write here"
                required
              />

              <FormSelect
                label="Discount Type"
                name="discountType"
                options={discountTypeOptions}
                // multiple={false}
                placeholder="Select discount type"
                required
              />

              <NumberInput
                label="Discount Value"
                name="discountValue"
                placeholder="Write here"
                required
              />

              <MultiSelectComponent
                label="Discount Category"
                name="applicableCategoryIds"
                options={categories?.items || []}
                valueKey="id"
                labelKey="name"
                placeholder="Select discount category (Multi selection)"
                required
              />

              <MultiSelectComponent
                label="Applied to Plan"
                name="applicablePlanIds"
                options={plans?.items || []}
                valueKey="id"
                labelKey="name"
                placeholder="Select discount category (Multi selection)"
              />

              <FormInput
                label="Promotion Code"
                name="promotionCode"
                placeholder="Write here"
                required
              />

              <DateTimePicker
                label="Start Date"
                name="startDate"
                minuteDiffarent={10}
                required
              />

              <DateTimePicker
                label="End Date"
                name="endDate"
                minuteDiffarent={10}
                required
              />

              <div className="flex items-center">
                <FormRadio
                  label="Active Status"
                  name="isActive"
                  options={activeStatusOptions}
                  isHorizontal={true}
                  required
                />
              </div>

              <FormInput
                label="Usage Limit"
                name="usageLimit"
                type="number"
                placeholder="Write here"
                required
              />

              <NumberInput
                label="Minimum Purchase Amount"
                name="minimumPurchaseAmount"
                type="number"
                placeholder="Write here"
              />

              <NumberInput
                label="Maximum Purchase Amount"
                name="maximumDiscountAmount"
                type="number"
                placeholder="Write here"
              />
            </div>

            <FormInput
              label="Descriptions"
              name="description"
              isTextarea={true}
              placeholder="Write promotion descriptions"
              required
            />

            <div className="flex justify-end">
              <button
                type="submit"
                disabled={isSubmitting}
                className="px-6 py-2 bg-yellow-400 rounded-lg hover:bg-yellow-500 transition-colors"
              >
                {isSubmitting ? 'Updating...' : 'Update'}
              </button>
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default EditPromotion;
