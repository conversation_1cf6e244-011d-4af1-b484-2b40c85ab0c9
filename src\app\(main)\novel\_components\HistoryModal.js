import { ButtonIcon } from '@/components/Button';
import Tooltip from '@/components/Tooltip';
import useDataFetch from '@/hooks/useDataFetch';
import api from '@/lib/api';
import { formatDate } from '@/utils/dateFormatter';
import { Icon } from '@iconify/react';
import { motion, AnimatePresence } from 'framer-motion';
import React, { useState } from 'react';
import { useSelector } from 'react-redux';

const HistoryModal = ({
  isOpen,
  onClose,
  endPoint,
  mainComRefetch,
  moduleKey,
}) => {
  const [restoringVersionId, setRestoringVersionId] = useState(null);
  const {user} = useSelector((state) => state.auth);
  const isTutor = user?.type === 'tutor';

  if (!isOpen) return null;

  const {
    data: historyData,
    isLoading,
    error,
    refetch,
  } = useDataFetch({
    queryKey: [endPoint.split('/').join('-'), endPoint],
    endPoint: endPoint,
  });

  const handleRestoreVersion = async (versionId) => {
    if (
      !(
        historyData?.entryId ||
        historyData?.diaryEntryId ||
        historyData?.missionEntryId
      ) ||
      !versionId ||
      !mainComRefetch
    )
      return;

    setRestoringVersionId(versionId);
    const RESTORE_ENDPOINTS = {
      novel: `/student/novel/entries/${historyData?.entryId}/versions/${versionId}/restore`,
      diary: `/diary/entries/${historyData?.diaryEntryId}/versions/${versionId}/restore`,
      diary_mission: `/diary/missions/entries/${historyData?.missionEntryId}/versions/${versionId}/restore`,
    };

    const url = RESTORE_ENDPOINTS[moduleKey];

    try {
      await api.put(url);

      // Refetch the history data to get updated versions
      await refetch();
      await mainComRefetch();

      // Optional: Show success message or close modal
      // You might want to add a toast notification here
      console.log('Version restored successfully');
    } catch (error) {
      console.error('Error restoring version:', error);
    } finally {
      setRestoringVersionId(null);
    }
  };

  return (
    <AnimatePresence>
      <motion.div
        className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
      >
        <motion.div
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          transition={{ type: 'spring', damping: 20, stiffness: 300 }}
          className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[80vh] flex flex-col"
        >
          <div className="flex justify-between  items-center border-b p-4 py-2">
            <h2 className="text-xl font-semibold">Edited History</h2>
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700"
            >
              <ButtonIcon
                icon="ic:round-close"
                innerBtnCls={'h-10 w-10 text-gray-800'}
              />
            </button>
          </div>

          <div className="p-4 overflow-y-auto flex-1">
            {isLoading ? (
              <div className="flex justify-center items-center h-32">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
              </div>
            ) : error ? (
              <div className="text-red-500 p-4 text-center">{error}</div>
            ) : historyData ? (
              <div className="space-y-4">
                {historyData?.versions?.length > 0 ? (
                  historyData.versions.map((version) => (
                    <div
                      key={version?.id}
                      className={`relative border border-gray-300 bg-white group rounded-lg p-4 ${
                        version?.isLatest ? 'border-l-4 border-green-500' : ''
                      }`}
                    >
                      {/* <div className="font-medium text-gray-700">
                        <span className="font-semibold">
                          {version?.createdAt?.slice(0, 10)}
                        </span>
                      </div> */}
                      <div className="flex justify-between items-center mb-3">
                        {version?.title && (
                          <h3 className="font-semibold text-lg text-gray-800">
                            {version.title || 'A picnic journey'}
                          </h3>
                        )}
                        <div className="text-sm font-semibold text-gray-500 flex items-center gap-3">
                          Date:{' '}
                          {formatDate(
                            version.createdAt || version.updatedAt,
                            'ordinal'
                          )}
                          {version?.isLatest && (
                            <span className="ml-2 bg-green-100 text-green-800 text-xs px-2 py-1 rounded">
                              Current
                            </span>
                          )}
                          {!version?.isLatest && mainComRefetch && !isTutor && (
                            <span className="hidden group-hover:block text-gray-500 hover:cursor-pointer">
                              <Tooltip
                                content={
                                  restoringVersionId === version?.id
                                    ? 'Restoring...'
                                    : 'Restore this version'
                                }
                                color="user"
                                size="lg"
                                delay={100}
                                className="-ml-3"
                                position="right"
                              >
                                <button
                                  onClick={() =>
                                    handleRestoreVersion(version.id)
                                  }
                                  disabled={restoringVersionId === version.id}
                                  className="disabled:opacity-50 disabled:cursor-not-allowed hover:text-blue-600 transition-colors"
                                >
                                  {restoringVersionId === version.id ? (
                                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-gray-900"></div>
                                  ) : (
                                    <Icon
                                      icon="streamline-freehand:content-paper-edit"
                                      width="20"
                                      height="20"
                                    />
                                  )}
                                </button>
                              </Tooltip>
                            </span>
                          )}
                        </div>
                      </div>
                      <div
                        dangerouslySetInnerHTML={{ __html: version?.content }}
                        className="mt-2 text-gray-600 whitespace-pre-wrap"
                      ></div>
                    </div>
                  ))
                ) : (
                  <p>No history available</p>
                )}
              </div>
            ) : null}
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default HistoryModal;
