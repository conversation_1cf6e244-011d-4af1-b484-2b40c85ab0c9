'use client';

import React, { useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Icon } from '@iconify/react';
import useDataFetch from '@/hooks/useDataFetch';
import { toast } from 'sonner';
import api from '@/lib/api';

const StorymakersReview = () => {
  const { id } = useParams();
  const router = useRouter();
  const [feedback, setFeedback] = useState('');
  const [score, setScore] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Fetch submission details
  const { data: submission, isLoading, refetch } = useDataFetch({
    queryKey: ['storymaker-submission', id],
    endPoint: `/tutor/play-submissions/storymaker/${id}`,
  });

  const handleBack = () => {
    router.push('/dashboard/submission-management/hec-play?tab=storymaker');
  };

  const handleSubmitReview = async () => {
    if (!feedback.trim()) {
      toast.error('Please provide feedback');
      return;
    }

    setIsSubmitting(true);
    try {
      await api.post(`/tutor/play-submissions/storymaker/${id}/review`, {
        feedback: feedback.trim(),
        score: score ? parseInt(score) : null,
      });

      // toast.success('Review submitted successfully');
      refetch();
      router.push('/dashboard/submission-management/hec-play?tab=storymaker');
    } catch (error) {
      console.error('Error submitting review:', error); 
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-yellow-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading submission...</p>
        </div>
      </div>
    );
  }

  if (!submission) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <Icon icon="lucide:file-x" className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600">Submission not found</p>
          <button
            onClick={handleBack}
            className="mt-4 px-4 py-2 bg-yellow-500 text-white rounded-lg hover:bg-yellow-600"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  const isReviewed = submission.status === 'reviewed';

  return (
    <div className="max-w-6xl mx-auto p-6">
      {/* Header */}
      <div className="flex items-center mb-6">
        <button
          onClick={handleBack}
          className="flex items-center text-gray-600 hover:text-yellow-600 mr-4"
        >
          <Icon icon="eva:arrow-back-fill" className="w-6 h-6" />
        </button>
        <h1 className="text-2xl font-semibold">
          Storymaker Submission Review
        </h1>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Submission Content */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h2 className="text-xl font-semibold mb-4">Submission Details</h2>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Student Name
              </label>
              <p className="text-gray-900">{submission.studentName || 'N/A'}</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Story Title
              </label>
              <p className="text-gray-900">{submission.storyTitle || 'N/A'}</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Submitted On
              </label>
              <p className="text-gray-900">
                {submission.submittedAt 
                  ? new Date(submission.submittedAt).toLocaleDateString()
                  : 'N/A'
                }
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Content
              </label>
              <div className="bg-gray-50 p-4 rounded-lg max-h-96 overflow-y-auto">
                <div 
                  dangerouslySetInnerHTML={{ 
                    __html: submission.content || 'No content available' 
                  }} 
                />
              </div>
            </div>
          </div>
        </div>

        {/* Review Section */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h2 className="text-xl font-semibold mb-4">
            {isReviewed ? 'Review Details' : 'Submit Review'}
          </h2>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Score (Optional)
              </label>
              <input
                type="number"
                min="0"
                max="100"
                value={score}
                onChange={(e) => setScore(e.target.value)}
                disabled={isReviewed}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent disabled:bg-gray-100"
                placeholder="Enter score (0-100)"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Feedback
              </label>
              <textarea
                value={feedback}
                onChange={(e) => setFeedback(e.target.value)}
                disabled={isReviewed}
                rows={8}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent disabled:bg-gray-100"
                placeholder="Provide detailed feedback for the student..."
              />
            </div>

            {!isReviewed && (
              <div className="flex justify-end">
                <button
                  onClick={handleSubmitReview}
                  disabled={isSubmitting || !feedback.trim()}
                  className={`px-6 py-2 rounded-lg ${
                    isSubmitting || !feedback.trim()
                      ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                      : 'bg-yellow-500 text-white hover:bg-yellow-600'
                  }`}
                >
                  {isSubmitting ? 'Submitting...' : 'Submit Review'}
                </button>
              </div>
            )}

            {isReviewed && (
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex items-center">
                  <Icon icon="lucide:check-circle" className="w-5 h-5 text-green-600 mr-2" />
                  <span className="text-green-800 font-medium">Review Completed</span>
                </div>
                <p className="text-green-700 mt-1">
                  Reviewed on {new Date(submission.reviewedAt).toLocaleDateString()}
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default StorymakersReview;
