'use client';
import React, { useState } from 'react';
import Image from 'next/image';
import { Icon } from '@iconify/react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { IMAGE_BASE_URL } from '@/lib/config';
import api from '@/lib/api';
import { useQueryClient } from '@tanstack/react-query';
import DeleteModal from '@/components/form/modal/DeleteModal';

const ShopCategory = ({ categoryName, items, categoryId, refetch }) => {
  const router = useRouter();
  const [deleteData, setDeleteData] = useState(null);
  const queryClient = useQueryClient();

  return (
    <div className="mb-8">
      {/* Category Header */}
      <div className="flex justify-between items-center mb-4 border-b pb-2 border-gray-300">
        <h3 className="text-lg font-semibold uppercase">{categoryName}</h3>
        <Link
          href={`/dashboard/shops/${categoryId}`}
          className="text-sm text-gray-600 hover:text-gray-800 flex items-center"
        >
          See All <span className="ml-1">→</span>
        </Link>
      </div>

      {/* Items Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {items?.slice(0, 4)?.map((item) => (
          <div
            key={item.id}
            className="bg-[#F6F6F6] relative rounded-lg shadow-sm p-4 border bg-gray-100 cursor-pointer hover:shadow transition-all duration-300"
          >
            {/* Item Image */}
            <div
              onClick={() => router.push(`/dashboard/shops/details/${item.id}`)}
              className="relative h-48 mb-4"
            >
              <Image
                src={
                  item?.filePath
                    ? `${item.filePath}`
                    : '/assets/images/all-img/noImage.png'
                }
                alt={item.title}
                fill
                className="rounded-lg object-contain"
              />
              {item.type === 'free' && (
                <span className="absolute -top-4 -left-4 bg-green-500 text-white text-xs px-3 py-1 rounded-br-lg rounded-tl-lg">
                  New
                </span>
              )}
            </div>

            {/* Item Details */}
            <div className="text-[#2B2A28] space-y-2">
              <h4 className="font-[600] text-lg">{item.title}</h4>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <span className="font-[600]">
                    {item.price === 0 ? 'Free' : `${item.price} ₩`}
                  </span>
                  {item.isOnSale && (
                    <span className="ml-2 text-sm text-gray-500 line-through">
                      {item.discountedPrice} ₩
                    </span>
                  )}
                </div>

                {/* Action Buttons */}
                <div className="flex items-center space-x-2 mt-2 absolute right-3 z-10 botttom-8">
                  <button
                    onClick={() =>
                      router.push(`/dashboard/shops/edit/${item?.id}`)
                    }
                    className="p-1 hover:shadow bg-yellow-100 border border-yellow-300 rounded"
                  >
                    <Icon
                      icon="material-symbols:edit-outline"
                      className="w-5 h-5"
                    />
                  </button>
                  <button
                    onClick={() => setDeleteData(item)}
                    className="p-1 hover:shadow bg-yellow-100 rounded text-red-500 border border-red-300"
                  >
                    <Icon
                      icon="solar:trash-bin-trash-linear"
                      className="w-5 h-5"
                    />
                  </button>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      <DeleteModal
        isOpen={!!deleteData}
        onClose={() => setDeleteData(null)}
        onSuccess={refetch}
        data={deleteData}
        endPoint={`/admin/shop/items/${deleteData?.id}`}
        itemName="item"
      />
    </div>
  );
};

export default ShopCategory;
