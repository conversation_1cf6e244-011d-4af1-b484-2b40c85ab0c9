'use client';

import { use, useState } from 'react';
import Image from 'next/image';
import Modal from '@/components/Modal';
import useDataFetch from '@/hooks/useDataFetch';
import { Icon } from '@iconify/react';
import { useDispatch, useSelector } from 'react-redux';
import {
  setDeleteSkinModalData,
  setEditSkinModalId,
  setOpenCreateSkinModal,
} from '@/store/features/commonSlice';
import CreateSkinModal from './CreateSkinModal';
import DeleteModal from '@/components/form/modal/DeleteModal';
import EditSkinModal from './EditSkinModal';
import { queryClient } from '@/lib/queryClient';

const SelectSkinModal = ({ isOpen, onClose, onApply, currentSkinId }) => {
  const dispatch = useDispatch();
  const { openCreateSkinModal, editSkinModalId, deleteSkinModalData } =
    useSelector((state) => state.common);
  const { user } = useSelector((state) => state.auth);
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedSkinId, setSelectedSkinId] = useState(currentSkinId);
  const itemsPerPage = 9;
  const userId = user?.id;

  const { data, isLoading, error } = useDataFetch({
    queryKey: ['diary-skins', currentPage],
    endPoint: `/diary/skins?page=${currentPage}&limit=${itemsPerPage}`,
  });

  // Calculate total pages when data changes
  const totalPages = data?.totalPages || 1;

  const handleSkinSelect = (skin) => {
    setSelectedSkinId(skin.id);
  };

  const handleApply = () => {
    const selectedSkin = data?.items?.find(
      (skin) => skin.id === selectedSkinId
    );
    if (selectedSkin) {
      onApply(selectedSkin);
      onClose();
    }
  };

  // Handle page change
  const handlePageChange = (newPage) => {
    if (newPage >= 1 && newPage <= totalPages && !isLoading) {
      setCurrentPage(newPage);
    }
  };

  // Generate page numbers to display
  const getPageNumbers = () => {
    const pages = [];

    // For small number of pages, show all
    if (totalPages <= 5) {
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
      return pages;
    }

    // Always show first page
    pages.push(1);

    // Show pages around current page
    let startPage = Math.max(2, currentPage - 1);
    let endPage = Math.min(totalPages - 1, currentPage + 1);

    // Add ellipsis if needed before middle pages
    if (startPage > 2) {
      pages.push('...');
    }

    // Add middle pages
    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }

    // Add ellipsis if needed after middle pages
    if (endPage < totalPages - 1) {
      pages.push('...');
    }

    // Always show last page if it's different from current range
    if (endPage < totalPages) {
      pages.push(totalPages);
    }

    return pages;
  };

  return (
    <>
      {openCreateSkinModal ? (
        <CreateSkinModal />
      ) : editSkinModalId ? (
        <EditSkinModal />
      ) : (
        <Modal
          isOpen={isOpen}
          onClose={onClose}
          title="Select Skin"
          width="4xl"
        >
          <div className="p-4">
            {isLoading ? (
              <div className="flex justify-center items-center py-20">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-yellow-500"></div>
              </div>
            ) : error ? (
              <div className="text-center text-red-500 py-20">
                Failed to load skins. Please try again later.
              </div>
            ) : (
              <>
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 mb-8">
                  <div
                    className="h-52 bg-yellow-100 flex flex-col items-center justify-center p-4 cursor-pointer"
                    onClick={() =>
                      dispatch(setOpenCreateSkinModal(true), onClose())
                    }
                  >
                    <div className="w-12 h-12 rounded-full bg-yellow-500 flex items-center justify-center mb-2">
                      <span className="text-white text-2xl">+</span>
                    </div>
                    <h3 className="text-yellow-700 font-bold text-center">
                      Create Your
                      <br />
                      Own Skin
                    </h3>
                  </div>
                  {data?.items?.map((skin) => (
                    <div
                      key={skin.id}
                      className={`relative border rounded-lg overflow-hidden cursor-pointer hover:shadow-lg transition-all duration-200 ${
                        selectedSkinId === skin.id
                          ? 'border-yellow-500 shadow-md'
                          : 'border-gray-100'
                      }`}
                      onClick={() => handleSkinSelect(skin)}
                      onDoubleClick={() => {
                        setSelectedSkinId(skin.id);
                        handleApply();
                      }}
                    >
                      {userId === skin?.studentId && (
                        <div className="flex items-center justify-between  absolute w-full">
                          {/* {userId === skin?.studentId && ( */}
                          <span className=" bg-yellow-500 text-white text-xs px-2 py-1 rounded-tl rounded-br">
                            My Skin
                          </span>
                          {/* )} */}

                          <div className="flex items-center gap-1">
                            <span
                              onClick={() =>
                                dispatch(
                                  setEditSkinModalId(skin?.id),
                                  onClose()
                                )
                              }
                              className="bg-white p-1 rounded border shadow hover:bg-gray-100"
                              title="Edit Skin"
                            >
                              <Icon
                                icon="mdi:pencil"
                                className="text-gray-700 w-4 h-4"
                              />
                            </span>

                            <button
                              onClick={() => (
                                dispatch(
                                  setDeleteSkinModalData({
                                    id: skin?.id,
                                    title: skin?.name,
                                  })
                                ),
                                onClose()
                              )}
                              className="bg-white p-1 rounded border shadow hover:bg-gray-100"
                              title="Delete Skin"
                            >
                              <Icon
                                icon="mdi:delete"
                                className="text-red-500 w-4 h-4"
                              />
                            </button>
                          </div>
                        </div>
                      )}
                      <Image
                        src={
                          skin.previewImagePath || '/assets/images/noImage.png'
                        }
                        alt={skin.name}
                        width={300}
                        height={200}
                        className="w-full h-40 object-cover"
                      />
                      <div className="p-3">
                        <h3 className="font-semibold">{skin.name}</h3>
                        <p className="text-sm text-gray-600">
                          {skin.description}
                        </p>
                      </div>
                      {selectedSkinId === skin.id && (
                        <div className="absolute top-2 right-2 w-6 h-6 rounded-full bg-yellow-500 flex items-center justify-center">
                          <span className="text-white text-xs">✓</span>
                        </div>
                      )}

                      {selectedSkinId === skin.id && (
                        <div className="flex justify-end gap-2 p-2 text-sm border-t">
                          <button
                            onClick={onClose}
                            className="px-3 py-1 text-gray-600 hover:text-gray-800"
                          >
                            Cancel
                          </button>
                          <button
                            onClick={handleApply}
                            disabled={!selectedSkinId}
                            className={`px-4 py-1 rounded-lg text-white font-medium transition-all ${
                              selectedSkinId
                                ? 'bg-yellow-500 hover:bg-yellow-600'
                                : 'bg-gray-300 cursor-not-allowed'
                            }`}
                          >
                            Apply
                          </button>
                        </div>
                      )}
                    </div>
                  ))}
                </div>

                {/* Pagination Controls */}
                {data && data.totalCount > itemsPerPage && (
                  <div className="flex justify-center items-center mt-6 mb-4">
                    <button
                      onClick={() => handlePageChange(1)}
                      disabled={currentPage === 1 || isLoading}
                      className={`mx-1 p-2 rounded-md ${
                        currentPage === 1 || isLoading
                          ? 'text-gray-400 cursor-not-allowed'
                          : 'text-yellow-600 hover:bg-yellow-100'
                      }`}
                      aria-label="First page"
                    >
                      <Icon icon="mdi:page-first" className="w-5 h-5" />
                    </button>
                    <button
                      onClick={() => handlePageChange(currentPage - 1)}
                      disabled={currentPage === 1 || isLoading}
                      className={`mx-1 p-2 rounded-md ${
                        currentPage === 1 || isLoading
                          ? 'text-gray-400 cursor-not-allowed'
                          : 'text-yellow-600 hover:bg-yellow-100'
                      }`}
                      aria-label="Previous page"
                    >
                      <Icon icon="mdi:chevron-left" className="w-5 h-5" />
                    </button>

                    {/* Page numbers */}
                    {getPageNumbers().map((pageNum, index) => (
                      <button
                        key={`page-${index}`}
                        onClick={() =>
                          typeof pageNum === 'number' &&
                          handlePageChange(pageNum)
                        }
                        disabled={pageNum === '...' || pageNum === currentPage}
                        className={`mx-1 px-3 py-1 rounded-md ${
                          pageNum === currentPage
                            ? 'bg-yellow-500 text-white'
                            : pageNum === '...'
                            ? 'text-gray-500 cursor-default'
                            : 'bg-gray-100 hover:bg-yellow-100 text-gray-700'
                        }`}
                      >
                        {pageNum}
                      </button>
                    ))}

                    <button
                      onClick={() => handlePageChange(currentPage + 1)}
                      disabled={currentPage === totalPages || isLoading}
                      className={`mx-1 p-2 rounded-md ${
                        currentPage === totalPages || isLoading
                          ? 'text-gray-400 cursor-not-allowed'
                          : 'text-yellow-600 hover:bg-yellow-100'
                      }`}
                      aria-label="Next page"
                    >
                      <Icon icon="mdi:chevron-right" className="w-5 h-5" />
                    </button>
                    <button
                      onClick={() => handlePageChange(totalPages)}
                      disabled={currentPage === totalPages || isLoading}
                      className={`mx-1 p-2 rounded-md ${
                        currentPage === totalPages || isLoading
                          ? 'text-gray-400 cursor-not-allowed'
                          : 'text-yellow-600 hover:bg-yellow-100'
                      }`}
                      aria-label="Last page"
                    >
                      <Icon icon="mdi:page-last" className="w-5 h-5" />
                    </button>
                  </div>
                )}
              </>
            )}
          </div>

          {/* <DeleteModal /> */}
        </Modal>
      )}

      {deleteSkinModalData?.id && (
        <DeleteModal
          isOpen={deleteSkinModalData?.id !== null}
          onClose={() => dispatch(setDeleteSkinModalData(null))}
          endPoint={`/diary/skins/${selectedSkinId}`}
          data={deleteSkinModalData}
        />
      )}
    </>
  );
};

export default SelectSkinModal;
