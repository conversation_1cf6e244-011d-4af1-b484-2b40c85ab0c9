'use client';
import React, { useRef, useEffect, useState, useMemo, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import SkinPreview from '@/components/skin/SkinPreview';
import DecorationLayer from './DecorationLayer.js';
import CustomCursor from './CustomCursor.js';
import DecorationUndoRedo from './DecorationUndoRedo.js';
import DecorationDebug from './DecorationDebug.js';
import { addDecorationItem, selectIsDecorating, selectActiveTool, selectBrushSettings, setActiveTool, selectDecorationItems, undoDecoration, redoDecoration, selectCanUndo, selectCanRedo } from '@/store/features/diarySlice';

const DiaryWithDecorations = ({ skin, contentData, decorationData = undefined, scale: propScale = 1 }) => {
  const containerRef = useRef(null);
  const canvasRef = useRef(null);
  const dispatch = useDispatch();
  const isDecorating = useSelector(selectIsDecorating);
  const activeTool = useSelector(selectActiveTool);
  const brushSettings = useSelector(selectBrushSettings);
  const reduxDecorationItems = useSelector(selectDecorationItems);
  const canUndo = useSelector(selectCanUndo);
  const canRedo = useSelector(selectCanRedo);


  const [skinScale, setSkinScale] = useState(propScale);

  // Use prop decorations if explicitly provided (for read-only display), otherwise use Redux state
  // If decorationData is explicitly null from API, show empty array
  // If decorationData is undefined (not provided), fall back to Redux state
  const decorationItems = useMemo(() => {
    return decorationData !== undefined ? (decorationData || []) : reduxDecorationItems;
  }, [decorationData, reduxDecorationItems]);

  // Handle scale changes from SkinPreview
  const handleScaleChange = useCallback((newScale) => {
    setSkinScale(newScale);
  }, []);

  // Use the appropriate scale - skinScale for read-only mode, propScale for editing mode
  const effectiveScale = decorationData !== undefined ? skinScale : propScale;

  // Clear Redux decorations when in read-only mode with prop data to prevent state pollution
  useEffect(() => {
    if (decorationData !== undefined && !isDecorating) {
      // We're in read-only mode with explicit decoration data, ensure Redux state doesn't interfere
      // Only clear if Redux state has decorations that might interfere
      if (reduxDecorationItems.length > 0) {
        console.log('Clearing Redux decorations for read-only display');
        // Note: We don't actually clear here to avoid side effects,
        // instead we rely on the prop data taking precedence
      }
    }
  }, [decorationData, isDecorating, reduxDecorationItems]);
  const [containerDimensions, setContainerDimensions] = useState({ width: 0, height: 0 });
  const [isDrawing, setIsDrawing] = useState(false);
  const [lastPoint, setLastPoint] = useState(null);
  const [currentStroke, setCurrentStroke] = useState(null); // Store the current stroke being drawn

  // Parse skin to get dimensions
  let parsedSkin = {};
  try {
    parsedSkin = JSON.parse(skin || '{}');
  } catch (error) {
    console.error('Failed to parse skin JSON:', error);
  }

  const { width = 400, height = 600 } = parsedSkin;

  useEffect(() => {
    const updateDimensions = () => {
      if (containerRef.current) {
        const rect = containerRef.current.getBoundingClientRect();
        setContainerDimensions({
          width: rect.width,
          height: rect.height
        });
      }
    };

    updateDimensions();
    window.addEventListener('resize', updateDimensions);
    
    const resizeObserver = new ResizeObserver(updateDimensions);
    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);
    }

    return () => {
      window.removeEventListener('resize', updateDimensions);
      resizeObserver.disconnect();
    };
  }, []);

  useEffect(() => {
    // Redraw the canvas when decorations change
    if (canvasRef.current && decorationItems) {
      const ctx = canvasRef.current.getContext('2d');
      // Clear the canvas before redrawing
      ctx.clearRect(0, 0, containerDimensions.width, containerDimensions.height);
      // Filter for drawing items and replay them
      decorationItems.forEach(item => {
        if (item.type === 'drawing' && item.points.length > 1) {
          ctx.lineCap = 'round';
          ctx.lineJoin = 'round';
          if (item.tool === 'brush' || item.tool === 'pencil') {
            ctx.globalCompositeOperation = 'source-over';
            ctx.strokeStyle = item.color || '#000000';
            ctx.globalAlpha = item.opacity || 1;
          } else if (item.tool === 'highlighter') {
            ctx.globalCompositeOperation = 'source-over';
            ctx.strokeStyle = item.color || '#FFFF00';
            ctx.globalAlpha = item.opacity || 0.5;
          } else if (item.tool === 'eraser') {
            ctx.globalCompositeOperation = 'destination-out';
            ctx.globalAlpha = 1;
          }
          // Apply scale to line width and points
          ctx.lineWidth = (item.size || 5) * effectiveScale;
          ctx.beginPath();
          ctx.moveTo(item.points[0].x * effectiveScale, item.points[0].y * effectiveScale);
          for (let i = 1; i < item.points.length; i++) {
            ctx.lineTo(item.points[i].x * effectiveScale, item.points[i].y * effectiveScale);
          }
          ctx.stroke();
        }
      });
    }
  }, [decorationItems, containerDimensions, effectiveScale]);

  // Keyboard shortcuts for undo/redo
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (!isDecorating) return;

      if (e.ctrlKey || e.metaKey) {
        if (e.key === 'z' && !e.shiftKey && canUndo) {
          e.preventDefault();
          dispatch(undoDecoration());
        } else if ((e.key === 'y' || (e.key === 'z' && e.shiftKey)) && canRedo) {
          e.preventDefault();
          dispatch(redoDecoration());
        }
      }
    };

    if (isDecorating) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [isDecorating, canUndo, canRedo, dispatch]);

  const handleDrop = (e) => {
    e.preventDefault();
    
    if (!isDecorating) return;
    
    const rect = containerRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    
    try {
      const data = JSON.parse(e.dataTransfer.getData('text/plain'));
      
      if (data.type === 'decoration') {
        const newDecoration = {
          id: `decoration-${Date.now()}`,
          type: 'decoration', // This was missing
          src: data.src,
          title: data.title || 'Decoration',
          x: x - 40,
          y: y - 40,
          width: 80,
          height: 80,
          zIndex: 10
        };
        console.log('Decoration Added:', newDecoration);
        dispatch(addDecorationItem(newDecoration));
      } else if (data.type === 'shape') {
        const newShape = {
          id: `shape-${Date.now()}`,
          type: 'shape',
          shapeType: data.shapeType,
          title: data.title,
          x: x - 40,
          y: y - 40,
          width: 80,
          height: 80,
          zIndex: 10
        };
        console.log('Shape Added:', newShape);
        dispatch(addDecorationItem(newShape));
      } else if (data.type === 'brush') {
        dispatch(setActiveTool('brush'));
        console.log('Brush activated:', data.brushType, 'Size:', data.size);
      }
    } catch (error) {
      console.error('Error parsing drop data:', error);
    }
  };

  const handleDragOver = (e) => {
    e.preventDefault();
  };

  const startDrawing = (e) => {
    if (!isDecorating || !['brush', 'pencil', 'highlighter', 'eraser'].includes(activeTool)) return;
    
    setIsDrawing(true);
    const rect = containerRef.current.getBoundingClientRect();
    const point = {
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    };
    setLastPoint(point);
    // Start a new stroke
    setCurrentStroke({
      id: `drawing-${Date.now()}`,
      type: 'drawing',
      tool: activeTool,
      color: brushSettings.color,
      size: brushSettings.size,
      opacity: brushSettings.opacity,
      points: [point]
    });
  };

  const draw = (e) => {
    if (!isDrawing || !lastPoint || !canvasRef.current) return;
    
    const rect = containerRef.current.getBoundingClientRect();
    const currentPoint = {
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    };
    
    const ctx = canvasRef.current.getContext('2d');
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';
    
    if (activeTool === 'brush' || activeTool === 'pencil') {
      ctx.globalCompositeOperation = 'source-over';
      ctx.strokeStyle = brushSettings.color || '#000000';
      ctx.globalAlpha = brushSettings.opacity || 1;
    } else if (activeTool === 'highlighter') {
      ctx.globalCompositeOperation = 'source-over';
      ctx.strokeStyle = brushSettings.color || '#FFFF00';
      ctx.globalAlpha = brushSettings.opacity || 0.5;
    } else if (activeTool === 'eraser') {
      ctx.globalCompositeOperation = 'destination-out';
      ctx.globalAlpha = 1;
    }
    
    ctx.lineWidth = brushSettings.size || 5;
    ctx.beginPath();
    ctx.moveTo(lastPoint.x, lastPoint.y);
    ctx.lineTo(currentPoint.x, currentPoint.y);
    ctx.stroke();
    
    setLastPoint(currentPoint);
    // Add point to current stroke
    setCurrentStroke((prev) => prev ? { ...prev, points: [...prev.points, currentPoint] } : prev);
  };

  const stopDrawing = () => {
    setIsDrawing(false);
    setLastPoint(null);
    // Save the current stroke to Redux
    if (currentStroke && currentStroke.points.length > 1) {
      dispatch(addDecorationItem(currentStroke));
    }
    setCurrentStroke(null);
  };

  return (
    <div className="relative">
      <div 
        ref={containerRef}
        className="relative"
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onMouseDown={startDrawing}
        onMouseMove={draw}
        onMouseUp={stopDrawing}
        onMouseLeave={stopDrawing}
        style={{
          pointerEvents: isDecorating ? 'auto' : 'none',
          cursor: ['brush', 'pencil', 'highlighter', 'eraser'].includes(activeTool) ? 'none' : 'default'
        }}
      >
        <SkinPreview skin={skin} contentData={contentData} onScaleChange={handleScaleChange} />
        
        <canvas
          ref={canvasRef}
          className="absolute inset-0"
          width={containerDimensions.width}
          height={containerDimensions.height}
          style={{
            zIndex: 15,
            pointerEvents: isDecorating && ['brush', 'pencil', 'highlighter', 'eraser'].includes(activeTool) ? 'auto' : 'none'
          }}
        />
        
        <DecorationLayer
          containerWidth={containerDimensions.width}
          containerHeight={containerDimensions.height}
          isDecorating={isDecorating}
          decorationItems={decorationItems}
          scale={effectiveScale}
        />

        {/* Custom Cursor for drawing tools */}
        {isDecorating && (
          <CustomCursor containerRef={containerRef} />
        )}
        
        {isDecorating && (
          <div className="absolute inset-0 border-2 border-dashed border-yellow-400 pointer-events-none">
            <div className="absolute top-2 left-2 bg-yellow-400 text-black px-2 py-1 rounded text-xs font-medium flex items-center gap-2">
              <span>Decoration Mode</span>
              {activeTool !== 'decoration' && (
                <span className="bg-black bg-opacity-20 px-1 rounded text-xs">
                  {activeTool === 'brush' ? 'Brush' :
                   activeTool === 'pencil' ? 'Pencil' :
                   activeTool === 'highlighter' ? 'Highlighter' :
                   activeTool === 'eraser' ? 'Eraser' : ''}
                </span>
              )}
            </div>
            {/* Undo/Redo buttons positioned in top-right corner */}
            {/* <div className="absolute top-2 right-2 pointer-events-auto" style={{zIndex: 1000}}>
              <DecorationUndoRedo />
            </div> */}
          </div>
        )}
      </div>

      {/* Debug component - remove in production */}
      {/* <DecorationDebug /> */}
    </div>
  );
};

export default DiaryWithDecorations;