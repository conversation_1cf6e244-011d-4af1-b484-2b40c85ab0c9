import { io } from 'socket.io-client';
import { store } from '@/store/store';
import Cookies from 'js-cookie';
import { API_BASE_URL } from '@/lib/config';
import {
  addNotification,
  updateNotificationReadStatus,
  setUnreadCount
} from '@/store/features/notificationSlice';
import { handleTokenExpiration } from '@/lib/authUtils';

let socket = null;

/**
 * Initialize the Socket.io connection
 * @param {string} token - JWT token for authentication
 */
export const initializeSocket = (token) => {
  if (socket) {
    // If socket exists but disconnected, reconnect
    if (!socket.connected) {
      socket.connect();
    }
    return socket;
  }

  // Create new socket connection
  const authToken = token || Cookies.get('token');

  socket = io(API_BASE_URL, {
    auth: {
      token: authToken,
    },
    transports: ['websocket', 'polling'],
    reconnection: true,
    reconnectionAttempts: 5,
    reconnectionDelay: 1000,
  });

  // Set up event listeners
  setupSocketListeners();

  return socket;
};

/**
 * Set up event listeners for the socket
 */
const setupSocketListeners = () => {
  if (!socket) return;

  // Handle connection events
  socket.on('connect', () => {
    console.log('Socket connected');
  });

  socket.on('disconnect', (reason) => {
    console.log(`Socket disconnected: ${reason}`);
  });

  socket.on('connect_error', (error) => {
    console.error('Socket connection error:', error);

    // Check if the error is due to authentication failure
    if (error?.message?.includes('Authentication') ||
        error?.message?.includes('Unauthorized') ||
        error?.message?.includes('401')) {
      console.log('Socket authentication failed - token may be expired');
      handleTokenExpiration();
    }
  });

  // Handle notification events
  socket.on('notification', (notification) => {
    console.log('New notification received:', notification);
    store.dispatch(addNotification(notification));

    // Show toast notification
    if (typeof window !== 'undefined' && window.Sooner) {
      window.Sooner.success({
        title: notification.title,
        description: notification.message,
        position: 'top-right',
        duration: 5000,
      });
    }
  });

  socket.on('notification_read', ({ notificationId, read }) => {
    console.log('Notification read status updated:', notificationId, read);
    store.dispatch(updateNotificationReadStatus({ notificationId, read }));
  });

  socket.on('notification_count', ({ count }) => {
    console.log('Unread notification count updated:', count);
    store.dispatch(setUnreadCount(count));
  });
};

/**
 * Disconnect the socket
 */
export const disconnectSocket = () => {
  if (socket) {
    socket.disconnect();
    console.log('Socket disconnected');
  }
};

/**
 * Get the socket instance
 * @returns {Object|null} Socket instance or null if not initialized
 */
export const getSocket = () => socket;

const socketService = {
  initializeSocket,
  disconnectSocket,
  getSocket,
};

export default socketService;
