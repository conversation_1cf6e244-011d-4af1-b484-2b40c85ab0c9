'use client';
import GoBack from '@/components/shared/GoBack';
import FriendList from './_components/FriendList';
import { useDispatch, useSelector } from 'react-redux';
import { setFriendActiveTab } from '@/store/features/commonSlice';
import RequestList from './_components/RequestList';
import SearchFriend from './_components/SearchFriend';

const InviteFriends = () => {
  const { friendActiveTab: activeTab } = useSelector((state) => state.common);

  const dispatch = useDispatch();

  return (
    <div className="max-w-7xl mx-auto px-5 xl:px-0 py-5">
      <GoBack title="Diary Follow Request" linkClass={'max-w-64'} />

      <div className="shadow-lg border border-sky-200 max-sm:p-5 p-10 mt-5 rounded-lg h-full">
        <div className="max-w-4xl mx-auto">
          {/* Tabs */}
          <div className="flex space-x-2 bg-white rounded-lg shadow-sm p-3 border mb-6">
            <button
              className={`px-4 py-2 rounded-lg ${
                activeTab === 'search'
                  ? 'bg-yellow-100 text-yellow-800'
                  : 'text-gray-700'
              }`}
              onClick={() => dispatch(setFriendActiveTab('search'))}
            >
              Invite Followers
            </button>
            <button
              className={`px-4 py-2 rounded-lg ${
                activeTab === 'friends'
                  ? 'bg-yellow-100 text-yellow-800'
                  : 'text-gray-700'
              }`}
              onClick={() => dispatch(setFriendActiveTab('friends'))}
            >
              My Followers
            </button>
            <button
              className={`px-4 py-2 rounded-lg ${
                activeTab === 'requests'
                  ? 'bg-yellow-100 text-yellow-800'
                  : 'text-gray-700'
              }`}
              onClick={() => dispatch(setFriendActiveTab('requests'))}
            >
              Follow Requests
            </button>
          </div>

          {/* Tab Content */}
          {activeTab === 'friends' && <FriendList />}

          {activeTab === 'requests' && <RequestList />}

          {activeTab === 'search' && <SearchFriend />}
        </div>
      </div>
    </div>
  );
};

export default InviteFriends;
