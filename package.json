{"name": "hello-english-coaching", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev --turbopack", "dev:3001": "next dev --turbopack -p 3001", "build": "next build", "start": "next start -p 3001", "lint": "next lint"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@fullcalendar/daygrid": "^6.1.17", "@fullcalendar/interaction": "^6.1.17", "@fullcalendar/react": "^6.1.17", "@headlessui/react": "^2.2.2", "@iconify/react": "^5.2.1", "@reduxjs/toolkit": "^2.5.1", "@tanstack/react-query": "^5.66.7", "@tinymce/tinymce-react": "^6.2.1", "@tippyjs/react": "^4.2.6", "@tiptap/core": "^2.25.0", "@tiptap/extension-color": "^2.25.0", "@tiptap/extension-strike": "^2.23.1", "@tiptap/extension-text-align": "^2.14.0", "@tiptap/extension-text-style": "^2.14.0", "@tiptap/react": "^2.14.0", "@tiptap/starter-kit": "^2.14.0", "apexcharts": "^5.3.1", "axios": "^1.7.9", "date-fns": "^4.1.0", "formik": "^2.4.6", "framer-motion": "^12.6.3", "html-react-parser": "^5.2.4", "html2canvas": "^1.4.1", "js-cookie": "^3.0.5", "lucide-react": "^0.509.0", "next": "^15.3.0", "next-auth": "^4.24.11", "react": "^19.0.0", "react-apexcharts": "^1.7.0", "react-contenteditable": "^3.3.7", "react-datepicker": "^8.3.0", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-moveable": "^0.56.0", "react-redux": "^9.2.0", "react-select": "^5.10.1", "react-toastify": "^11.0.3", "redux-persist": "^6.0.0", "socket.io-client": "^4.8.1", "sonner": "^2.0.3", "tinymce": "^7.9.1", "yup": "^1.6.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "eslint": "^9", "eslint-config-next": "15.1.7", "postcss": "^8", "tailwindcss": "^3.4.1"}}