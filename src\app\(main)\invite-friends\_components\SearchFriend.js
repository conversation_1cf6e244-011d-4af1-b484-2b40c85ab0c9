'use client';
import api from '@/lib/api';
import { Icon } from '@iconify/react';
import Image from 'next/image';
import React, { useEffect, useState } from 'react';
import { toast } from 'sonner';
import { useSearchParams } from 'next/navigation';
import useDataFetch from '@/hooks/useDataFetch';

const SearchFriend = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [isSearching, setIsSearching] = useState(false);
  const [hasSearched, setHasSearched] = useState(false);
  const searchParams = useSearchParams();

  const { data, isLoading, refetch } = useDataFetch({
    queryKey: ['available-friends'],
    endPoint: `/student/friends/search`,
  });

  const availableFriends = data?.items || [];

  const handleSearchChange = (e) => {
    setSearchQuery(e.target.value);
  };

  const handleSearchSubmit = async (e) => {
    e.preventDefault();
    setHasSearched(true);

    if (searchQuery.length === 0) {
      setSearchResults([]);
      return;
    }

    if (searchQuery.length < 3) {
      toast.error('Please enter at least 3 characters');
      return;
    }

    try {
      setIsSearching(true);
      const response = await api.get(
        `student/friends/search?query=${searchQuery}`
      );
      const results = response?.data?.items || [];
      setSearchResults(results);
    } catch (error) {
      console.error('Error searching friends:', error);
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  };

  const handleSendRequest = async (userId) => {
    try {
      await api.post(`/student/friends/diary-follow/request`, {
        diaryOwnerId: userId,
        requestMessage: '',
      });

      refetch();
      // Refresh the current list (either search results or available friends)
      if (hasSearched && searchQuery.length >= 3) {
        const response = await api.get(
          `student/friends/search?query=${searchQuery}`
        );
        const results = response?.data?.items || [];
        setSearchResults(results);
      }
      // If showing available friends, we could refetch them here if needed
      // For now, the useDataFetch hook should handle the refresh automatically
    } catch (error) {
      console.error('Error sending friend request:', error);
    }
  };

  // Function to render user list (either available friends or search results)
  const renderUserList = (users) => (
    <div className="space-y-4 max-h-[600px] overflow-y-auto p-4">
      {users.map((user) => (
        <div
          key={user.id}
          className="flex items-center justify-between py-3 border-b border-gray-100"
        >
          <div className="flex items-center">
            <div className="w-12 h-12 rounded-full overflow-hidden mr-4">
              <Image
                src={user.profilePicture || '/assets/images/all-img/avatar.png'}
                alt={user.name}
                width={100}
                height={100}
                className="w-full h-full object-cover"
              />
            </div>
            <div>
              <p className="font-medium">{user.name}</p>
              <p className="text-sm text-gray-500">
                {user.email || user.userId}
              </p>
            </div>
          </div>
          {user?.canViewDiary ? (
            <button
              className="bg-green-100 text-green-700 px-3 py-1 rounded-lg text-sm border border-green-500"
              disabled
            >
              <span>
                <Icon
                  icon="mdi:check"
                  width="24"
                  height="24"
                  className="inline-block mb-0.5"
                />{' '}
                Follower
              </span>
            </button>
          ) : user?.diaryFollowInvitationSent ? (
            <button
              className="bg-blue-100 text-blue-700 px-3 py-1 rounded-lg text-sm border border-blue-500"
              disabled
            >
              <span>
                <Icon
                  icon="mdi:clock-outline"
                  width="20"
                  height="20"
                  className="inline-block mb-0.5"
                />{' '}
                Invited
              </span>
            </button>
          ) : (
            <button
              className="bg-yellow-100 text-gray-700 px-3 py-1 rounded-lg text-sm hover:bg-yellow-200 border border-gray-500"
              onClick={() => handleSendRequest(user.id)}
            >
              <span>+ Invite to Follow</span>
            </button>
          )}
        </div>
      ))}
    </div>
  );

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">Diary Follow Request</h2>

      <div className="rounded-lg shadow-md p-6 mb-4 bg-[#FCF8EF]">
        <form onSubmit={handleSearchSubmit} className="flex items-center gap-2">
          <div className="flex-1 relative">
            <input
              type="text"
              placeholder="Search by name or email"
              value={searchQuery}
              onChange={handleSearchChange}
              className="w-full pl-9 px-4 py-2 rounded-lg focus:outline-none focus:ring-1 focus:ring-yellow-400"
            />
            <Icon
              icon="material-symbols:search"
              className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 w-5 h-5"
            />
          </div>
          <button
            type="submit"
            className="text-white tex-lg bg-gray-700 px-4 py-2 rounded-full flex items-center gap-2"
            disabled={isSearching}
          >
            <Icon icon="iconoir:search" width="24" height="24" />
            <span>{isSearching ? 'Searching...' : 'Search'}</span>
          </button>
        </form>
      </div>

      {isSearching ? (
        <div className="flex justify-center items-center py-20">
          <Icon
            icon="eos-icons:loading"
            width="48"
            height="48"
            className="animate-spin text-yellow-500"
          />
        </div>
      ) : !hasSearched ? (
        // Show available friends initially
        isLoading ? (
          <div className="flex justify-center items-center py-20">
            <Icon
              icon="eos-icons:loading"
              width="48"
              height="48"
              className="animate-spin text-yellow-500"
            />
          </div>
        ) : availableFriends.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-20">
            <Icon
              icon="mingcute:user-x-line"
              width="64"
              height="64"
              className="text-gray-400 mb-4"
            />
            <h3 className="text-xl font-medium text-gray-600">
              No friends available
            </h3>
            <p className="text-gray-500">
              Use the search to find friends to invite
            </p>
          </div>
        ) : (
          <div>
            <h3 className=" font-medium mb-4 text-gray-700">Friends</h3>
            {renderUserList(availableFriends)}
          </div>
        )
      ) : searchResults.length === 0 ? (
        <div className="flex flex-col items-center justify-center py-20">
          <Icon
            icon="mingcute:user-x-line"
            width="64"
            height="64"
            className="text-gray-400 mb-4"
          />
          <h3 className="text-xl font-medium text-gray-600">No users found</h3>
          <p className="text-gray-500">Try with a different name or email</p>
        </div>
      ) : (
        <div>
          <h3 className="text-lg font-medium mb-4 text-gray-700">
            Search Results
          </h3>
          {renderUserList(searchResults)}
        </div>
      )}
    </div>
  );
};

export default SearchFriend;
