import { configureStore } from '@reduxjs/toolkit';
import { persistStore, persistReducer } from 'redux-persist';
import storage from 'redux-persist/lib/storage';

import authReducer from './features/authSlice';
import canvasReducer from './features/canvasSlice';
import crudReducer from './features/crudSlice';
import diaryReducer from './features/diarySlice';
import commonReducer from './features/commonSlice';
import hecPlayReducer from './features/hecPlaySlice';
import notificationReducer from './features/notificationSlice';
import chatReducer from './features/chatSlice';

// Persist configuration
const authPersistConfig = {
  key: 'auth',
  storage,
  whitelist: ['user', 'token', 'isAuth', 'isAdmin', 'isStudent', 'isTutor'],
};

// Canvas state is not persisted

// Create persisted reducers
const persistedAuthReducer = persistReducer(authPersistConfig, authReducer);

export const store = configureStore({
  reducer: {
    auth: persistedAuthReducer,
    canvas: canvasReducer,
    crud: crudReducer,
    diary: diaryReducer,
    common: commonReducer,
    hecPlay: hecPlayReducer,
    notifications: notificationReducer,
    chat: chatReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
      },
    }),
});

export const persistor = persistStore(store);

export default store;
