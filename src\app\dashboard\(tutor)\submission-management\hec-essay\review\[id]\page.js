'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import { toast } from 'sonner';
import api from '@/lib/api';
import useDataFetch from '@/hooks/useDataFetch';
import HecSubmissionLayout from '@/components/layouts/HecSubmissionLayout';

import EditorViewer from '@/components/EditorViewer';
import SkinPreview from '@/components/skin/SkinPreview';
import EssayFeedbackModal from '../_components/EssayFeedbackModal';
import SimpleTiptapEditor from '@/components/form/SimpleTiptapEditor';

const EssayReviewPage = () => {
  const { id } = useParams();
  const router = useRouter();
  const [correctionText, setCorrection] = useState('');
  const [score, setScore] = useState('');
  const [taskRemarks, setTaskRemarks] = useState('');
  const searchParams = useSearchParams();
  const tabParam = searchParams.get('tab');
  const [activeTab, setActiveTab] = useState(tabParam || 'essaySubmissions');
  const [isFeedbackModalOpen, setIsFeedbackModalOpen] = useState(false);
  const editorRef = useRef(null);
  const [isPostMethod, setIsPostMethod] = useState(true);
  const [isSubmittingReview, setIsSubmittingReview] = useState(false);

  useEffect(() => {
    if (tabParam) {
      setActiveTab(tabParam);
    }
  }, [tabParam]);

  // Define tabs for the layout
  const tabs = [
    { name: 'Essay Submissions', value: 'essaySubmissions' },
    { name: 'Mission Essay', value: 'missionEssay' },
  ];

  // Fetch essay entry data
  const { data, isLoading, error, refetch } = useDataFetch({
    queryKey: 'essay-entry-review',
    endPoint: `/tutor-essay/${id}`,
    enabled: !!id,
  });

  // Get the latest submission from submissionHistory
  const latestSubmission =
    data?.submissionHistory?.[data?.submissionHistory?.length - 1];
  const isReviewed =
    data?.status === 'reviewed' && latestSubmission?.submissionMark;
  const existingFeedback =
    latestSubmission?.submissionMark?.taskRemarks ||
    latestSubmission?.content ||
    '';
  const existingPoints = latestSubmission?.submissionMark?.points || '';

  // Extract feedbacks from submission history for the modal
  const feedbacks =
    data?.submissionHistory
      ?.map((submission) => submission?.submissionMark?.submissionFeedback)
      .filter((feedback) => feedback)
      .join('\n') || '';

  // Initialize correction text and score when data is loaded
  useEffect(() => {
    if (existingFeedback) {
      setTaskRemarks(existingFeedback);
    } else {
      setTaskRemarks('');
    }
  }, [data, isReviewed, existingFeedback]);

  useEffect(() => {
    if (existingPoints && !score) {
      setScore(existingPoints.toString());
    }
  }, [existingPoints, score]);

  // Determine if we should use POST or PATCH method
  useEffect(() => {
    setIsPostMethod(!isReviewed);
  }, [isReviewed]);

  // Submit review function
  const submitReview = async () => {
    try {
      setIsSubmittingReview(true);
      if (!correctionText.trim() || !score) {
        toast.error('Please provide both correction and score');
        return;
      }

      const method = isPostMethod ? 'post' : 'patch';
      const response = await api[method](`/tutor-essay/markEssay`, {
        submissionId: data?.id,
        points: parseInt(score),
        taskRemarks: correctionText,
      });

      if (response.success) {
        if (refetch) {
          refetch();
        }
        router.push(
          '/dashboard/submission-management/hec-essay?tab=essaySubmissions'
        );
      }
    } catch (error) {
      console.error('Error submitting review:', error);
    } finally {
      setIsSubmittingReview(false);
    }
  };

  if (isLoading) {
    return (
      <HecSubmissionLayout
        activeTab={activeTab}
        tabs={tabs}
        title="HEC Essay"
        basePath="/dashboard/submission-management/hec-essay"
      >
        <div className="flex items-center justify-center h-96">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-yellow-500"></div>
        </div>
      </HecSubmissionLayout>
    );
  }

  if (error) {
    return (
      <HecSubmissionLayout
        activeTab={activeTab}
        tabs={tabs}
        title="HEC Essay"
        basePath="/dashboard/submission-management/hec-essay"
      >
        <div className="bg-red-100 text-red-700 p-4 rounded-md">
          Error loading essay entry: {error.message}
        </div>
      </HecSubmissionLayout>
    );
  }

  return (
    <HecSubmissionLayout
      activeTab={activeTab}
      tabs={tabs}
      title="HEC Essay"
      basePath="/dashboard/submission-management/hec-essay"
    >
      {/* Student Panel-like Interface */}
      <div className="max-w-7xl mx-auto relative my-8">
        <div className="p-2 bg-[#FDE7E9] grid grid-cols-1 lg:grid-cols-2 gap-3 rounded">
          {/* Left Panel - Skin Preview */}
          <div className="bg-white rounded-lg text-center flex items-center relative py-4">
            <div className="space-y-5 w-full">
              {data?.diarySkin ? (
                <SkinPreview
                  skin={data.diarySkin.templateContent}
                  contentData={{
                    subject: data?.title || 'Essay Title',
                    body:
                      data?.submissionHistory?.[
                        data?.submissionHistory?.length - 1
                      ]?.content || 'Essay content...',
                    date: data?.submissionHistory?.[
                      data?.submissionHistory?.length - 1
                    ]?.submissionDate
                      ? new Date(
                          data.submissionHistory[
                            data?.submissionHistory?.length - 1
                          ].submissionDate
                        )
                          .toISOString()
                          .slice(0, 10)
                      : new Date().toISOString().slice(0, 10),
                  }}
                />
              ) : (
                <div className="p-8 text-gray-500">
                  <p>No skin preview available</p>
                </div>
              )}
            </div>
          </div>

          {/* Right Panel - Student Content and Tutor Correction */}
          <div className="bg-white rounded-lg p-3 space-y-3 w-full">
            {/* Student Submission Display */}
            <div className="flex flex-col p-2 shadow-lg border rounded-lg gap-3">
              <div className="flex items-center justify-between border-b border-dashed pb-1 gap-3">
                <h3 className="p-2 mr-2 w-full">
                  {data?.title || 'Essay Title'}
                </h3>
                <span className="min-w-24 text-sm text-gray-600">
                  {data?.submissionHistory?.[
                    data?.submissionHistory?.length - 1
                  ]?.submissionDate
                    ? new Date(
                        data.submissionHistory[
                          data?.submissionHistory?.length - 1
                        ].submissionDate
                      ).toLocaleDateString()
                    : 'Unknown Date'}
                </span>
              </div>

              {/* Student Content */}
              <div className="border p-2 w-full rounded min-h-40 max-h-52 overflow-y-auto shadow-[inset_2px_2px_6px_0px_#0000001F] bg-gray-50">
                <EditorViewer
                  data={
                    data?.submissionHistory?.[
                      data?.submissionHistory.length - 1
                    ]?.content || 'No content available'
                  }
                  className="whitespace-pre-wrap text-sm text-[#314158]"
                />
              </div>
            </div>

            {/* Tutor Correction Zone */}
            <div className=" shadow-lg border rounded-lg p-2 relative">
              <div className="h-full">
                <p className="text-sm text-[#864D0D] text-center font-medium mb-2">
                  Tutor Correction Zone
                </p>

                {/* Submission Correction Editor */}
                <div className="space-y-3">
                  <SimpleTiptapEditor
                    editorRef={editorRef}
                    initialValue={taskRemarks || ''}
                    setValue={setCorrection}
                    height={200}
                  />
                </div>
              </div>

              {/* Action Buttons */}
              <div className="mt-4 flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <button
                    onClick={() => setIsFeedbackModalOpen(true)}
                    className="px-2 py-1 bg-[#FEFCE8] text-sm text-[#723F11] rounded-md border border-[#723F11] font-medium hover:bg-[#FFF8D6]"
                  >
                    Give feedback
                  </button>
                  <div className="flex items-center">
                    <label className="mr-2 text-sm">Score:</label>
                    {isReviewed ? (
                      <p className="text-sm border px-2 py-0.5 rounded">
                        {score}
                      </p>
                    ) : (
                      <input
                        type="number"
                        value={score}
                        onChange={(e) => setScore(e.target.value)}
                        className="w-20 border border-gray-300 rounded px-2 py-1 text-sm"
                        min="0"
                        max="100"
                        placeholder="0-100"
                      />
                    )}
                  </div>
                </div>

                {(data?.status !== 'reviewed') && (
                  <div className="flex items-center gap-2">
                    <button
                      onClick={() => router.back()}
                      className="px-3 py-1 bg-gray-400 text-white rounded-md text-sm hover:bg-gray-500"
                    >
                      Cancel
                    </button>
                    <button
                      onClick={submitReview}
                      disabled={!correctionText.trim() || !score}
                      className="px-3 py-1 bg-yellow-500 text-white rounded-md text-sm hover:bg-yellow-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
                    >
                      {isSubmittingReview ? 'Submitting...' : 'Submit Review'}
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      <EssayFeedbackModal
        isOpen={isFeedbackModalOpen}
        feedbacks={feedbacks || ''}
        onClose={() => setIsFeedbackModalOpen(false)}
        onSuccess={() => refetch()}
        submissionId={data?.id}
        isPostMethod={isPostMethod}
      />
    </HecSubmissionLayout>
  );
};

export default EssayReviewPage;
