'use client';
import { useEffect } from 'react';

export default function Error({ error, reset }) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error('Skin editor error:', error);
  }, [error]);

  return (
    <div className="flex items-center justify-center min-h-[500px]">
      <div className="bg-white p-8 rounded-lg shadow-lg text-center max-w-md">
        <div className="text-red-500 text-6xl mb-4">⚠️</div>
        <h2 className="text-2xl font-bold text-red-600 mb-2">Something went wrong!</h2>
        <p className="text-gray-700 mb-6">
          We encountered an error while loading the skin editor. Please try again.
        </p>
        <button
          onClick={() => reset()}
          className="px-6 py-3 bg-[#8B4513] text-white rounded-md hover:bg-[#723F11] transition-colors"
        >
          Try again
        </button>
      </div>
    </div>
  );
}
