import axios from 'axios';
import Cookies from 'js-cookie';
import { API_BASE_URL } from './config';
import { toast } from 'sonner';
import { handleTokenExpiration, isTokenExpiredError } from './authUtils';

/**
 * Axios instance with automatic toast notifications
 *
 * Toast Behavior:
 * - Success toasts: Show by default for non-GET requests unless showSuccessToast: false
 * - Error toasts: Show by default unless showErrorToast: false
 *
 * Usage Examples:
 *
 * // Default behavior - shows success/error toasts
 * api.post('/users', userData)
 *
 * // Disable success toast
 * api.post('/users', userData, { showSuccessToast: false })
 *
 * // Disable error toast
 * api.post('/users', userData, { showErrorToast: false })
 *
 * // Disable both toasts
 * api.post('/users', userData, { showSuccessToast: false, showErrorToast: false })
 */

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

api.interceptors.request.use(
  (config) => {
    const token = Cookies.get('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    // Show request error toast by default unless explicitly set to false
    const shouldShowErrorToast = error.config?.showErrorToast !== false;

    if (shouldShowErrorToast) {
      toast.error('Request failed. Please check your connection and try again.');
    }
    return Promise.reject(error);
  }
);

api.interceptors.response.use(
  (response) => {
    const config = response.config || {};
    // Show success toast by default unless explicitly set to false
    const shouldShowSuccessToast = config?.showSuccessToast !== false;

    if (
      response?.data?.success &&
      config.method !== 'get' &&
      shouldShowSuccessToast
    ) {
      toast.success(
        response?.data?.message ||
        response?.message ||
        'Operation completed successfully!'
      );
    }
    return response.data;
  },
  (error) => {
    const config = error.config || {};
    // Show error toast by default unless explicitly set to false
    const shouldShowErrorToast = config?.showErrorToast !== false;

    // Check for token expiration (401 Unauthorized)
    if (isTokenExpiredError(error)) {
      // Handle automatic logout
      handleTokenExpiration();

      // Return early to prevent further error handling
      return Promise.reject(error);
    }

    const errorMessage =
      error?.response?.data?.message ||
      error.message ||
      'Something went wrong!';

    if (shouldShowErrorToast) {
      toast.error(errorMessage);
    }
    return Promise.reject(error);
  }
);

export default api;
