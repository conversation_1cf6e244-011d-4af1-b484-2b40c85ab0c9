import FormDatePicker from '@/components/form/FormDatePicker';
import FormInput from '@/components/form/FormInput';
import FormSelect from '@/components/form/FormSelect';
import NumberInput from '@/components/form/NumberInput';
import api from '@/lib/api';
import { queryClient } from '@/lib/queryClient';
import { Form, Formik } from 'formik';
import React from 'react';

const AdminEditForm = ({ editValue, setIsEditing }) => {
  const handleSubmit = async (values) => {
    try {
      await api.patch('/users/profile', values);
      setIsEditing(false);
      queryClient.invalidateQueries('admin-profile');
    } catch (error) {
      console.log(error);
    }
  };

  const gendarOptions = [
    {
      label: 'Male',
      value: 'male',
    },
    {
      label: 'Female',
      value: 'female',
    },
  ];

  const bloodGroups = [
    { label: 'O+', value: 'o+' },
    { label: 'O−', value: 'o-' },
    { label: 'A+', value: 'a+' },
    { label: 'A−', value: 'a-' },
    { label: 'B+', value: 'b+' },
    { label: 'B−', value: 'b-' },
    { label: 'AB+', value: 'ab+' },
    { label: 'AB−', value: 'ab-' },
  ];

  return (
    <Formik
      initialValues={{
        bio: editValue?.bio || '',
        dateOfBirth: editValue?.dateOfBirth || '',
        gender: editValue?.gender || '',
        // blood: editValue?.blood || '',
        address: editValue?.address || '',
        phoneNumber: editValue?.phoneNumber || '',
      }}
      onSubmit={handleSubmit}
    >
      {({ isSubmitting }) => (
        <Form>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <div>
              <h3 className="text-base font-medium mb-4 text-amber-800">
                Personal Details
              </h3>
              <div className="space-y-3">
                <div>
                  <label className="text-xs font-bold uppercase tracking-wide block mb-2">
                    Biography
                  </label>
                  <FormInput
                    name="bio"
                    type="textarea"
                    placeholder="Please enter your bio data"
                    isTextarea={true}
                  />
                </div>

                <div>
                  <label className="text-xs font-bold uppercase tracking-wide block mb-2">
                    Age
                  </label>
                  <FormDatePicker
                    // label={'Birth Date'}
                    name="dateOfBirth"
                    placeholder="Date of Birth"
                  />
                </div>
                <div>
                  <label className="text-xs font-bold uppercase tracking-wide block mb-2">
                    Gender
                  </label>
                  <FormSelect
                    name={'gender'}
                    options={gendarOptions}
                    placeholder="Select your gender"
                  />
                </div>
                {/* <div>
                  <label className="text-xs font-bold uppercase tracking-wide block mb-2">
                    Blood Group
                  </label>
                  <FormSelect
                    name={'blood'}
                    options={bloodGroups}
                    placeholder="Select your blood"
                  />
                </div> */}
              </div>
            </div>

            <div className="relative">
              {/* Vertical Dotted Line */}
              <div className="absolute left-0 top-0 bottom-0 w-0.5 border-l-2 border-dotted border-gray-300 hidden lg:block"></div>

              <div className="lg:pl-12">
                <h3 className="text-base font-medium text-amber-800 mb-4">
                  Contact Details
                </h3>
                <div className="space-y-3">
                  <div>
                    <label className="text-xs font-bold uppercase tracking-wide block mb-2">
                      Address
                    </label>
                    <FormInput
                      name="address"
                      placeholder="Enter your address"
                    />
                  </div>
                  <div>
                    <label className="text-xs font-bold uppercase tracking-wide block mb-2">
                      Phone Number
                    </label>
                    <NumberInput name="phoneNumber" />
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="flex items-center justify-end gap-2">
            <button
              onClick={() => setIsEditing(false)}
              className="bg-gray-300 rounded px-4 py-1.5 text-sm border"
            >
              Cancel
            </button>
            <button
              disabled={isSubmitting}
              className="bg-yellow-500 rounded px-4 py-1.5 text-sm disabled:bg-yellow-400 disabled:cursor-not-allowed border"
            >
              {isSubmitting ? 'Submitting...' : 'Submit'}
            </button>
          </div>
        </Form>
      )}
    </Formik>
  );
};

export default AdminEditForm;
