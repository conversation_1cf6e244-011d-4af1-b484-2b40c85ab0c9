'use client';
import GoBack from '@/components/shared/GoBack';
import useDataFetch from '@/hooks/useDataFetch';
import React from 'react';
import { Icon } from '@iconify/react';
import WaterfallGameMain from './_components/WaterfallGameMain';

const WaterPlay = () => {
  const { data, isLoading, error, refetch } = useDataFetch({
    queryKey: ['waterfall-game'],
    endPoint: '/play/waterfall/new-game',
  });

  // Loading state
  if (isLoading) {
    return (
      <div className="max-w-7xl mx-auto p-5 xl:px-0 flex justify-center items-center min-h-[60vh]">
        <GoBack title={'HEC Play'} linkClass="absolute top-5 left-5 w-full max-w-40" />
        <div className="flex flex-col items-center">
          <Icon icon="eos-icons:loading" className="text-yellow-500 text-5xl animate-spin" />
          <p className="mt-4 text-gray-600">Loading questions...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="max-w-7xl mx-auto p-5 xl:px-0">
        <GoBack title={'HEC Play'} linkClass="my-5 w-full max-w-40" />
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
          <Icon icon="material-symbols:error" className="text-red-500 text-5xl mx-auto" />
          <h2 className="text-xl font-semibold mt-4 text-red-700">Error Loading Game</h2>
          <p className="text-red-600 mt-2 mb-4">There was a problem loading the game. Please try again.</p>
          <button
            onClick={() => refetch()}
            className="px-4 py-2 bg-red-100 hover:bg-red-200 text-red-700 rounded-md"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-5 xl:px-0">
      <GoBack title={'HEC Play'} linkClass="my-5 w-full max-w-40" />

      {/* Waterfall Game */}
      <WaterfallGameMain
        initialData={data}
        refetchGame={refetch}
      />
    </div>
  );
};

export default WaterPlay;
