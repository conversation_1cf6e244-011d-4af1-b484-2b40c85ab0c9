'use client';

import React, { useState, useEffect } from 'react';
import api from '@/lib/api';
import NewTablePage from '@/components/form/NewTablePage';
import { useRouter } from 'next/navigation';
import { toast } from 'react-toastify';
import { useQuery } from '@tanstack/react-query';
import { useSelector } from 'react-redux';

const QASubmissionsList = () => {
  const router = useRouter();
  const auth = useSelector((state) => state.auth);
  
  // State variables
  const [submissions, setSubmissions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [sortField, setSortField] = useState('createdAt');
  const [sortDirection, setSortDirection] = useState('DESC');
  const [activeTab, setActiveTab] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalItems, setTotalItems] = useState(0);
  const [totalPages, setTotalPages] = useState(1);

  // Tabs configuration
  const tabs = [
    { name: 'Weekly Submissions', frequency: 'weekly' },
    { name: 'Monthly Submissions', frequency: 'monthly' }
  ];

  // Table columns configuration
  const columns = [
    { 
      label: 'STUDENT NAME', 
      field: 'student_name',
      sortable: false,
    },
   
    { 
      label: activeTab === 0 ? 'WEEK' : 'MONTH', 
      field: 'mission_sequence_number',
      sortable: false,
      cellRenderer: (value) => (
        <div className="flex items-center">
          <span className="text-sm font-medium">
            {activeTab === 0 ? `Week ${value}` : `Month ${value}`}
          </span>
        </div>
      )
    },
    {
      field: 'status',
      label: 'REVIEW STATUS',
      sortable: false,
      cellRenderer: (_, row) => {
        const isReviewed = row.status === 'reviewed';
        const isSubmitted = row.status === 'submitted';
        const isDraft = row.status === 'draft';

        return (
          <div className="flex items-center">
            <span
              className={`px-3 py-1 rounded-full text-xs font-medium ${
                isReviewed
                  ? 'bg-green-100 text-green-800'
                  : isSubmitted
                  ? 'bg-orange-100 text-orange-800'
                  : 'bg-yellow-100 text-yellow-800'
              }`}
            >
              {isReviewed
                ? 'Reviewed'
                : isSubmitted
                ? 'Not Reviewed Yet'
                : 'Incomplete'}
            </span>
            {isSubmitted && <span className="ml-2 text-red-500">✕</span>}
            {isDraft && <span className="ml-2 text-yellow-500">⏱</span>}
            {isReviewed && <span className="ml-2 text-green-500">✓</span>}
          </div>
        );
      }
    }
  ];

  // Handle view submission action
  const handleViewSubmission = (submission) => {
    // Only navigate if status is not draft
    if (submission.status !== 'draft') {
      router.push(`hec-qa/review/mission/${submission.id}?tab=questionSubmission`);
    }
  };

  // Table actions configuration - now with conditional disable
  const actions = [
    {
      icon: 'material-symbols:visibility',
      className: (row) => 
        row.status === 'draft' 
          ? 'text-gray-400 cursor-not-allowed' 
          : 'text-blue-600 hover:text-blue-700 cursor-pointer',
      onClick: handleViewSubmission,
      tooltip: (row) => 
        row.status === 'draft' 
          ? 'Cannot view draft submissions' 
          : 'View Submission',
      disabled: (row) => row.status === 'draft',
    }
  ];

  // Fetch submissions from API
  const fetchSubmissions = async () => {
    try {
      setLoading(true);
      
      // Construct query parameters
      const params = {
        page: currentPage,
        limit: rowsPerPage,
        timeFrequency: tabs[activeTab].frequency,
        sortBy: sortField,
        sortDirection: sortDirection
      };

      const response = await api.get('/tutor-qa-mission/QASubmissions', { params });
      
      if (response?.success) {
        const submissionItems = response.data?.items || [];
        
        const formattedSubmissions = submissionItems.map((submission, index) => ({
          id: submission.submissionHistory?.[0]?.id || `${submission.student_id}_${index}`,
          student_name: submission.student_name,
          student_email: submission.student_email,
          student_id: submission.student_id,
          mission_id: submission.mission_id,
          mission_sequence_number: submission.mission_sequence_number,
          mission_time_frequency: submission.mission_time_frequency,
          submissionHistory: submission.submissionHistory,
          wordCount: submission.submissionHistory?.[0]?.wordCount || 0,
          submissionDate: submission.submissionHistory?.[0]?.submissionDate,
          content: submission.submissionHistory?.[0]?.content,
          status: submission.status || submission.submissionHistory?.[0]?.status || 'draft'
        }));
        
        setSubmissions(formattedSubmissions);
        setTotalItems(response.data?.totalItems || 0);
        setTotalPages(response.data?.totalPages || 1);
      } else {
        throw new Error(response?.message || 'Failed to fetch submissions');
      }
    } catch (error) {
      console.error('Error fetching submissions:', error);
      toast.error(error.message || 'Error fetching submissions');
      setSubmissions([]);
      setTotalItems(0);
      setTotalPages(1);
    } finally {
      setLoading(false);
    }
  };

  // Use React Query for data fetching
  useQuery({
    queryKey: [
      'qaSubmissions',
      activeTab,
      currentPage,
      rowsPerPage,
      sortField,
      sortDirection
    ],
    queryFn: fetchSubmissions,
  });

  // Reset states when tab changes
  useEffect(() => {
    setCurrentPage(1);
  }, [activeTab]);

  // Handle page change
  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  // Handle sort change
  const handleSort = (field) => {
    if (field === sortField) {
      setSortDirection(sortDirection === 'ASC' ? 'DESC' : 'ASC');
    } else {
      setSortField(field);
      setSortDirection('ASC');
    }
    setCurrentPage(1);
  };

  // Handle tab change
  const handleTabChange = (index) => {
    setActiveTab(index);
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold">Q&A Submissions</h1>
      </div>
      
      {/* Tab navigation */}
      <div className="flex border-b border-gray-200 mb-6">
        {tabs.map((tab, index) => (
          <button
            key={index}
            className={`py-3 px-6 font-medium text-sm focus:outline-none ${
              activeTab === index 
                ? 'text-black border-b-2 border-yellow-500 hover:bg-[#FEFCE8]' 
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => handleTabChange(index)}
          >
            {tab.name}
          </button>
        ))}
      </div>
      
      {/* Table component */}
      <NewTablePage
        title=""
        showCreateButton={false}
        columns={columns}
        actions={actions}
        data={submissions}
        loading={loading}
        currentPage={currentPage}
        totalPages={totalPages}
        changePage={handlePageChange}
        totalItems={totalItems}
        rowsPerPage={rowsPerPage}
        setRowsPerPage={setRowsPerPage}
        showSearch={false}
        showNameFilter={false}
        showSortFilter={false}
        onSort={handleSort}
        sortField={sortField}
        sortDirection={sortDirection}
        showCheckboxes={false}
      />
      
      {/* Empty state */}
      {submissions.length === 0 && !loading && (
        <div className="text-center py-8 text-gray-500">
          No submissions found.
        </div>
        )}
    </div>
  );
};

export default QASubmissionsList;