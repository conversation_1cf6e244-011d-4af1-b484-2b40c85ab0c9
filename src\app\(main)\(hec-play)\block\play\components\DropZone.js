import React, { useEffect, useRef } from 'react';

const DropZone = ({
  word = null,
  index,
  blockIndex,
  hide = false,
  disable = false,
  disableDrop = false,
  disableRemove = false,
  dragType,
  onDrop,
  onRemoveWord,
  onClick,
  isMobile = false,
  selectedWord = null,
  className = "",
  style = {}
}) => {
  const dropZoneRef = useRef(null);

  if (hide) return null;

  const handleDragOver = (e) => {
    if (disable || disableDrop) {
      return;
    }
    e.preventDefault();
  };

  const handleDrop = (e) => {
    if (disable || disableDrop) {
      e.preventDefault();
      return;
    }
    if (onDrop) {
      onDrop(dragType, index, blockIndex);
    }
  };

  const handleRemoveWord = () => {
    if (disable || disableRemove || !word) {
      return;
    }
    if (onRemoveWord) {
      onRemoveWord(index, dragType, blockIndex);
    }
  };

  // Handle mobile drop events
  useEffect(() => {
    const dropZone = dropZoneRef.current;
    if (!dropZone) return;

    const handleMobileDrop = (e) => {
      if (disable || disableDrop) return;

      const { draggedWord, dragType: sourceDragType } = e.detail;
      if (sourceDragType === dragType && onDrop) {
        onDrop(dragType, index, blockIndex);
      }
    };

    dropZone.addEventListener('mobileDrop', handleMobileDrop);
    return () => {
      dropZone.removeEventListener('mobileDrop', handleMobileDrop);
    };
  }, [disable, disableDrop, dragType, index, blockIndex, onDrop]);

  const getClassName = () => {
    let baseClass = "min-w-12 w-auto h-8 sm:min-w-16 sm:h-10 px-2 bg-[#FFFDE8] border border-dashed border-yellow-400 rounded-md flex items-center justify-center text-xs sm:text-sm transition-all duration-200";

    if (disable) {
      baseClass += " opacity-50";
    }

    if (isMobile && selectedWord && !word) {
      baseClass += " ring-2 ring-yellow-500 bg-yellow-50 cursor-pointer";
    } else if (isMobile && !word) {
      baseClass += " cursor-pointer hover:bg-yellow-50";
    }

    if (className) {
      baseClass += ` ${className}`;
    }

    return baseClass;
  };

  return (
    <div
      ref={dropZoneRef}
      data-drop-zone="true"
      onDragOver={handleDragOver}
      onDrop={handleDrop}
      onClick={word ? handleRemoveWord : onClick}
      className={getClassName()}
      style={style}
    >
      {word ? (
        <span className="select-none">
          {word} ×
        </span>
      ) : (
        <span className="text-gray-400 text-xs select-none">
          {isMobile && selectedWord ? 'Tap to place' : 'Drop here'}
        </span>
      )}
    </div>
  );
};

export default DropZone;
