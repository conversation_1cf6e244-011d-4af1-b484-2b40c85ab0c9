'use client';

import { useState, useEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { toast } from 'sonner';
import { usePathname, useRouter } from 'next/navigation';
import SkinPreview from '@/components/skin/SkinPreview';
import useDataFetch from '@/hooks/useDataFetch';
import api from '@/lib/api';
import { ButtonIcon } from '@/components/Button';
import EssayFeedBackModal from './_components/FeedbackModal';
import Tooltip from '@/components/Tooltip';
import {
  selectIsSkinModalOpen,
  setIsSkinModalOpen,
} from '@/store/features/diarySlice';
import SelectSkinModal from '../diary/_component/SelectSkinModal';

export default function WriteEssay() {
  const router = useRouter();
  const dispatch = useDispatch();
  const pathname = usePathname();

  // State management - all hooks declared at the top level
  const [isSaving, setIsSaving] = useState(false);
  const [modalData, setModalData] = useState(null);
  const [showError, setShowError] = useState(false);
  const [titleError, setTitleError] = useState(false);
  const [bodyError, setBodyError] = useState(false);
  const [selectedSkin, setSelectedSkin] = useState(null);
  const [subject, setSubject] = useState('');
  const [body, setBody] = useState('');
  const [wordCount, setWordCount] = useState(0);
  const [date, setDate] = useState(new Date().toISOString().slice(0, 10));
  const [autoSaveTimeout, setAutoSaveTimeout] = useState(null);
  const textareaRef = useRef(null);

  // New state for managing essay flow
  const [submissionId, setSubmissionId] = useState(null);
  const [skinInfo, setSkinInfo] = useState(null);
  const [submissionDetails, setSubmissionDetails] = useState(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitted, setIsSubmitted] = useState(false);
  // const [isEditing, setIsEditing] = useState(false);

  const isSkinModalOpen = useSelector(selectIsSkinModalOpen);

  // Always call useDataFetch hook - no conditional rendering
  const {
    data: activeEssay,
    isLoading: isActiveEssayLoading,
    refetch,
  } = useDataFetch({
    queryKey: ['active-essay'],
    endPoint: `/student-essay/activeEssay`,
  });

  // Initialize essay flow
  useEffect(() => {
    if (isActiveEssayLoading || isInitialized) return;

    const initializeEssay = async () => {
      try {
        setIsLoading(true);
        setIsSubmitted(false);

        if (activeEssay) {
          // Extract data from activeEssay response
          console.log('Active essay found:', activeEssay);

          // Set skin info from activeEssay
          if (activeEssay.diarySkin) {
            setSkinInfo(activeEssay.diarySkin);
            setSelectedSkin(activeEssay.diarySkin);
            dispatch(setIsSkinModalOpen(false));
          } else {
            // No skin available, force user to select one
            setSkinInfo(null);
            setSelectedSkin(null);
            dispatch(setIsSkinModalOpen(true));
          }

          // Set submission details
          setSubmissionDetails(activeEssay);
          setSubmissionId(activeEssay.id);
          const latestSubmission =
            activeEssay.submissionHistory[
              activeEssay.submissionHistory.length - 1
            ];

          // show edit button based on content
          // if (activeEssay?.title || latestSubmission?.content) {
          //   setIsEditing(false);
          // } else {
          //   setIsEditing(true);
          // }

          if (activeEssay.title)
            // Set content from existing submission
            setSubject(activeEssay.title);
          if (activeEssay.submissionHistory?.length > 0) {
            if (latestSubmission.content) setBody(latestSubmission.content);
            if (latestSubmission.submissionDate) {
              setDate(
                new Date(latestSubmission.submissionDate)
                  .toISOString()
                  .slice(0, 10)
              );
            }
          }
        } else {
          // Start new essay
          console.log('No active essay, starting new one');
          const response = await api.post('/student-essay/start/essay', {
            taskId: null,
          });

          console.log('Start essay response:', response);

          // Extract data from start essay response
          if (response.data) {
            setSubmissionId(response.data.id);
            // setIsEditing(true);

            // Set skin info from response
            if (response.data.skinInfo && response.data.diarySkin) {
              setSkinInfo(response.data.skinInfo);
              setSelectedSkin(response.data.diarySkin);
              dispatch(setIsSkinModalOpen(false));
            } else {
              // No skin available, force user to select one
              setSkinInfo(null);
              setSelectedSkin(null);
              dispatch(setIsSkinModalOpen(true));
            }

            setSubmissionDetails(response.data);
          }
        }

        setIsInitialized(true);
        setIsLoading(false);
      } catch (error) {
        console.error('Error initializing essay:', error);
        setIsInitialized(true);
        setIsLoading(false);
      }
    };

    initializeEssay();
  }, [isActiveEssayLoading, activeEssay, isInitialized]);

  // Word count calculation
  const countWords = (html) => {
    if (!html) return 0;
    const text = html.replace(/<[^>]*>/g, ' ');
    const cleanText = text.replace(/&nbsp;|&|<|>|"|'/g, ' ');
    return cleanText
      .trim()
      .split(/\s+/)
      .filter((word) => word.length > 0).length;
  };

  // Auto-focus textarea when isEditable is true
  // useEffect(() => {
  //   if (isEditing && textareaRef.current) {
  //     textareaRef.current.focus();
  //     // Set cursor to end of text
  //     const length = textareaRef.current.value.length;
  //     textareaRef.current.setSelectionRange(length, length);
  //   }
  // }, [isEditing]);

  useEffect(() => {
    setWordCount(countWords(body));
  }, [body]);

  // Auto-save functionality
  const handleAutoSave = async (content, title) => {
    if (!submissionId || isSubmitted) return;
    // !isEditing

    try {
      const payload = {
        submissionId: submissionId,
        skinId: selectedSkin?.id || null,
        title: title || subject,
        content: content || body,
      };

      await api.post('/student-essay/submit/essay/update', payload, {
        showSuccessToast: false,
      });
      console.log('Auto-saved successfully');
    } catch (error) {
      console.error('Auto-save error:', error);
    }

    // setAutoSaveTimeout(timeout);
  };

  useEffect(() => {
    const timeout = setTimeout(() => {
      handleAutoSave(body, subject);
    }, 800);

    return () => clearTimeout(timeout);
  }, [body, subject, selectedSkin]);

  const handleSave = async () => {
    // Reset error states
    setTitleError(false);
    setBodyError(false);
    setShowError(false);

    let hasError = false;

    // Validation for title and body
    if (!subject.trim()) {
      toast.error('Please enter an essay title');
      setTitleError(true);
      hasError = true;
    }

    if (!body.trim()) {
      toast.error('Please write essay content');
      setBodyError(true);
      hasError = true;
    }

    if (hasError) {
      return;
    }

    if (
      (submissionDetails?.task?.wordLimitMinimum
        ? wordCount < submissionDetails?.task?.wordLimitMinimum
        : wordCount < 0) ||
      (submissionDetails?.task?.wordLimitMaximum
        ? wordCount > submissionDetails?.task?.wordLimitMaximum
        : wordCount > Infinity)
    ) {
      setShowError(true);
      setBodyError(true);
      return;
    } else if (!selectedSkin?.id) {
      toast.error("Essay can't be submitted without a skin");
      return;
    }

    setIsSaving(true);

    const payload = {
      skinId: selectedSkin?.id || null,
      title: subject,
      content: body,
    };

    try {
      // Clear auto-save timeout before submission
      if (autoSaveTimeout) {
        clearTimeout(autoSaveTimeout);
        setAutoSaveTimeout(null);
      }

      refetch();
      await api.post('/student-essay/submit/essay', payload);
      setShowError(false);
      setSubject('');
      setBody('');
      setIsSubmitted(true);
      // refetch();
      // setIsEditing(false);
    } catch (error) {
      console.error('Error saving:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleSkinChange = async (newSkin) => {
    if (!newSkin.templateContent) {
      toast.error('This skin has no valid template content');
      return;
    }

    try {
      // Update both skin states
      setSkinInfo(newSkin);
      setSelectedSkin(newSkin);

      // Close the modal
      dispatch(setIsSkinModalOpen(false));

      toast.success(`Skin "${newSkin.name}" applied successfully`);
    } catch (error) {
      console.error('Error applying skin template:', error);
      toast.error('Failed to apply skin template');
    }
  };

  // Show loading while initializing
  if (isActiveEssayLoading || isLoading) {
    return (
      <div className="flex justify-center items-center py-20">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-yellow-500"></div>
      </div>
    );
  }

  // Show error if not initialized properly
  if (!isInitialized || !submissionId) {
    return (
      <div className="flex flex-col items-center justify-center py-20">
        <div className="text-center">
          <h2 className="text-2xl font-semibold text-gray-800 mb-4">
            Unable to Initialize Essay
          </h2>
          <p className="text-gray-600 mb-6">
            There was an error setting up your essay. Please try again.
          </p>
          <button
            onClick={() => window.location.reload()}
            className="bg-gradient-to-b from-yellow-300 to-yellow-500 hover:from-yellow-400 hover:to-yellow-600 text-black font-medium py-2 px-6 rounded-full transition-all duration-300"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  const isEditing = submissionDetails?.status !== 'submitted';
  const latestSubmission = submissionDetails?.submissionHistory?.at(-1) || {};

  return (
    <div className="max-w-7xl mx-auto p-5 xl:px-0 my-8 relative">
      <div className="p-2 bg-[#FDE7E9] grid grid-cols-1 lg:grid-cols-2 gap-3 rounded">
        <div className="bg-white rounded-lg text-center flex items-center relative py-4">
          {selectedSkin ? (
            <div className="space-y-5">
              <SkinPreview
                skin={
                  selectedSkin
                    ? selectedSkin?.templateContent
                    : skinInfo?.moduleDefaultSkin?.skin?.templateContent
                }
                contentData={{
                  subject,
                  body,
                  date,
                }}
              />
            </div>
          ) : (
            <div className="text-sm flex items-center justify-center w-full">
              No skin to preview
            </div>
          )}
        </div>

        {/* Example inputs for subject, body, and date */}
        <div className="bg-white rounded-lg p-3 space-y-3 w-full">
          <div className="flex flex-col p-2 shadow-lg border rounded-lg gap-3">
            <div className="flex items-center justify-between border-b border-dashed pb-1 gap-3">
              {/* {isEditing ? ( */}
              <input
                type="text"
                value={subject}
                autoComplete="off"
                autoCorrect="off"
                spellCheck="false"
                autoCapitalize="off"
                data-gramm="false"
                data-gramm_editor="false"
                data-enable-grammarly="false"
                onChange={(e) => setSubject(e.target.value)}
                placeholder="Enter essay title..."
                className={`p-2 mr-2 w-full rounded border ${
                  titleError ? 'border-red-500' : 'border-gray-200'
                } focus:outline-[1px] focus:outline-gray-200`}
              />
              {/* ) : (
                <h3 className="p-2 mr-2 w-full">{subject}</h3>
              )} */}
              <span className="min-w-24">{date}</span>
            </div>
            {/* {isEditing ? ( */}
            <textarea
              value={body}
              ref={textareaRef}
              autoComplete="off"
              autoCorrect="off"
              spellCheck="false"
              autoCapitalize="off"
              data-gramm="false"
              data-gramm_editor="false"
              data-enable-grammarly="false"
              onChange={(e) => setBody(e.target.value)}
              placeholder="Essay description"
              className={`border ${
                latestSubmission?.submissionMark ? 'h-80' : 'min-h-60'
              } ${
                bodyError ? 'border-red-500' : 'border-gray-300'
              } p-2 w-full rounded focus:outline-[1px] focus:outline-yellow-400 shadow-[inset_2px_2px_6px_0px_#0000001F]`}
              rows={4}
            />
            {/* ) : (
              <p
                className={`w-full rounded px-2 ${
                  latestSubmission?.submissionMark ? 'h-80' : 'min-h-60'
                }`}
              >
                {body}
              </p>
            )} */}

            <div className="flex items-center justify-between">
              {isEditing && (
                <div className="text-sm flex items-center justify-between min-w-28">
                  <span>{`${wordCount} / ${
                    submissionDetails?.task?.wordLimitMaximum || Infinity
                  } (word)`}</span>

                  {showError &&
                    wordCount < submissionDetails?.task?.wordLimitMinimum && (
                      <span className="text-red-500">
                        Minimum {submissionDetails?.task?.wordLimitMinimum}{' '}
                        words required.
                      </span>
                    )}
                </div>
              )}

              {/* Submit Button */}
              {submissionDetails?.status !== 'submitted' && (
                <div className="flex justify-end w-full">
                  <button
                    onClick={() => handleSave()}
                    disabled={isSaving}
                    className={`text-black font-medium py-1 px-5 text-sm text-center rounded-full whitespace-nowrap
                  border-2 border-yellow-100
                  shadow-[0px_4px_6px_-1px_rgba(0,0,0,0.1),0px_2px_4px_-2px_rgba(0,0,0,0.1),-4px_8px_12px_0px_#00000026]
                  transition-all duration-300
                  bg-gradient-to-b from-yellow-300 to-yellow-500 hover:from-yellow-400 hover:to-yellow-600
                  relative
                  ring-2 ring-[#A36105] ${
                    isSaving
                      ? 'bg-gray-300 cursor-not-allowed'
                      : 'bg-yellow-400 hover:bg-yellow-300'
                  }`}
                  >
                    {isSaving ? 'Submitting...' : 'Submit'}
                  </button>
                </div>
              )}
            </div>
          </div>

          {/* Only show correction section if there's feedback */}
          {latestSubmission?.submissionMark?.submissionFeedback && (
            <div className="min-h-40 max-h-72 overflow-y-auto shadow-lg border rounded-lg p-2 relative">
              <div className="h-full">
                <p className="text-sm text-[#864D0D] text-center font-medium mb-2">
                  Tutor Review Zone
                </p>
                <div className="flex justify-between items-center border-b-2 border-dashed border-gray-300 mb-3">
                  <h3 className="mb-2">{subject}</h3>
                  <div className="flex items-center gap-3 text-sm">{date}</div>
                </div>
                <div
                  className="min-h-28"
                  dangerouslySetInnerHTML={{
                    __html:
                      latestSubmission?.submissionMark?.submissionFeedback,
                  }}
                />
              </div>

              <span className="absolute right-2 bottom-2">
                <ButtonIcon
                  icon="tabler:message-2-star"
                  innerBtnCls={`h-12 w-12`}
                  btnIconCls={`h-5 w-5`}
                  onClick={() => setModalData(body ? body : ' ')}
                  aria-label=""
                />
              </span>
            </div>
          )}
        </div>
      </div>

      <EssayFeedBackModal
        isOpen={!!modalData}
        onClose={() => setModalData(null)}
        data={''}
      />

      {isEditing && (
        <>
          <div className="absolute top-5 -right-10">
            <div className="w-8 h-8 cursor-pointer">
              <Tooltip
                content={'Skin'}
                color="user"
                size="lg"
                delay={100}
                className="-ml-3 "
                position="right"
              >
                <ButtonIcon
                  icon={'arcticons:image-combiner'}
                  innerBtnCls="h-12 w-12"
                  btnIconCls="h-5 w-5"
                  aria-label={'Skin'}
                  onClick={() => dispatch(setIsSkinModalOpen(true))}
                />
              </Tooltip>
            </div>
          </div>

          {isSkinModalOpen && (
            <SelectSkinModal
              isOpen={isSkinModalOpen}
              onClose={() => dispatch(setIsSkinModalOpen(false))}
              onApply={handleSkinChange}
              currentSkinId={selectedSkin?.id || skinInfo?.id}
            />
          )}
        </>
      )}
    </div>
  );
}
