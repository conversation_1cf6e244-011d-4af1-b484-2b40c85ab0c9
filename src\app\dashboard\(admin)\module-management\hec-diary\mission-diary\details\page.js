'use client';
import React, { useState } from 'react';
import { Icon } from '@iconify/react';
import Image from 'next/image';

const ViewModal = ({ isOpen, onClose, mission }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  if (!isOpen || !mission) return null;

  // Format date to readable format
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const toggleExpand = () => {
    setIsExpanded(!isExpanded);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div
        className={`bg-white rounded-lg shadow-xl w-full overflow-hidden flex flex-col transition-all duration-300 ${
          isExpanded
            ? 'max-w-4xl h-[90vh] max-h-[90vh]'
            : 'max-w-2xl h-full max-h-[600px]'
        }`}
      >
        {/* Header - Fixed */}
        <div className="bg-[#FFF9FB] py-3 px-6 border-b border-amber-200 relative flex-shrink-0">
          {/* Action Buttons */}
          <div className="absolute top-3 right-4 z-10 flex items-center space-x-2">
            {/* Expand/Collapse Button */}
            <button
              type="button"
              onClick={toggleExpand}
              className="focus:outline-none hover:bg-gray-100 rounded-full p-1 transition-colors"
              aria-label={isExpanded ? 'Collapse' : 'Expand'}
              title={isExpanded ? 'Collapse' : 'Expand'}
            >
              <Icon
                icon={isExpanded ? 'mdi:fullscreen-exit' : 'mdi:fullscreen'}
                className="w-6 h-6 text-gray-600"
              />
            </button>

            {/* Close Button */}
            <button
              type="button"
              onClick={onClose}
              className="focus:outline-none"
              aria-label="Close"
            >
              <Image
                src="/assets/images/all-img/cross-bg.png"
                alt="Close"
                width={32}
                height={32}
                className="w-8 h-8"
              />
            </button>
          </div>

          <div className="flex justify-center">
            <div className="relative w-full max-w-xs">
              <Image
                src="/assets/images/all-img/missiondiary-bg.png"
                alt="Mission Details"
                width={300}
                height={100}
                className="w-full h-auto"
                priority
              />
            </div>
          </div>
        </div>

        {/* Content Section - Scrollable */}
        <div className="px-6 py-4 flex-1 overflow-y-auto">
          <h1
            className={`font-semibold mb-4 text-gray-800 ${
              isExpanded ? 'text-2xl' : 'text-lg'
            }`}
          >
            {mission.title}
          </h1>

          <div className={`space-y-4 ${isExpanded ? 'max-w-4xl mx-auto' : ''}`}>
            {/* Status Section */}
            <div className="flex items-center justify-between py-3 border-b border-gray-200">
              <div className="flex items-center">
                <Icon
                  icon={mission.isActive ? 'mdi:check-circle' : 'mdi:cancel'}
                  className={`mr-2 ${
                    mission.isActive ? 'text-green-500' : 'text-red-500'
                  } ${isExpanded ? 'w-5 h-5' : 'w-4 h-4'}`}
                />
                <span
                  className={`font-medium text-gray-700 ${
                    isExpanded ? 'text-base' : 'text-sm'
                  }`}
                >
                  Status
                </span>
              </div>
              <span
                className={`px-2 py-1 rounded-full font-medium ${
                  isExpanded ? 'text-sm px-3 py-2' : 'text-xs'
                } ${
                  mission.isActive
                    ? 'bg-green-100 text-green-800'
                    : 'bg-red-100 text-red-800'
                }`}
              >
                {mission.isActive ? 'Active' : 'Inactive'}
              </span>
            </div>

            {/* Word Count Section */}
            <div className="space-y-2">
              <h3
                className={`font-medium text-gray-700 flex items-center ${
                  isExpanded ? 'text-base' : 'text-sm'
                }`}
              >
                <Icon
                  icon="mdi:format-text"
                  className={`mr-2 text-blue-500 ${
                    isExpanded ? 'w-5 h-5' : 'w-4 h-4'
                  }`}
                />
                Word Count Requirements
              </h3>
              <div
                className={`ml-6 grid gap-4 ${
                  isExpanded
                    ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-4'
                    : 'grid-cols-2'
                }`}
              >
                <div className="flex flex-col">
                  <span
                    className={`text-gray-600 mb-1 ${
                      isExpanded ? 'text-sm' : 'text-xs'
                    }`}
                  >
                    Minimum Words
                  </span>
                  <span
                    className={`font-semibold text-gray-900 ${
                      isExpanded ? 'text-base' : 'text-sm'
                    }`}
                  >
                    {mission.targetWordCount || 'N/A'}
                  </span>
                </div>
                <div className="flex flex-col">
                  <span
                    className={`text-gray-600 mb-1 ${
                      isExpanded ? 'text-sm' : 'text-xs'
                    }`}
                  >
                    Maximum Words
                  </span>
                  <span
                    className={`font-semibold text-gray-900 ${
                      isExpanded ? 'text-base' : 'text-sm'
                    }`}
                  >
                    {mission.targetMaxWordCount || 'N/A'}
                  </span>
                </div>
              </div>
            </div>

            {/* Timeline Section */}
            <div className="space-y-2">
              <h3
                className={`font-medium text-gray-700 flex items-center ${
                  isExpanded ? 'text-base' : 'text-sm'
                }`}
              >
                <Icon
                  icon="mdi:calendar-clock"
                  className={`mr-2 text-purple-500 ${
                    isExpanded ? 'w-5 h-5' : 'w-4 h-4'
                  }`}
                />
                Timeline
              </h3>
              <div
                className={`ml-6 grid gap-4 ${
                  isExpanded
                    ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-4'
                    : 'grid-cols-2'
                }`}
              >
                <div className="flex flex-col">
                  <span
                    className={`text-gray-600 mb-1 ${
                      isExpanded ? 'text-sm' : 'text-xs'
                    }`}
                  >
                    Start Date
                  </span>
                  <span
                    className={`font-semibold text-gray-900 ${
                      isExpanded ? 'text-base' : 'text-sm'
                    }`}
                  >
                    {formatDate(mission.publishDate)}
                  </span>
                </div>
                <div className="flex flex-col">
                  <span
                    className={`text-gray-600 mb-1 ${
                      isExpanded ? 'text-sm' : 'text-xs'
                    }`}
                  >
                    End Date
                  </span>
                  <span
                    className={`font-semibold text-gray-900 ${
                      isExpanded ? 'text-base' : 'text-sm'
                    }`}
                  >
                    {formatDate(mission.expiryDate)}
                  </span>
                </div>
              </div>
            </div>

            {/* Score Section (if available) */}
            {mission.score !== undefined && (
              <div className="space-y-2">
                <h3
                  className={`font-medium text-gray-700 flex items-center ${
                    isExpanded ? 'text-base' : 'text-sm'
                  }`}
                >
                  <Icon
                    icon="mdi:star"
                    className={`mr-2 text-yellow-500 ${
                      isExpanded ? 'w-5 h-5' : 'w-4 h-4'
                    }`}
                  />
                  Score
                </h3>
                <div className="ml-6">
                  <span
                    className={`font-semibold text-gray-900 ${
                      isExpanded ? 'text-2xl' : 'text-lg'
                    }`}
                  >
                    {mission.score}
                  </span>
                  <span
                    className={`text-gray-600 ml-1 ${
                      isExpanded ? 'text-sm' : 'text-xs'
                    }`}
                  >
                    points
                  </span>
                </div>
              </div>
            )}

            <div className='flex flex-col sm:flex-row'>
              {/* Description Section */}
              <div className="space-y-2 w-1/2">
                <h3
                  className={`font-medium text-gray-700 flex items-center ${
                    isExpanded ? 'text-base' : 'text-sm'
                  }`}
                >
                  <Icon
                    icon="mdi:text-box"
                    className={`mr-2 text-green-500 ${
                      isExpanded ? 'w-5 h-5' : 'w-4 h-4'
                    }`}
                  />
                  Description
                </h3>
                <div className="ml-6">
                  <p
                    className={`text-gray-900 leading-relaxed ${
                      isExpanded ? 'text-base leading-loose' : 'text-sm'
                    }`}
                  >
                    {mission.description || 'No description provided'}
                  </p>
                </div>
              </div>

              {/* Description Section */}
              <div className="space-y-2 w-1/2">
                <h3
                  className={`font-medium text-gray-700 flex items-center ${
                    isExpanded ? 'text-base' : 'text-sm'
                  }`}
                >
                  <Icon
                    icon="material-symbols:category-outline"
                    className={`mr-2 text-orange-500 ${
                      isExpanded ? 'w-5 h-5' : 'w-4 h-4'
                    }`}
                  />
                  Category
                </h3>
                <div className="ml-6">
                  <p
                    className={`text-gray-900 leading-relaxed ${
                      isExpanded ? 'text-base leading-loose' : 'text-sm'
                    }`}
                  >
                    {mission?.category?.name || 'No description provided'}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ViewModal;
