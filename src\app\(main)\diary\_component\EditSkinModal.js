'use client';

import Modal from '@/components/Modal';
import CreateSkin from '@/components/SkinManagement/add/CreateSkin';
import CreateSkinLayout from '@/components/SkinManagement/add/CreateSkinLayout';
import EditSkin from '@/components/SkinManagement/edit/EditSkin';
import EditSkinLayout from '@/components/SkinManagement/edit/EditSkinLayout';
import { setEditSkinModalId } from '@/store/features/commonSlice';
import React, { use } from 'react';
import { useDispatch, useSelector } from 'react-redux';

const EditSkinModal = () => {
  const dispatch = useDispatch();
  const { editSkinModalId } = useSelector((state) => state.common);

  const onClose = () => {
    dispatch(setEditSkinModalId(null));
  };
  return (
    <div>
      <Modal
        isOpen={editSkinModalId}
        onClose={onClose}
        title="Edit Skin"
        width="7xl"
      >
        <EditSkinLayout children={<EditSkin editId={editSkinModalId} />} />
      </Modal>
    </div>
  );
};

export default EditSkinModal;
