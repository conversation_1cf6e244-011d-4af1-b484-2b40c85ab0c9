import { ButtonIcon } from '@/components/Button';
import Image from 'next/image';
import React, { useState } from 'react';
import { Formik, Form, Field, ErrorMessage } from 'formik';
import * as Yup from 'yup';
import api from '@/lib/api';
import FormInput from '@/components/form/FormInput';

const SuggestTopicSection = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const validationSchema = Yup.object({
    description: Yup.string()
      .required('Please enter details.')
      .test('max-words', 'Maximum 15 words allowed', (value) => {
        if (!value) return true;
        const wordCount = value.trim().split(/\s+/).length;
        return wordCount <= 15;
      }),
  });
  
  const handleSubmit = async (values, { resetForm }) => {
    try {
      const res = await api.post('/student/novel/suggestions', values);

      resetForm();
      setIsModalOpen(false);
    } catch (error) {
      console.error('Error:', error);
    }
  };

  return (
    <div>
      <div className="shadow-[-2px_-2px_8px_0px_#B7212626_inset] rounded-xl p-5 bg-[#FFF9FB] border border-[#FDE7E9]">
        <div className="space-y-3 p-5 border border-[#FDE7E9] rounded-xl bg-[#FFF9FB] text-gray-600">
          <h1 className="text-2xl text-yellow-800 font-semibold">
            Hello English Coaching Novel Open Projects
          </h1>
          <div>
            <h4>Instruction: </h4>
            <p>1. Suggest a topic for Topic Contest.</p>
          </div>

          <button
            onClick={() => setIsModalOpen(true)}
            className={` text-black font-medium py-2 px-8 text-center rounded-full whitespace-nowrap
            border-2 border-yellow-100
            shadow-[0px_4px_6px_-1px_rgba(0,0,0,0.1),0px_2px_4px_-2px_rgba(0,0,0,0.1),-4px_8px_12px_0px_#00000026]
            transition-all duration-300
            bg-gradient-to-b from-yellow-300 to-yellow-500 hover:from-yellow-400 hover:to-yellow-600
            relative pr-8
            ring-2 ring-[#A36105]`}
          >
            Suggest a Topic
          </button>
        </div>
      </div>

      {isModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-xl overflow-hidden">
            <div className="relative">
              <div className="absolute z-10 top-2 right-3">
                <ButtonIcon
                  icon="mdi:close"
                  innerBtnCls="h-8 w-8"
                  btnIconCls="h-5 w-5"
                  onClick={() => setIsModalOpen(false)}
                  aria-label="Close modal"
                />
              </div>

              <div className="bg-[#FFF6EF] p-6 relative shadow-lg text-center">
                <Image
                  src={'/assets/images/all-img/woodFrame.png'}
                  alt="Suggest Topic"
                  width={600}
                  height={200}
                  className="max-w-96 mx-auto h-auto"
                  priority
                />

                <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 mt-6 bg-[#FCF8EF] rounded-lg p-5 px-4 w-[55%]">
                  <h2 className="text-2xl font-bold text-yellow-900 font-serif">
                    Topic Suggestion
                  </h2>
                </div>
              </div>

              <div className="p-3">
                <Formik
                  initialValues={{ description: '' }}
                  validationSchema={validationSchema}
                  onSubmit={handleSubmit}
                >
                  {({ isSubmitting }) => (
                    <Form className="space-y-4">
                      <FormInput
                        name="description"
                        isTextarea={true}
                        placeholder="Write your suggestion here..."
                      />

                      <div className="text-center pb-3">
                        <button
                          type="submit"
                          className={` text-black font-medium py-2 px-8 text-center rounded-full whitespace-nowrap
                            border-2 border-yellow-100
                            shadow-[0px_4px_6px_-1px_rgba(0,0,0,0.1),0px_2px_4px_-2px_rgba(0,0,0,0.1),-4px_8px_12px_0px_#00000026]
                            transition-all duration-300
                            bg-gradient-to-b from-yellow-300 to-yellow-500 hover:from-yellow-400 hover:to-yellow-600
                            relative pr-8
                            ring-2 ring-[#A36105]`}
                        >
                          {isSubmitting ? 'Submitting...' : 'Submit'}
                        </button>
                      </div>
                    </Form>
                  )}
                </Formik>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SuggestTopicSection;
