'use client';
import Button, { ButtonIcon } from '@/components/Button';
import <PERSON><PERSON>iewer from '@/components/EditorViewer';
import DetailsModal from '@/components/form/modal/MissionConfirmationModal';
import SimpleTiptapEditor from '@/components/form/SimpleTiptapEditor';
import GoBack from '@/components/shared/GoBack';
import useDataFetch from '@/hooks/useDataFetch';
import api from '@/lib/api';

import { Form, Formik } from 'formik';
import Image from 'next/image';
import React, { useEffect, useRef, useState, useCallback } from 'react';
import { useSelector } from 'react-redux';

const CommonQnA = ({ getEndPoint, submitEndPoint, updateEndPoint }) => {
  const editorRef = useRef(null);
  const updateTimeoutRef = useRef(null);
  const [value, setValue] = useState('');
  const { user } = useSelector((state) => state.auth);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);

  const { data: questionDetails, refetch } = useDataFetch({
    queryKey: [`question-info`, getEndPoint],
    endPoint: getEndPoint,
  });

  const showSubmission = questionDetails?.submission?.answer && !isSubmitted && questionDetails?.is_played;
  const showFeedback =
    questionDetails?.submission?.answer &&
    questionDetails?.submission?.feedback;

  // Count words in HTML content
  const countWords = (html) => {
    if (!html) return 0;
    // Remove HTML tags
    const text = html.replace(/<[^>]*>/g, ' ');
    // Remove entities
    const cleanText = text.replace(/&nbsp;|&amp;|&lt;|&gt;|&quot;|&#39;/g, ' ');
    // Remove extra spaces and split by whitespace
    const words = cleanText
      .trim()
      .split(/\s+/)
      .filter((word) => word.length > 0);
    return words.length;
  };

  const handleSubmit = async (values) => {
    try {
      // Check minimum word count
      const wordCount = countWords(values.answer);
      const minimumWords = questionDetails?.question?.minimumWords || 0;

      const response = await api.post(submitEndPoint, values);
      console.log(response);
      setIsSubmitted(true);
    } catch (error) {
      console.log(error);
    }
  };

  const handleUpdate = useCallback(
    async (values) => {
      try {
        // Ensure we have the answer value
        if (!values || !values.answer || !updateEndPoint) return;

        // Clear any existing timeout
        if (updateTimeoutRef.current) {
          clearTimeout(updateTimeoutRef.current);
        }

        // If it's an explicit submission (not auto-save), update immediately
        if (!values._autoSave) {
          const response = await api.put(
            updateEndPoint,
            {
              content: values.answer,
            },
            { showSuccessToast: false }
          );

          console.log('Saved:', response);
          refetch();
          setIsSubmitted(false);
          return;
        }

        // For auto-save, use debouncing
        try {
          const response = await api.put(
            updateEndPoint,
            {
              content: values.answer,
            },
            { showSuccessToast: false }
          );

          console.log('Auto-saved (debounced):', response);
        } catch (error) {
          console.error('Error auto-saving submission:', error);
        }
      } catch (error) {
        console.error('Error updating submission:', error);
      }
    },
    [updateEndPoint, refetch]
  );

  // Handle component unmount - save draft
  useEffect(() => {
    return () => {
      // Save draft when navigating away if there's content
      if (value && !isSubmitted && questionDetails?.id && updateEndPoint) {
        // Clear any existing timeout to ensure we don't have multiple calls
        if (updateTimeoutRef.current) {
          clearTimeout(updateTimeoutRef.current);
        }

        // Immediately save without debouncing when unmounting
        api
          .put(
            updateEndPoint,
            {
              content: value,
            },
            { showSuccessToast: false }
          )
          .catch((error) => {
            console.error('Error saving on unmount:', error);
          });
      }
    };
  }, [value, isSubmitted, questionDetails?.id, updateEndPoint]);

  return (
    <div className="relative">
      <div className="max-w-7xl mx-auto px-5 xl:px-0 relative z-10">
        <GoBack title={'HEC Play'} linkClass="my-5 mb-8 w-full max-w-40" />

        <div className="p-5 rounded-lg bg-[#FFF9FB] shadow-lg space-y-5 mb-10">
          <div className="p-5 bg-[#FCF8EF] rounded-lg [box-shadow:2px_2px_12px_0px_#F5D1B066_inset,_-2px_-2px_12px_0px_#F5D1B066_inset]">
            <h1 className="text-2xl text-yellow-800 font-semibold">
              {questionDetails?.title}
            </h1>
            <div className="flex items-start gap-6">
              {questionDetails?.picture && (
                <Image
                  src={questionDetails?.picture}
                  alt={questionDetails?.title}
                  width={200}
                  height={200}
                />
              )}
              <div>
                {(questionDetails?.instructions ||
                  questionDetails?.description) && (
                  <p className="font-semibold text-lg text-gray-700">
                    Instruction:
                  </p>
                )}
              </div>
            </div>
            <EditorViewer
              data={
                questionDetails?.instructions || questionDetails?.description
              }
            />
          </div>

          {showSubmission ? (
            <div className="space-y-3">
              <div className="p-5 shadow-lg rounded-lg border relative bg-white">
                <h1 className="text-xl text-yellow-800 font-semibold">
                  My Answer
                </h1>
                <EditorViewer
                  data={
                    questionDetails?.submission?.answer?.length > 200
                      ? questionDetails?.submission?.answer?.slice(0, 400) +
                        '...'
                      : questionDetails?.submission?.answer
                  }
                />

                <div className="absolute right-2 top-2">
                  <ButtonIcon
                    icon={'ri:edit-2-fill'}
                    innerBtnCls={'h-10 w-10'}
                    btnIconCls={'h-5 w-5'}
                    onClick={() => setIsSubmitted(true)}
                  />
                </div>
              </div>

              <div className="p-5 shadow-lg rounded-lg border relative bg-white">
                <h1 className="text-xl text-yellow-800 font-semibold">
                  Tutor Correction Zone
                </h1>
                {showFeedback && (
                  <>
                    {questionDetails?.submission?.corrections?.grammar?.length >
                      0 && (
                      <div className="rounded-md">
                        <ul className=" text-sm text-gray-800">
                          {questionDetails.submission.corrections.grammar.map(
                            (item, index) => (
                              <EditorViewer key={index} data={item} />
                            )
                          )}
                        </ul>
                      </div>
                    )}

                    <div className="absolute right-2 top-2">
                      <ButtonIcon
                        icon={'arcticons:feedback-2'}
                        innerBtnCls={'h-10 w-10'}
                        btnIconCls={'h-4 w-4'}
                        onClick={() => setShowDetailsModal(true)}
                      />
                    </div>
                  </>
                )}

                <p
                  className={`${
                    !(questionDetails?.submission?.status === 'reviewed') &&
                    'text-red-600'
                  } text-center mt-2`}
                >
                  {!(questionDetails?.submission?.status === 'reviewed') &&
                    'Not Confirmed yet'}
                </p>
              </div>
            </div>
          ) : (
            <Formik
              initialValues={{
                content: value || questionDetails?.submission?.answer || '',
                story_maker_id: questionDetails?.id,
              }}
              onSubmit={
                questionDetails?.submission?.answer && updateEndPoint
                  ? handleUpdate
                  : handleSubmit
              }
              enableReinitialize
            >
              {() => (
                <Form>
                  <SimpleTiptapEditor
                    name="answer"
                    editorRef={editorRef}
                    initialValue={questionDetails?.submission?.answer || ''}
                    onAutoSave={(content) => {
                      setValue(content);
                      handleUpdate({ answer: content, _autoSave: true });
                    }}
                    setValue={setValue}
                    maxWords={
                      questionDetails?.targetWordCount
                        ? questionDetails?.targetWordCount
                        : 500
                    }
                  />

                  <div className="flex justify-center mt-3 gap-3">
                    <Button
                      buttonText="Cancel"
                      type="button"
                      onClick={() => setIsSubmitted(false)}
                    />
                    <Button
                      buttonText={
                        questionDetails?.submission?.answer
                          ? 'Update'
                          : 'Submit'
                      }
                      type="submit"
                      className="bg-yellow-400 hover:bg-yellow-500 text-black"
                    />
                  </div>
                </Form>
              )}
            </Formik>
          )}
        </div>
      </div>

      <DetailsModal
        isOpen={showDetailsModal}
        onClose={() => setShowDetailsModal(false)}
        data={questionDetails?.submission?.corrections?.grammar}
        title="Teachers Feedback"
        link={`/answer`}
        showBtn={false}
      />
    </div>
  );
};

export default CommonQnA;
