import React from 'react';
import Image from 'next/image';

const ShowMission = ({ mission, weeklyMission, onTaskClick }) => {
  return (
    <div
      key={mission.id}
      className="py-5 max-w-[377px] mx-auto space-y-8 mb-10"
    >
      <div className="flex justify-center pb-3">
        <span className="bg-yellow-200 min-w-52 py-2 px-4 text-center text-2xl rounded-lg">
          {weeklyMission
            ? `Week ${mission.sequenceNumber}`
            : `Month ${mission.sequenceNumber}`}
        </span>
      </div>

      {/* Map through each task in the mission */}
      {mission.tasks.map((task, taskIdx) => {
        // Get progress from task submissions
        const taskProgress = task.progress || 0; //task.submissions?.[0]?.firstRevisionProgress ||
        const progressPercent = `${taskProgress}%`;

        // Create a unique index for this task
        const idx = taskIdx;

        return (
          <div key={task.id}>
            <div className="relative w-full">
              <div
                className={`flex items-center gap-2 ${
                  idx % 2 == 0
                    ? 'justify-start'
                    : 'justify-start flex-row-reverse'
                } w-full`}
              >
                <div
                  onClick={() => onTaskClick(task)}
                  className="h-20 w-20 rounded-full cursor-pointer relative shadow-[0_8px_20px_rgba(0,0,0,0.2)] hover:shadow-[0_10px_25px_rgba(0,0,0,0.25)] active:shadow-[0_4px_12px_rgba(0,0,0,0.15)] transform hover:-translate-y-1 active:translate-y-0 transition-all duration-150"
                  style={{
                    background: `
                            conic-gradient(
                              ${taskProgress === 100 ? '#22c55e' : '#92400e'} ${
                      taskProgress * 3.6
                    }deg, 
                              #FFDE34 ${taskProgress * 3.6}deg
                            ),
                            radial-gradient(circle at 30% 30%, rgba(255,255,255,0.8) 0%, transparent 40%)
                          `,
                    boxShadow: `
                            inset 0 2px 4px rgba(255,255,255,0.6), 
                            inset 0 -2px 4px rgba(0,0,0,0.2), 
                            0 6px 12px rgba(0,0,0,0.15),
                            0 0 0 2px rgba(255,215,0,0.3)
                          `,
                  }}
                >
                  <div className="absolute top-1 left-1 right-1 bottom-1 bg-white rounded-full flex items-center justify-center">
                    <div
                      className={`h-[95%] w-[95%] rounded-full flex items-center justify-center text-xs font-semibold shadow-lg ${
                        idx == 0 && taskProgress == 0 ? 'animate-scale' : ''
                      }`}
                      style={{
                        backgroundColor:
                          taskProgress === 100 ? '#dbffe8ff' : '#fefce8',
                        color: taskProgress === 100 ? '#15803d' : '#92400e',
                        animationIterationCount:
                          idx == 0 && taskProgress == 0 ? 'infinite' : 'unset',
                      }}
                    >
                      <Image
                        alt="mission icon"
                        src={
                          idx == 0 && taskProgress == 0
                            ? '/assets/images/all-img/missionStartIcon.png'
                            : '/assets/images/all-img/missionIcon.png'
                        }
                        height={60}
                        width={60}
                        className={`max-h-9 max-w-9 ${
                          idx == 0 && taskProgress == 0 ? 'animate-pulse' : ''
                        }`}
                      />
                    </div>
                  </div>
                </div>

                <div>
                  <p
                    className="font-semibold hover:underline cursor-pointer"
                    onClick={() => onTaskClick(task)}
                  >
                    {task.title}
                  </p>
                  {taskProgress === 100 ? (
                    <p className={idx % 2 === 0 ? 'text-left' : 'text-right'}>
                      Completed
                    </p>
                  ) : taskProgress < 1 ? (
                    <p className={idx % 2 === 0 ? 'text-left' : 'text-right'}>
                      Start
                    </p>
                  ) : (
                    <p className={idx % 2 === 0 ? 'text-left' : 'text-right'}>
                      In-progress
                    </p>
                  )}
                </div>
              </div>

              {/* Only show connecting line if not the last task */}
              {!(taskIdx === mission.tasks.length - 1) && (
                <Image
                  src={
                    idx % 2 == 0
                      ? '/assets/images/all-img/mission-shape2.png'
                      : '/assets/images/all-img/mission-shape1.png'
                  }
                  alt={'arrow'}
                  width={280}
                  height={20}
                  className={`absolute max-w-[70%] sm:w-full left-1/2 -translate-x-1/2 ${
                    idx % 2 == 0 ? '-bottom-5' : '-bottom-6'
                  }`}
                />
              )}
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default ShowMission;
