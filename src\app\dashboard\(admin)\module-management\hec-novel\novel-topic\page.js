'use client';
import React from 'react';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import { useMutation, useQueryClient, useQuery } from '@tanstack/react-query';
import { toast } from 'sonner';
import FormInput from '@/components/form/FormInput';
import api from '@/lib/api';
import useDataFetch from '@/hooks/useDataFetch';

const CreateNovelTopics = () => {
  const queryClient = useQueryClient();

  // Alternative: Use React Query directly instead of useDataFetch
  const { 
    data: categoriesResponse, 
    isLoading: loadingCategories, 
    refetch: refetchCategories, 
    error: queryError 
  } = useQuery({
    queryKey: ['novel-categories'],
    queryFn: async () => {
      const response = await api.get('/admin/novel/categories');
      return response.data;
    },
    enabled: true
  });

  // Extract categories from the response data with better error handling
  let categories = [];
  if (categoriesResponse) {
    if (categoriesResponse.data && Array.isArray(categoriesResponse.data)) {
      categories = categoriesResponse.data;
    }
    else if (Array.isArray(categoriesResponse)) {
      categories = categoriesResponse;
    }
    else if (categoriesResponse.categories && Array.isArray(categoriesResponse.categories)) {
      categories = categoriesResponse.categories;
    }
  }
  
  const categoriesError = queryError || (categoriesResponse && categoriesResponse.success === false);

  // Define validation schema
  const validationSchema = Yup.object().shape({
    title: Yup.string()
      .trim()
      .min(3, 'Title must be at least 3 characters')
      .max(200, 'Title must not exceed 200 characters')
      .required('Title is required'),
    sequenceTitle: Yup.string()
      .trim()
      .min(3, 'Sequence Title must be at least 3 characters')
      .max(100, 'Sequence Title must not exceed 100 characters')
      .required('Sequence Title is required'),
    category: Yup.string().required('Category is required'),
    instruction: Yup.string()
      .trim()
      // .min(10, 'Instruction must be at least 10 characters')
      .max(1000, 'Instruction must not exceed 1000 characters')
      .required('Instruction is required'),
    isActive: Yup.boolean().required('Active status is required')
  });

  // Define initial values - Added isActive field
  const initialValues = {
    title: '',
    sequenceTitle: '',
    category: '',
    instruction: '',
    isActive: true // Default to active
  };

  // Create mutation for API call
  const createNovelTopicMutation = useMutation({
    mutationFn: (topicData) => {
      return api.post('/admin/novel/topics', topicData);
    },
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ['novel-topics'] }); 
    },
    onError: (error) => {
      const errorMessage = error?.response?.data?.message || 'Failed to create novel topic'; 
      console.log(errorMessage)      
      console.error('Create novel topic error:', error);
    }
  });

  // Handle form submission
  const handleSubmit = (values, { setSubmitting, resetForm }) => {
    // Include isActive in the payload
    const payload = {
      title: values.title.trim(),
      sequenceTitle: values.sequenceTitle.trim(),
      category: values.category,
      instruction: values.instruction.trim(),
      isActive: values.isActive // Add this field
    };

    createNovelTopicMutation.mutate(payload, {
      onSettled: () => {
        setSubmitting(false);
      },
      onSuccess: () => {
        resetForm();
      }
    });
  };

  return (
    <div className='min-h-screen bg-white p-5'>
      <div className="max-w-4xl mx-auto">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-800">Create Novel Topic</h1>
          <p className="text-gray-600 mt-2">Create a new novel topic for users</p>
        </div>

        <Formik
          initialValues={initialValues}
          validationSchema={validationSchema}
          onSubmit={handleSubmit}
          validateOnChange={true}
          validateOnBlur={true}
        >
          {({ isSubmitting, errors, touched, resetForm, values, setFieldValue }) => (
            <Form className="bg-white p-6 rounded-lg shadow-md border border-gray-200">
              <div>
                {/* Category Field */}
                <div className="mb-6">
                  <div className="bg-[#FFFAC2] rounded-lg p-4 max-w-lg mx-auto">
                    <label htmlFor="category" className="block text-sm font-semibold text-black mb-2">
                      Time Frequency<span className="text-red-600">*</span>
                    </label>
                    <div className="relative">
                      <select
                        id="category"
                        name="category"
                        value={values.category}
                        onChange={(e) => setFieldValue('category', e.target.value)}
                        className={`w-full text-base rounded-md border ${
                          errors.category && touched.category ? 'border-red-500' : 'border-gray-300'
                        } px-4 py-3 appearance-none bg-white focus:outline-none focus:ring-1 focus:ring-yellow-400`}
                        disabled={loadingCategories}
                      >
                        <option value="">
                          {loadingCategories ? 'Loading time frequency...' : 'Select Time Frequency'}
                        </option>
                        {!loadingCategories && categories.length > 0 && categories.map((category) => (
                          <option key={category.value} value={category.value}>
                            {category.description}
                          </option>
                        ))}
                      </select>
                      <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                        <svg className="fill-current h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                          <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
                        </svg>
                      </div>
                    </div>
                   
                    {categoriesError && !loadingCategories && (
                      <div className="text-red-500 text-sm mt-1">
                        Failed to load categories. 
                        <button 
                          type="button" 
                          onClick={() => refetchCategories()} 
                          className="text-blue-500 underline ml-1 hover:text-blue-700"
                        >
                          Retry
                        </button>
                      </div>
                    )}
                    {!loadingCategories && !categoriesError && categories.length === 0 && (
                      <div className="text-yellow-600 text-sm mt-1">
                        No categories available.
                      </div>
                    )}
                  </div>
                </div>

                {/* Sequence Title Field */}
                <div className="mb-6">
                  <label htmlFor="sequenceTitle" className="block text-sm font-semibold text-black mb-2">
                    Sequence Title <span className="text-red-600">*</span>
                  </label>
                  <FormInput
                    type="text"
                    name="sequenceTitle"
                    id="sequenceTitle"
                    placeholder="Enter sequence title (e.g., Topic 1, Chapter 1)"
                    className={`w-full text-base rounded-md border ${
                      errors.sequenceTitle && touched.sequenceTitle ? 'border-red-500' : 'border-gray-300'
                    } px-4 py-3 focus:outline-none focus:ring-1 focus:ring-yellow-400 transition-colors`}
                  />
                  
                </div>

                {/* Title Field */}
                <div className="mb-6">
                  <label htmlFor="title" className="block text-sm font-semibold text-black mb-2">
                    Title <span className="text-red-600">*</span>
                  </label>
                  <FormInput
                    type="text"
                    name="title"
                    id="title"
                    placeholder="Enter the main title (e.g., Adventure in the Enchanted Forest)"
                    className={`w-full text-base rounded-md border ${
                      errors.title && touched.title ? 'border-red-500' : 'border-gray-300'
                    } px-4 py-3 focus:outline-none focus:ring-1 focus:ring-yellow-400 transition-colors`}
                  />
                  
                </div>

                {/* Instruction Field */}
                <div className="mb-6">
                  <label htmlFor="instruction" className="block text-sm font-semibold text-black mb-2">
                    Instruction <span className="text-red-600">*</span>
                  </label>
                  <textarea
                    name="instruction"
                    id="instruction"
                    value={values.instruction}
                    onChange={(e) => setFieldValue('instruction', e.target.value)}
                    placeholder="Write a creative story about friendship and adventure..."
                    rows={6}
                    className={`w-full p-3 border ${
                      errors.instruction && touched.instruction ? 'border-red-500' : 'border-gray-300'
                    } rounded-lg resize-vertical focus:outline-none focus:ring-1 focus:ring-yellow-300 transition-colors`}
                  />
                 
                  <div className="text-sm text-gray-500 mt-2">
                    {values.instruction.length}/1000 characters
                  </div>
                </div>

                {/* Active Status Field */}
                <div className="mb-6">
                  <label className="block text-sm font-semibold text-black mb-2">
                    Status <span className="text-red-600">*</span>
                  </label>
                  <div className="flex items-center space-x-4">
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="isActive"
                        value="true"
                        checked={values.isActive === true}
                        onChange={() => setFieldValue('isActive', true)}
                        className="mr-2 text-yellow-400 focus:ring-yellow-400"
                      />
                      <span className="text-green-600 font-medium">Active</span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="isActive"
                        value="false"
                        checked={values.isActive === false}
                        onChange={() => setFieldValue('isActive', false)}
                        className="mr-2 text-yellow-400 focus:ring-yellow-400"
                      />
                      <span className="text-gray-600 font-medium">Inactive</span>
                    </label>
                  </div>
                  
                </div>

                <div className="flex justify-end gap-4 pt-4 border-t border-gray-200">
                  <button
                    type="button"
                    className="bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-3 px-6 rounded-lg transition-colors duration-200 disabled:cursor-not-allowed border border-gray-300"
                    onClick={() => resetForm()}
                    disabled={isSubmitting}
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="bg-[#FFDE34] hover:bg-yellow-400 text-black font-medium py-3 px-6 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed shadow-sm"
                    disabled={isSubmitting || loadingCategories}
                  >
                    {isSubmitting ? 'Creating...' : 'Create Topic'}
                  </button>
                </div>
              </div>
            </Form>
          )}
        </Formik>
      </div>
    </div>
  );
};

export default CreateNovelTopics;