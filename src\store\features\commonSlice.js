import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  friendActiveTab: 'search',
  openCreateSkinModal: false,
  editSkinModalId: null,
  deleteSkinModalData: null,
  selectedPlan: null
};

const commonSlice = createSlice({
  name: 'common',
  initialState,
  reducers: {
    setFriendActiveTab: (state, action) => {
      state.friendActiveTab = action.payload;
    },
    setOpenCreateSkinModal: (state, action) => {
      state.openCreateSkinModal = action.payload;
    },
    setEditSkinModalId: (state, action) => {
      state.editSkinModalId = action.payload;
    },
    setDeleteSkinModalData: (state, action) => {
      state.deleteSkinModalData = action.payload;
    },
    setSelectedPlan: (state, action) => {
      state.selectedPlan = action.payload;
    },
  },
});

export const { setFriendActiveTab, setOpenCreateSkinModal, setEditSkinModalId, setDeleteSkinModalData, setSelectedPlan } = commonSlice.actions;
export default commonSlice.reducer;
