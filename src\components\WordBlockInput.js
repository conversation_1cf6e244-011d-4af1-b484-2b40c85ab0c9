'use client';
import React, { useState } from 'react';
import { Icon } from '@iconify/react';

const WordBlockInput = ({ label, required, value = [], onChange }) => {
  const [inputValue, setInputValue] = useState('');

  // Handle input change
  const handleInputChange = (e) => {
    setInputValue(e.target.value);
  };

  // Handle key down (Enter)
  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && inputValue.trim()) {
      e.preventDefault();
      addWord();
    }
  };

  // Handle add button click
  const handleAddClick = () => {
    if (inputValue.trim()) {
      addWord();
    }
  };

  // Add word to the array
  const addWord = () => {
    const newWord = inputValue.trim();
    if (newWord) {
      const newValue = [...value, newWord];
      onChange(newValue);
      setInputValue('');
    }
  };

  // Remove word from the array
  const removeWord = (index) => {
    const newValue = [...value];
    newValue.splice(index, 1);
    onChange(newValue);
  };

  return (
    <div className="mb-4">
      <label className="block text-sm md:text-lg font-medium mb-2">
        {label} {required && <span className="text-red-500">*</span>}
      </label>

      <div className="border border-gray-300 rounded-lg bg-white">
        {/* Input field with add button */}
        <div className="flex items-center">
          <input
            type="text"
            value={inputValue}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            placeholder="Type your word here and enter to show wrong answers"
            className="flex-1 px-3 py-2 rounded-tl-lg focus:outline-none focus:ring-0"
          />
          <button
            type="button"
            onClick={handleAddClick}
            className="px-3 py-3 bg-gray-100 rounded hover:bg-gray-200 m-2"
          >
            <Icon
              icon="material-symbols:arrow-forward"
              className="text-gray-600"
            />
          </button>
        </div>

        {/* Divider */}
        <div className="border-t border-dashed border-gray-300 mb-3"></div>

        {/* Word blocks */}
        <div className="flex flex-wrap gap-2 p-4">
          {value?.length > 0 ? (
            value?.map((word, index) => (
              <div
                key={index}
                className="flex items-center bg-gray-100 rounded-md px-3 py-1.5 text-gray-700"
              >
                <span>{word}</span>
                <button
                  type="button"
                  onClick={() => removeWord(index)}
                  className="ml-2 text-red-500"
                >
                  <Icon icon="mdi:close" className="w-4 h-4" />
                </button>
              </div>
            ))
          ) : (
            <div className="flex-1 text-gray-500 text-center">
              No word {label} added yet
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default WordBlockInput;
