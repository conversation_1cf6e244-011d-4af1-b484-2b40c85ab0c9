'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import { motion, AnimatePresence } from 'framer-motion';
import { toast } from 'sonner';
import api from '@/lib/api';
import { useMutation, useQueryClient } from '@tanstack/react-query';

const MessageModal = ({ isOpen, onClose }) => {
  const [greeting, setGreeting] = useState(''); // Changed from message to greeting
  const queryClient = useQueryClient();

  // Create mutation for sending greeting
  const sendGreetingMutation = useMutation({
    mutationFn: (greetingData) => {
      return api.post('/diary/greeting', greetingData);
    },
    onSuccess: (response) => {
      if (response.data?.success) {
        setGreeting('');

        // Invalidate and refetch diary entry data
        queryClient.invalidateQueries({ queryKey: ['diary', 'entries', 'today'] });

        // Dispatch a custom event to notify the parent component
        window.dispatchEvent(new CustomEvent('greetingSent', {
          detail: { success: true }
        }));

        onClose();
      } else {
        console.log(response.data?.message || 'Failed to send greeting');
      }
    },
    onError: (error) => {
      console.error('Error sending greeting:', error);
    }
  });

  const handleSubmit = () => {
    if (!greeting.trim()) {
      toast.error('Please enter a greeting before sending');
      return;
    }

    sendGreetingMutation.mutate({ greeting: greeting.trim() });
  };

  const handleClose = () => {
    if (!sendGreetingMutation.isPending) {
      setGreeting('');
      onClose();
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onClick={handleClose}
        >
          <motion.div
            className="bg-white rounded-xl shadow-xl w-full max-w-2xl overflow-hidden relative"
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            transition={{ type: 'spring', damping: 20, stiffness: 300 }}
            onClick={(e) => e.stopPropagation()}
          >
            {/* Wooden sign header */}
            <div className="relative">
              <div className="bg-[#FFF9FB] pt-2 pb-2 px-2 shadow-xl flex justify-center">
                <Image
                  src="/assets/images/all-img/woodFrame.png"
                  alt="Send Greeting"
                  width={300}
                  height={80}
                  priority
                  className="object-cover"
                />
              </div>

              <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 mt-6 bg-[#FCF8EF] rounded-lg p-3 w-[250px]">
                <h2 className="text-2xl font-bold text-yellow-900 font-serif">
                  Greet Your Tutor
                </h2>
              </div>
            </div>

            {/* Modal content */}
            <div className="p-6">
              <div className="mb-4">
                {/* <label className="block text-sm font-medium text-gray-700 mb-2">
                  Your Greeting Message
                </label> */}
                <textarea
                  value={greeting}
                  onChange={(e) => setGreeting(e.target.value)}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-yellow-500 h-32"
                  placeholder="Write a greeting message to your tutor..."
                  disabled={sendGreetingMutation.isPending}
                />
              </div>

              {/* Action buttons */}
              <div className="flex justify-end space-x-3 mt-6">
                <button
                  type="button"
                  onClick={handleClose}
                  disabled={sendGreetingMutation.isPending}
                  className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 disabled:opacity-50"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={handleSubmit}
                  disabled={sendGreetingMutation.isPending || !greeting.trim()}
                  className="px-4 py-2 bg-yellow-500 text-white rounded-md hover:bg-yellow-600 disabled:opacity-50"
                >
                  {sendGreetingMutation.isPending ? 'Sending...' : 'Send Greeting'}
                </button>
              </div>
            </div>

            {/* Close button */}
            <button
              className="absolute top-1 right-1 w-10 h-10 hover:opacity-80 transition-opacity"
              onClick={handleClose}
              aria-label="Close greeting modal"
            >
              <Image
                src="/assets/images/all-img/cross-bg.png"
                alt="Close"
                width={50}
                height={50}
                className="w-full h-full object-contain"
              />
            </button>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default MessageModal;