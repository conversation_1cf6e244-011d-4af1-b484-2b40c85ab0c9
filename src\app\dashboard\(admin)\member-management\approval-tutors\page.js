'use client';

import NewTablePage from "@/components/form/NewTablePage";
import useDataFetch from "@/hooks/useDataFetch";
import React, { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import api from '@/lib/api';
import { useRouter } from 'next/navigation';

const approvalLists = () => {
  const router = useRouter();
  
  // Handle back button click - moved inside the component
  const handleBackClick = () => {
    router.back();
  };
  
  // Pagination states
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  
  // Set a reasonable limit that the API accepts and filter by approved status
  const [queryParams, setQueryParams] = useState({
    page: 1,
    limit: 10,
    status: 'approved'
  });
  
  const [itemStatuses, setItemStatuses] = useState({});
  
  // Update query params when pagination changes
  useEffect(() => {
    setQueryParams(prev => ({
      ...prev,
      page: currentPage,
      limit: rowsPerPage
    }));
  }, [currentPage, rowsPerPage]);
  
  // Fetch tutor approval data using the custom hook
  const {
    data: response,
    isLoading,
    error,
    refetch
  } = useDataFetch({
    queryKey: ['tutor-approval', queryParams],
    endPoint: '/tutor-approval',
    params: queryParams
  });

  // Extract items data
  const items = response?.items || [];
  
  // Function to get total count handling different response structures
  const getTotalCount = () => {
    if (!response) return 0;
    
    if (response.meta?.totalItems !== undefined) {
      return response.meta.totalItems;
    }
    
    if (response.totalCount !== undefined) {
      return response.totalCount;
    }
    
    return (response.items || []).length;
  };
  
  // Extract metadata for pagination
  const totalItems = getTotalCount();
  const totalPages = response?.meta?.totalPages || Math.ceil(totalItems / rowsPerPage) || 1;
  
  // Handle page change
  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  // Log the response to debug
  useEffect(() => {
    console.log("API Response:", response);
  }, [response]);

  // Initialize statuses from fetched data
  useEffect(() => {
    if (items.length > 0) {
      const initialStatuses = {};
      items.forEach(item => {
        initialStatuses[item.id] = item.status || 'pending';
      });
      setItemStatuses(prev => ({...prev, ...initialStatuses}));
    }
  }, [items]);
  
  const handleAssign = (row) => {
    // Debug: Log the entire row object to see the structure
    console.log("Full row object:", row);
    console.log("row.user:", row.user);
    console.log("row.user?.userId:", row.user?.userId);
    console.log("row.user?.name:", row.user?.name);
    
    // Store the tutor data in localStorage before navigating
    // FIXED: Access userId from the nested user object and use it as the main id
    const tutorData = {
      id: row.user?.userId || row.id, // Use the actual userId as the main id
      userId: row.user?.id|| 'N/A', // Keep userId for reference
      name: row.user?.name || 'N/A',
      email: row.user?.email || 'N/A',
      phoneNumber: row.user?.phoneNumber || 'N/A',
      bio: row.user?.bio || '',
      originalId: row.id // Keep the original approval record id for reference
    };
    
    localStorage.setItem('selectedTutor', JSON.stringify(tutorData));
    
    console.log("Storing tutor data:", tutorData);
    
    // Navigate to the assign task page
    router.push('/dashboard/member-management/assign-task');
  };
  
  const preparedData = items.map(item => {
    // Extract user fields to the top level for the table display
    const preparedItem = { 
      ...item,
      name: item.user?.name || 'N/A',
      userId: item.user?.userId || 'N/A', // Display the actual userId in the table
      email: item.user?.email || 'N/A',
      phoneNumber: item.user?.phoneNumber || 'N/A'
    };
    
    // Pass the original item to handleAssign
    preparedItem.action = (
      <button 
        className="bg-[#FFDE34] text-white px-4 py-1 rounded text-sm font-bold"
        onClick={() => handleAssign(item)}
      >
        Assign Roles
      </button>
    );
    
    return preparedItem;
  });

  const UserAvatar = ({ name }) => {
    return (
      <div className="flex items-center justify-center h-8 w-8 rounded-full bg-amber-100 text-amber-800">
        <svg 
          className="h-5 w-5" 
          xmlns="http://www.w3.org/2000/svg" 
          viewBox="0 0 24 24" 
          fill="currentColor"
        >
          <path 
            fillRule="evenodd" 
            d="M7.5 6a4.5 4.5 0 119 0 4.5 4.5 0 01-9 0zM3.751 20.105a8.25 8.25 0 0116.498 0 .75.75 0 01-.437.695A18.683 18.683 0 0112 22.5c-2.786 0-5.433-.608-7.812-1.7a.75.75 0 01-.437-.695z" 
            clipRule="evenodd" 
          />
        </svg>
      </div>
    );
  };

  const tutorColumns = [
    {
      label: 'TUTOR NAME',
      field: 'name',
      cellRenderer: (value, row) => (
        <div className="flex items-center space-x-2">
          <UserAvatar name={value} />
          <span className="font-medium text-gray-900">{value}</span>
        </div>
      )
    },
    {
      label: 'TUTOR ID',
      field: 'userId', // Changed from 'id' to 'userId' to show the actual user ID
    },
    {
      label: 'EMAIL ADDRESS',
      field: 'email',
    },
    {
      label: 'PHONE NUMBER',
      field: 'phoneNumber',
    },
    {
      label: 'ACTIONS',
      field: 'action',
    }
  ];

  if (error) {
    return (
      <div className="p-4 text-red-600">
        Error loading data: {error.message}
      </div>
    );
  }

  return (
    <div className="w-full px-4">
      <div className="overflow-auto max-h-[80vh]">
        <NewTablePage
          showSearch={false}
          showNameFilter={false}
          showSortFilter={false}
          showCreateButton={false}
          title="Approved Tutors"
          data={preparedData}
          columns={tutorColumns}
          loading={isLoading}
          onBack={handleBackClick}
          showCheckboxes={false}
          
          // Pagination props
          currentPage={currentPage}
          totalPages={totalPages}
          changePage={handlePageChange}
          totalItems={totalItems}
          rowsPerPage={rowsPerPage}
          setRowsPerPage={setRowsPerPage}
          hideTableFooter={false}
        />
      </div>
    </div>
  );
};

export default approvalLists;