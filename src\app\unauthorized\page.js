'use client';
import React from 'react';
import { useRouter } from 'next/navigation';
import { useSelector } from 'react-redux';
import { Icon } from '@iconify/react';
import Button from '@/components/Button';
import { getRoleBasedRoute } from '@/lib/navigation';

export default function UnauthorizedPage() {
  const router = useRouter();
  const { isAuth, user } = useSelector((state) => state.auth);

  const handleGoBack = () => {
    if (isAuth && user) {
      // Redirect to appropriate area based on user role
      const roleBasedRoute = getRoleBasedRoute(isAuth, user, '/login');
      router.push(roleBasedRoute);
    } else {
      // Redirect to login if not authenticated
      router.push('/login');
    }
  };

  const handleGoHome = () => {
    router.push('/');
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
        {/* Icon */}
        <div className="mb-6">
          <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
            <Icon 
              icon="material-symbols:block" 
              className="w-8 h-8 text-red-600" 
            />
          </div>
        </div>

        {/* Title */}
        <h1 className="text-2xl font-bold text-gray-900 mb-4">
          Access Denied
        </h1>

        {/* Message */}
        <p className="text-gray-600 mb-6">
          You don't have permission to access this page. Please contact your administrator if you believe this is an error.
        </p>

        {/* User Role Info */}
        {isAuth && user && (
          <div className="bg-gray-50 rounded-lg p-4 mb-6">
            <p className="text-sm text-gray-700">
              <span className="font-medium">Current Role:</span>{' '}
              <span className="capitalize">
                {user.selectedRole || user.role || 'Unknown'}
              </span>
            </p>
          </div>
        )}

        {/* Action Buttons */}
        <div className="space-y-3">
          <Button
            buttonText="Go to My Dashboard"
            onClick={handleGoBack}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white"
          />
          
          <Button
            buttonText="Go to Home"
            onClick={handleGoHome}
            className="w-full bg-gray-200 hover:bg-gray-300 text-gray-800"
          />
        </div>

        {/* Help Text */}
        <div className="mt-6 pt-6 border-t border-gray-200">
          <p className="text-xs text-gray-500">
            If you need access to this area, please contact your system administrator.
          </p>
        </div>
      </div>
    </div>
  );
}
