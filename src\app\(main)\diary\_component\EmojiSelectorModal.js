'use client';

import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Image from 'next/image';
import useDataFetch from '@/hooks/useDataFetch';
import Link from 'next/link';
import { Icon } from '@iconify/react';
import { useDispatch } from 'react-redux';
import { setActiveTool, setBrushSettings } from '@/store/features/diarySlice';
import { ButtonIcon } from '@/components/Button';

/**
 * A custom emoji selector modal that appears near the button that triggers it
 *
 * @param {Object} props
 * @param {boolean} props.isOpen - Whether the modal is open
 * @param {Function} props.onClose - Function to call when the modal is closed
 * @param {Function} props.onSelect - Function to call when an emoji is selected
 * @param {Object} props.triggerRef - Reference to the button that triggers the modal
 * @param {string} props.position - Position of the modal relative to the trigger (top, right, bottom, left)
 */
const EmojiSelectorModal = ({
  isOpen,
  onClose,
  onSelect,
  triggerRef,
  position,
}) => {
  const modalRef = useRef(null);
  const dispatch = useDispatch();
  const [selectedTool, setSelectedTool] = useState('decoration');

  const { data, isLoading, error } = useDataFetch({
    queryKey: ['diary-owned-items'],
    endPoint: '/student/owned-items',
  });

  const ownedItems = data?.items || [];

  // Different brush types
  const brushTypes = [
    { id: 'round', name: 'Round Brush', size: 5, type: 'round' },
    { id: 'square', name: 'Square Brush', size: 8, type: 'square' },
    { id: 'soft', name: 'Soft Brush', size: 12, type: 'soft' },
    { id: 'hard', name: 'Hard Brush', size: 3, type: 'hard' },
    { id: 'texture', name: 'Texture Brush', size: 10, type: 'texture' },
    { id: 'calligraphy', name: 'Calligraphy', size: 6, type: 'calligraphy' },
  ];

  // Eraser sizes
  const eraserSizes = [
    { id: 'small', name: 'Small', size: 5 },
    { id: 'medium', name: 'Medium', size: 10 },
    { id: 'large', name: 'Large', size: 20 },
    { id: 'xlarge', name: 'X-Large', size: 30 },
    { id: 'xxlarge', name: 'XX-Large', size: 50 },
  ];

  // Brush sizes for pencil, brush, highlighter
  const brushSizes = [
    { id: 'xs', name: 'XS', size: 1 },
    { id: 'small', name: 'S', size: 3 },
    { id: 'medium', name: 'M', size: 6 },
    { id: 'large', name: 'L', size: 10 },
    { id: 'xl', name: 'XL', size: 15 },
    { id: 'xxl', name: 'XXL', size: 20 },
  ];

  // Shapes
  const shapes = [
    { id: 'circle', name: 'Circle', icon: 'mdi:circle-outline' },
    { id: 'square', name: 'Square', icon: 'mdi:square-outline' },
    { id: 'triangle', name: 'Triangle', icon: 'mdi:triangle-outline' },
    { id: 'star', name: 'Star', icon: 'mdi:star-outline' },
    { id: 'arrow', name: 'Arrow', icon: 'mdi:arrow-right' },
  ];

  // Sample emoji data - replace with actual data from API later
  const emojis = [
    { id: 1, emoji: '💛' },
    { id: 2, emoji: '😊' },
    { id: 3, emoji: '🥑' },
    { id: 4, emoji: '😎' },
    { id: 5, emoji: '💛' },
    { id: 6, emoji: '💛' },
    { id: 7, emoji: '😢' },
    { id: 8, emoji: '😄' },
    { id: 9, emoji: '💛' },
  ];

  // Close when clicking outside
  // useEffect(() => {
  //   const handleClickOutside = (event) => {
  //     if (
  //       modalRef.current &&
  //       !modalRef.current.contains(event.target) &&
  //       triggerRef.current &&
  //       !triggerRef.current.contains(event.target)
  //     ) {
  //       onClose();
  //     }
  //   };

  //   if (isOpen) {
  //     document.addEventListener('mousedown', handleClickOutside);
  //   }

  //   return () => {
  //     document.removeEventListener('mousedown', handleClickOutside);
  //   };
  // }, [isOpen, onClose, triggerRef]);

  // Calculate position based on trigger element
  const getPosition = () => {
    if (!triggerRef.current) return {};

    const rect = triggerRef.current.getBoundingClientRect();

    switch (position) {
      case 'top':
        return {
          bottom: `${window.innerHeight - rect.top + 10}px`,
          left: `${rect.left + rect.width / 2 - 140}px`, // Center the modal
        };
      case 'right':
        return {
          left: `${rect.right + 10}px`,
          top: `${rect.top + rect.height / 2 - 140}px`, // Center the modal
        };
      case 'left':
        return {
          right: `${window.innerWidth - rect.left - 4}px`,
          top: `${rect.top + rect.height / 2 - 175}px`, // Center the modal
        };
      case 'bottom':
      default:
        return {
          top: `${rect.bottom + 10}px`,
          left: `${rect.left + rect.width / 2 - 140}px`, // Center the modal
        };
    }
  };

  // Get arrow position based on modal position
  const getArrowStyle = () => {
    switch (position) {
      case 'top':
        return {
          bottom: '100%',
          left: '50%',
          transform: 'translateX(-50%)',
          borderLeft: '8px solid transparent',
          borderRight: '8px solid transparent',
          borderBottom: '8px solid #FFD54F',
        };
      case 'right':
        return {
          left: '-8px',
          top: '50%',
          transform: 'translateY(-50%)',
          borderTop: '8px solid transparent',
          borderBottom: '8px solid transparent',
          borderRight: '8px solid #FFD54F',
        };
      case 'left':
        return {
          right: '-8px',
          top: '50%',
          transform: 'translateY(-50%)',
          borderTop: '8px solid transparent',
          borderBottom: '8px solid transparent',
          borderLeft: '8px solid #FFD54F',
        };
      case 'bottom':
      default:
        return {
          top: '-8px',
          left: '50%',
          transform: 'translateX(-50%)',
          borderLeft: '8px solid transparent',
          borderRight: '8px solid transparent',
          borderBottom: '8px solid #FFD54F',
          position: 'absolute',
        };
    }
  };

  // Animation variants
  const variants = {
    hidden: { opacity: 0, scale: 0.9 },
    visible: { opacity: 1, scale: 1 },
    exit: { opacity: 0, scale: 0.9 },
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          ref={modalRef}
          className="fixed -left-[450px] mt-4 translate-x-1/2 z-50 max-w-7xl min-w-[300px] sm:min-w-[500px] md:min-w-[600px] lg:min-w-[1200px] max-h-[300px] mx-auto border rounded-lg"
          // style={getPosition()}
          initial="hidden"
          animate="visible"
          exit="exit"
          variants={variants}
          transition={{ duration: 0.3, ease: "easeInOut" }}
        >
          <div className="relative bg-white rounded-lg shadow-lg overflow-hidden p-3 space-y-2 max-h-64 overflow-y-auto">
            {/* Close button */}
           
            <div className="absolute top-2 right-2 flex items-center justify-center text-gray-800 hover:text-gray-800 transition-colors z-10">
              <ButtonIcon
                icon="mdi:close"
                innerBtnCls="h-10 w-10 cursor-pointer"
                btnIconCls="h-5 w-5"
                aria-label="close diary"
              onClick={onClose}
              />
            </div>
            {/* Arrow pointing to the button */}
            {/* <div className="absolute w-0 h-0" style={getArrowStyle()}></div> */}
            {/* <div className='text-center border border-yellow-400 p-3 rounded-lg bg-yellow-200'>This feature is under development.</div> */}

            {/* Header */}
            <div className=" px-4 py-2 border-b-2 border-orange-300 mb-3 opacity-50">
              <h3 className="text-sm font-medium text-orange-800">
                Owned Items - Drag to decorate diary
              </h3>
            </div>

            {/* Shop Now Section */}
            <div className="">
              <div className="flex items-center justify-between">
                <Link
                  href="/diary/owned-item"
                  className="flex justify-center items-center w-20 h-20 bg-yellow-100 border-2 border-yellow-200 rounded-lg hover:bg-yellow-200 transition-colors"
                >
                  <span className="text-xs text-orange-600 font-medium hover:underline">
                    Shop Now
                  </span>
                </Link>

                <div className="flex items-center gap-2 flex-1 mx-3">
                  {selectedTool === 'decoration' &&
                    ownedItems?.slice(0, 8).map((item) => (
                      <div
                        key={item.id}
                        draggable
                        onDragStart={(e) => {
                          e.dataTransfer.setData(
                            'text/plain',
                            JSON.stringify({
                              type: 'decoration',
                              src: item.imageUrl || item.filePath,
                              title: item.title || 'Owned Item',
                            })
                          );
                        }}
                        className="w-20 h-20 rounded-lg bg-yellow-100 border-2 border-yellow-200 flex items-center justify-center cursor-pointer hover:scale-105 transition-transform duration-200 shadow-sm"
                        onClick={() => {
                          onSelect(item);
                          onClose();
                        }}
                        title={`${item.title} - Click to select or drag to decorate`}
                      >
                        {item.filePath || item.imageUrl ? (
                          <Image
                            src={item.imageUrl || item.filePath}
                            alt={item.title}
                            width={70}
                            height={70}
                            className="object-contain pointer-events-none"
                          />
                        ) : (
                          <span className="text-2xl">{item.emoji}</span>
                        )}
                      </div>
                    ))}

                  {(selectedTool === 'brush' ||
                    selectedTool === 'pencil' ||
                    selectedTool === 'highlighter') && (
                    <div className="w-full">
                      {/* Color Selection Row */}
                      <div className="mb-2">
                        <div className="flex gap-1 overflow-x-auto pb-1">
                          {[
                            '#000000',
                            '#FF0000',
                            '#00FF00',
                            '#0000FF',
                            '#FFFF00',
                            '#FF00FF',
                            '#00FFFF',
                            '#FFA500',
                            '#800080',
                            '#FFC0CB',
                          ].map((color) => (
                            <div
                              key={color}
                              onClick={() =>
                                dispatch(setBrushSettings({ color: color }))
                              }
                              className="flex-shrink-0 w-6 h-6 rounded-full border border-gray-300 cursor-pointer hover:scale-110 transition-transform"
                              style={{ backgroundColor: color }}
                              title={`Select ${color}`}
                            />
                          ))}
                        </div>
                      </div>

                      {/* Brush Sizes Row */}
                      <div className="flex gap-1 overflow-x-auto pb-1">
                        {brushSizes.map((brushSize) => (
                          <div
                            key={brushSize.id}
                            onClick={() =>
                              dispatch(
                                setBrushSettings({ size: brushSize.size })
                              )
                            }
                            className="flex-shrink-0 w-12 h-12 rounded-lg bg-blue-100 border border-blue-200 flex flex-col items-center justify-center cursor-pointer hover:scale-105 transition-transform duration-200"
                            title={`Size ${brushSize.size}px`}
                          >
                            <div
                              className="bg-blue-600 rounded-full"
                              style={{
                                width: `${Math.min(
                                  brushSize.size * 0.8,
                                  10
                                )}px`,
                                height: `${Math.min(
                                  brushSize.size * 0.8,
                                  10
                                )}px`,
                              }}
                            />
                            <span className="text-xs text-blue-700 mt-0.5 leading-none">
                              {brushSize.name}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {selectedTool === 'shapes' && (
                    <div className="w-full">
                      {/* Shapes Row */}
                      <div className="flex gap-1 overflow-x-auto pb-1">
                        {shapes.map((shape) => (
                          <div
                            key={shape.id}
                            draggable
                            onDragStart={(e) => {
                              e.dataTransfer.setData(
                                'text/plain',
                                JSON.stringify({
                                  type: 'shape',
                                  shapeType: shape.id,
                                  title: shape.name,
                                })
                              );
                            }}
                            className="flex-shrink-0 w-12 h-12 rounded-lg bg-green-100 border border-green-200 flex flex-col items-center justify-center cursor-pointer hover:scale-105 transition-transform duration-200"
                            title={`${shape.name} - Drag to add`}
                          >
                            <Icon
                              icon={shape.icon}
                              className="w-6 h-6 text-green-700"
                            />
                            <span className="text-xs text-green-700 mt-0.5 leading-none">
                              {shape.name}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {selectedTool === 'eraser' && (
                    <div className="w-full overflow-hidden">
                      {/* Eraser Sizes Row */}
                      <div className="flex gap-1 overflow-x-auto pb-1">
                        {eraserSizes.map((eraser) => (
                          <div
                            key={eraser.id}
                            onClick={() => {
                              dispatch(setActiveTool('eraser'));
                              dispatch(
                                setBrushSettings({
                                  size: eraser.size,
                                })
                              );
                            }}
                            className="flex-shrink-0 w-12 h-12 rounded-lg bg-red-100 border border-red-200 flex flex-col items-center justify-center cursor-pointer hover:scale-105 transition-transform duration-200"
                            title={`${eraser.name} Eraser - ${eraser.size}px`}
                          >
                            <div
                              className="bg-red-600 rounded-full"
                              style={{
                                width: `${Math.min(eraser.size * 0.3, 10)}px`,
                                height: `${Math.min(eraser.size * 0.3, 10)}px`,
                              }}
                            />
                            <span className="text-xs text-red-700 mt-0.5 leading-none">
                              {eraser.name}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                <Link
                  href="/diary/owned-item"
                  className="flex justify-center items-center w-20 h-20 bg-yellow-100 border-2 border-yellow-200 rounded-lg hover:bg-yellow-200 transition-colors"
                >
                  <span className="text-xs text-orange-600 font-medium hover:underline">
                    See More
                  </span>
                </Link>
              </div>

              {/* Tools Section */}
              <div className="pt-3">
                <div className=" px-4 py-2 border-b-2 border-orange-300 mb-3">
                  <h3 className="text-sm font-medium text-orange-800">
                    Toolsss - Drag to decorate
                  </h3>
                </div>
                <div className="flex items-center gap-3">
                  <div className="flex items-center gap-2">
                    {/* Main Tool Selection */}
                    <div
                      onClick={() => {
                        setSelectedTool('decoration');
                        dispatch(setActiveTool('decoration'));
                      }}
                      className={`w-10 h-10 rounded flex items-center justify-center cursor-pointer hover:scale-105 transition-transform ${
                        selectedTool === 'decoration'
                          ? 'bg-yellow-200 border-2 border-yellow-400'
                          : 'bg-yellow-100 border border-yellow-200'
                      }`}
                      title="Image Tool"
                    >
                      <Icon
                        icon="mdi:image-plus"
                        className="w-5 h-5 text-yellow-700"
                      />
                    </div>

                    {/* Brush Tools */}
                    <div
                      onClick={() => {
                        setSelectedTool('pencil');
                        dispatch(setActiveTool('pencil'));
                        dispatch(setBrushSettings({ size: 2, type: 'pencil' }));
                      }}
                      className={`w-10 h-10 rounded flex items-center justify-center cursor-pointer hover:scale-105 transition-transform ${
                        selectedTool === 'pencil'
                          ? 'bg-blue-200 border-2 border-blue-400'
                          : 'bg-blue-100 border border-blue-200'
                      }`}
                      title="Pencil Tool"
                    >
                      <Icon
                        icon="mdi:pencil"
                        className="w-5 h-5 text-blue-700"
                      />
                    </div>

                    <div
                      onClick={() => {
                        setSelectedTool('brush');
                        dispatch(setActiveTool('brush'));
                        dispatch(setBrushSettings({ size: 8, type: 'brush' }));
                      }}
                      className={`w-10 h-10 rounded flex items-center justify-center cursor-pointer hover:scale-105 transition-transform ${
                        selectedTool === 'brush'
                          ? 'bg-blue-200 border-2 border-blue-400'
                          : 'bg-blue-100 border border-blue-200'
                      }`}
                      title="Brush Tool"
                    >
                      <Icon
                        icon="mdi:brush"
                        className="w-5 h-5 text-blue-700"
                      />
                    </div>

                    <div
                      onClick={() => {
                        setSelectedTool('highlighter');
                        dispatch(setActiveTool('highlighter'));
                        dispatch(
                          setBrushSettings({
                            size: 15,
                            type: 'highlighter',
                            opacity: 0.5,
                          })
                        );
                      }}
                      className={`w-10 h-10 rounded flex items-center justify-center cursor-pointer hover:scale-105 transition-transform ${
                        selectedTool === 'highlighter'
                          ? 'bg-blue-200 border-2 border-blue-400'
                          : 'bg-blue-100 border border-blue-200'
                      }`}
                      title="Highlighter Tool"
                    >
                      <Icon
                        icon="mdi:marker"
                        className="w-5 h-5 text-blue-700"
                      />
                    </div>

                    <div
                      onClick={() => {
                        setSelectedTool('shapes');
                        dispatch(setActiveTool('shapes'));
                      }}
                      className={`w-10 h-10 rounded flex items-center justify-center cursor-pointer hover:scale-105 transition-transform ${
                        selectedTool === 'shapes'
                          ? 'bg-green-200 border-2 border-green-400'
                          : 'bg-green-100 border border-green-200'
                      }`}
                      title="Shapes Tool"
                    >
                      <Icon
                        icon="mdi:shape"
                        className="w-5 h-5 text-green-700"
                      />
                    </div>

                    <div
                      onClick={() => {
                        setSelectedTool('eraser');
                        dispatch(setActiveTool('eraser'));
                      }}
                      className={`w-10 h-10 rounded flex items-center justify-center cursor-pointer hover:scale-105 transition-transform ${
                        selectedTool === 'eraser'
                          ? 'bg-red-200 border-2 border-red-400'
                          : 'bg-red-100 border border-red-200'
                      }`}
                      title="Eraser Tool"
                    >
                      <Icon
                        icon="mdi:eraser"
                        className="w-5 h-5 text-red-700"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default EmojiSelectorModal;
