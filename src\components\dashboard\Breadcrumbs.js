'use client';
import { usePathname } from 'next/navigation';
import Link from 'next/link';
import { Icon } from '@iconify/react';

const Breadcrumbs = () => {
  const pathname = usePathname();

  // Remove leading slash and split path into segments
  const pathSegments = pathname.split('/').filter((segment) => segment);

  // Generate breadcrumb items, skipping UUID segments
  const breadcrumbs = pathSegments.reduce((acc, segment, index) => {
    // Check if segment is a UUID
    const isUUID =
      /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(
        segment
      );

    // Skip UUID segments
    if (isUUID) return acc;

    // Create the path for this breadcrumb by including only non-UUID segments up to this point
    const path =
      '/' +
      pathSegments
        .slice(0, index + 1)
        .filter((seg, i) => {
          const segIsUUID =
            /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(
              seg
            );
          return !segIsUUID;
        })
        .join('/');

    // Format the segment text
    const formattedText = segment
      .split('-')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');

    acc.push({
      text: formattedText,
      path,
      isLast: index === pathSegments.length - 1,
    });

    return acc;
  }, []);

  if (breadcrumbs.length === 0) return null;

  return (
    <nav className="flex items-center text-sm text-gray-600 mb-4">
      <Link
        href="/dashboard"
        className="flex items-center hover:text-yellow-600 transition-colors"
      >
        <Icon icon="lucide:home" className="w-4 h-4" />
      </Link>

      {breadcrumbs.map((breadcrumb, index) => (
        <div key={breadcrumb.path} className="flex items-center">
          <Icon
            icon="lucide:chevron-right"
            className="w-4 h-4 mx-1 text-gray-400"
          />

          {breadcrumb.isLast ? (
            <span className="font-medium text-gray-600">{breadcrumb.text}</span>
          ) : (
            <Link
              href={breadcrumb.path}
              className="hover:text-yellow-600 transition-colors"
            >
              {breadcrumb.text}
            </Link>
          )}
        </div>
      ))}
    </nav>
  );
};

export default Breadcrumbs;
