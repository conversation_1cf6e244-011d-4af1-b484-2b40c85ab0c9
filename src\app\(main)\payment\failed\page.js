'use client';

import React, { useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Icon } from '@iconify/react';

const PaymentFailed = () => {
  const router = useRouter();
  const searchParams = useSearchParams();

  // Extract payment result parameters
  const paymentData = {
    ordr_idxx: searchParams.get('ordr_idxx'),
    good_name: searchParams.get('good_name'),
    good_mny: searchParams.get('good_mny'),
    buyr_name: searchParams.get('buyr_name'),
    res_cd: searchParams.get('res_cd'),
    res_msg: searchParams.get('res_msg'),
    error_code: searchParams.get('error_code'),
    error_msg: searchParams.get('error_msg'),
  };

  useEffect(() => {
    // You can add analytics tracking here
    console.log('Payment failed:', paymentData);
  }, []);

  const handleRetry = () => {
    router.push('/checkout');
  };

  const handleGoHome = () => {
    router.push('/');
  };

  const handleContactSupport = () => {
    router.push('/contactus');
  };

  // Get error message to display
  const getErrorMessage = () => {
    if (paymentData.res_msg) {
      return paymentData.res_msg;
    }
    if (paymentData.error_msg) {
      return paymentData.error_msg;
    }
    return 'An unexpected error occurred during payment processing.';
  };

  return (
    <div className="min-h-[85vh] py-8 max-w-4xl mx-auto px-5 xl:px-0">
      <div className="bg-white rounded-lg shadow-lg p-8 text-center">
        {/* Error Icon */}
        <div className="mb-6">
          <div className="w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Icon 
              icon="material-symbols:error" 
              className="text-red-600 text-4xl"
            />
          </div>
          <h1 className="text-3xl font-bold text-gray-800 mb-2">
            Payment Failed
          </h1>
          <p className="text-gray-600">
            We're sorry, but your payment could not be processed.
          </p>
        </div>

        {/* Error Details */}
        <div className="bg-red-50 rounded-lg p-6 mb-6 text-left max-w-md mx-auto">
          <h3 className="font-semibold mb-4 text-center text-red-800">Error Details</h3>
          <div className="space-y-3">
            <div className="text-center">
              <p className="text-red-700 font-medium">
                {getErrorMessage()}
              </p>
            </div>
            
            {paymentData.ordr_idxx && (
              <div className="flex justify-between border-t pt-3">
                <span className="text-gray-600">Order ID:</span>
                <span className="font-medium">{paymentData.ordr_idxx}</span>
              </div>
            )}
            {paymentData.good_name && (
              <div className="flex justify-between">
                <span className="text-gray-600">Product:</span>
                <span className="font-medium">{paymentData.good_name}</span>
              </div>
            )}
            {paymentData.good_mny && (
              <div className="flex justify-between">
                <span className="text-gray-600">Amount:</span>
                <span className="font-medium">₩{parseInt(paymentData.good_mny).toLocaleString()}</span>
              </div>
            )}
            {paymentData.buyr_name && (
              <div className="flex justify-between">
                <span className="text-gray-600">Buyer:</span>
                <span className="font-medium">{paymentData.buyr_name}</span>
              </div>
            )}
            {paymentData.res_cd && (
              <div className="flex justify-between">
                <span className="text-gray-600">Error Code:</span>
                <span className="font-medium">{paymentData.res_cd}</span>
              </div>
            )}
          </div>
        </div>

        {/* Common Error Solutions */}
        <div className="bg-yellow-50 rounded-lg p-6 mb-6 text-left max-w-md mx-auto">
          <h3 className="font-semibold mb-3 text-center text-yellow-800">Common Solutions</h3>
          <ul className="text-sm text-yellow-700 space-y-2">
            <li className="flex items-start">
              <Icon icon="material-symbols:check-small" className="text-yellow-600 mt-0.5 mr-2 flex-shrink-0" />
              Check your card details and try again
            </li>
            <li className="flex items-start">
              <Icon icon="material-symbols:check-small" className="text-yellow-600 mt-0.5 mr-2 flex-shrink-0" />
              Ensure sufficient funds in your account
            </li>
            <li className="flex items-start">
              <Icon icon="material-symbols:check-small" className="text-yellow-600 mt-0.5 mr-2 flex-shrink-0" />
              Try using a different payment method
            </li>
            <li className="flex items-start">
              <Icon icon="material-symbols:check-small" className="text-yellow-600 mt-0.5 mr-2 flex-shrink-0" />
              Contact your bank if the issue persists
            </li>
          </ul>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <button
            onClick={handleRetry}
            className="px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
          >
            Try Again
          </button>
          <button
            onClick={handleContactSupport}
            className="px-8 py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors font-medium"
          >
            Contact Support
          </button>
          <button
            onClick={handleGoHome}
            className="px-8 py-3 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors font-medium"
          >
            Go Home
          </button>
        </div>

        {/* Additional Information */}
        <div className="mt-8 p-4 bg-blue-50 rounded-lg">
          <p className="text-sm text-blue-800">
            <Icon icon="material-symbols:info" className="inline mr-1" />
            No charges have been made to your account. You can safely retry the payment.
          </p>
        </div>
      </div>
    </div>
  );
};

export default PaymentFailed;
