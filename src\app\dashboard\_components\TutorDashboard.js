import React from 'react';
import { motion } from 'framer-motion';
import StatsCard from '@/components/dashboard/StatsCard';
import SubmissionCard from '@/components/dashboard/SubmissionCard';
import useDataFetch from '@/hooks/useDataFetch';
import { useRouter } from 'next/navigation';

const TutorDashboard = ({ statsData = [] }) => {
  const router = useRouter();
  const { data: totalReviews, isLoading: isReviewsLoading } = useDataFetch({
    queryKey: 'dashboard-module-stats',
    endPoint: '/tutor/dashboard/reviews/total',
  });

  const { data: pendingSubmissions, isLoading: isPendingSubmissionsLoading } =
    useDataFetch({
      queryKey: 'dashboard-submissions-pending',
      endPoint: '/tutor/dashboard/submissions/pending',
    });

  const {
    data: confirmedSubmissions,
    isLoading: isConfirmedSubmissionsLoading,
  } = useDataFetch({
    queryKey: 'dashboard-submissions-confirmed',
    endPoint: '/tutor/dashboard/submissions/confirmed',
  });

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-3 2xl:grid-cols-5 gap-6">
        <div className="col-span-1 lg:col-span-3 2xl:col-span-5 space-y-6">
          {/* Top Stats Grid */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 md:gap-6 w-full">
            {statsData?.map((stat, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                style={{ backgroundColor: stat?.bgColor }}
                onClick={() => index == 0 && router.push('/dashboard/students')}
                className={`shadow-sm border border-gray-200 rounded-lg overflow-hidden ${
                  index == 0 && 'cursor-pointer'
                }`}
              >
                <StatsCard {...stat} />
              </motion.div>
            ))}
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Reviews Stats */}
        <motion.div
          className="space-y-6 border rounded-lg p-4 shadow"
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.4 }}
        >
          {/* Total Reviews */}
          <div className="space-y-3">
            <h3 className="text-lg font-semibold text-gray-900">
              Total Reviews Across All Modules.
            </h3>
            <div className="text-3xl text-orange-400 font-semibold  mb-4 flex items-center gap-4">
              {totalReviews?.totalReviews || 0}{' '}
              <span className="text-sm text-gray-500">Reviews</span>{' '}
              <div className="h-8 w-px bg-gray-200"></div>
              {totalReviews?.totalFeedback}{' '}
              <span className="text-sm text-gray-500">Feedback</span>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              {totalReviews?.moduleBreakdown?.map((item, index) => (
                <SubmissionCard
                  key={index}
                  title={item?.moduleName}
                  countNumber={{
                    review: {
                      count: item?.totalSubmissions || 0,
                      title: 'Reviews',
                    },
                    feedback: {
                      count: item?.totalFeedback || 0,
                      title: 'Feedback',
                    },
                  }}
                  bgColor={'bg-yellow-50'}
                />
              ))}
            </div>
          </div>
        </motion.div>

        {/* Submission Stats */}
        <motion.div
          className="space-y-6 border rounded-lg p-4 shadow"
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.4 }}
        >
          {/* Total Reviews */}
          <div className="space-y-3">
            <h3 className="text-lg font-semibold text-gray-900">
              Review status Across All Modules.
            </h3>
            <div className="text-3xl text-orange-400 font-semibold  mb-4 flex items-center gap-4">
              <h1 className="text-green-500">
                {confirmedSubmissions?.totalConfirmedSubmissions || 0}{' '}
                <span className="text-sm text-gray-500">Confirm</span>{' '}
              </h1>
              <div className="h-8 w-px bg-gray-200"></div>
              <h1 className="text-red-400">
                {confirmedSubmissions?.totalPendingSubmissions}{' '}
                <span className="text-sm text-gray-500">Pending</span>
              </h1>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              {confirmedSubmissions?.moduleBreakdown?.map((item, index) => (
                <SubmissionCard
                  key={index}
                  title={item?.moduleName}
                  countNumber={{
                    review: {
                      count: item?.confirmedSubmissions || 0,
                      title: 'Confirmed',
                    },
                    feedback: {
                      count: item?.pendingSubmissions || 0,
                      title: 'Pending',
                    },
                  }}
                  bgColor={'bg-yellow-50'}
                />
              ))}
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default TutorDashboard;
