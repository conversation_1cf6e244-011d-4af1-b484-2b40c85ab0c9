import Head from 'next/head';
import { openSans } from '../fonts';
import { Providers } from '../providers/providers';
import './globals.css';

export const metadata = {
  title: 'HEC',
  description: 'This is home page',
  icons: {
    icon: '/assets/images/all-img/Logo.png',
  },
  other: {
    // Disable spell checking and grammar checking globally
    'google': 'notranslate',
    'grammarly-disable': 'true',
    'data-gramm': 'false',
    'data-gramm_editor': 'false',
    'data-enable-grammarly': 'false',
  },
};


export default function RootLayout({ children }) {
  return (
    <html
      lang="en"
      className="notranslate"
      translate="no"
      data-gramm="false"
      data-gramm_editor="false"
      data-enable-grammarly="false"
      spellCheck="false"
    >
      <Head>
        <meta name="google" content="notranslate" />
        <meta name="grammarly-disable" content="true" />
        <meta name="data-gramm" content="false" />
        <meta name="data-gramm_editor" content="false" />
        <meta name="data-enable-grammarly" content="false" />
      </Head>
      <body
        className={`${openSans.variable}`}
        data-gramm="false"
        data-gramm_editor="false"
        data-enable-grammarly="false"
        spellCheck="false"
      >
        <Providers>{children}</Providers>
      </body>
    </html>
  );
}
