'use client';
import React from 'react';
import ProfileHeader from './ProfileHeader';
import Link from 'next/link';
import Image from 'next/image';

const StudentProfile = ({ profileData }) => {
  const boardLinks = [
    { title: 'My Diary', link: '/diary' },
    { title: 'Awards', link: '/diary/award' },
    { title: 'Owned Items', link: '/diary/owned-item' },
    { title: 'Friends', link: '/find-friends' },
    { title: 'My Q/A', link: '/qa' },
    { title: 'My Essay', link: '/essay/list' },
    { title: 'My Tutor', link: '/tutors' },
    { title: 'My History', link: '/history' },
  ];

  return (
    <div>
      <ProfileHeader userDetails={profileData} />

      <div className="py-8">
        {/* My Board Section */}
        <div className="bg-[#EDFDFD] mb-12 p-8 py-12 rounded-lg">
          <div className="max-w-7xl mx-auto px-5 xl:px-0 ">
            <h2 className="text-2xl font-semibold mb-6">My Board</h2>

            <div className="grid grid-cols-2 md:grid-cols-3 xl:grid-cols-4 gap-4">
              {boardLinks?.map((item, idx) => (
                <Link key={idx} href={item?.link}>
                  <div className="bg-gradient-to-b from-[#FFDE5B] to-[#DCA600] transition-colors rounded-lg py-4 text-center shadow-md border border-yellow-600">
                    <span className="font-medium">{item?.title}</span>
                  </div>
                </Link>
              ))}
            </div>
          </div>
        </div>

        {/* Recent Activities Section */}
        <div className="max-w-7xl mx-auto px-5 xl:px-0">
          <h2 className="text-2xl font-semibold mb-6">Recent Activities</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Activity Card 1 */}
            <div className="rounded-lg overflow-hidden">
              <div className="relative">
                <Image
                  src="/assets/images/all-img/groupFriends.jpg"
                  alt="Children reading"
                  width={600}
                  height={400}
                  className="w-full h-80 object-cover rounded-lg"
                />
              </div>
              <div className="py-4 bg-white flex items-center gap-3">
                <div className="">
                  <div className="bg-gradient-to-b from-[#6A8CBE] to-[#314158] text-white p-2 px-5 rounded-lg text-center">
                    <div className="text-xl font-bold">16</div>
                    <div className="text-xs">Feb</div>
                  </div>
                </div>
                <h3 className="text-lg font-medium">
                  Exploring milestone children park and buy happiness
                </h3>
              </div>
            </div>

            {/* Activity Card 2 */}
            <div className="rounded-lg overflow-hidden">
              <div className="relative">
                <Image
                  src="/assets/images/all-img/groupFriends.jpg"
                  alt="Children reading"
                  width={600}
                  height={400}
                  className="w-full h-80 object-cover rounded-lg"
                />
              </div>
              <div className="py-4 bg-white flex items-center gap-3">
                <div className="">
                  <div className="bg-gradient-to-b from-[#6A8CBE] to-[#314158] text-white p-2 px-5 rounded-lg text-center">
                    <div className="text-xl font-bold">16</div>
                    <div className="text-xs">Feb</div>
                  </div>
                </div>
                <h3 className="text-lg font-medium">
                  Exploring milestone children park and buy happiness
                </h3>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StudentProfile;
