'use client';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import FormInput from '@/components/form/FormInput';
import FormRadio from '@/components/form/FormRadio';
import FormSelect from '@/components/form/FormSelect';
import NumberInput from '@/components/form/NumberInput';
import Form<PERSON>heckbox from '@/components/form/FormCheckbox';
import RegularGoBack from '@/components/shared/RegularGoBack';
import api from '@/lib/api';
import { useRouter } from 'next/navigation';
import { useQueryClient } from '@tanstack/react-query';
import useDataFetch from '@/hooks/useDataFetch';
import { useEffect, useState } from 'react';

const validationSchema = Yup.object().shape({
  name: Yup.string().required('Award name is required'),
  description: Yup.string().required('Award description is required'),
  module: Yup.string().required('Module is required'),
  criteria: Yup.array().min(1, 'At least one criteria must be selected'),
  frequency: Yup.string().required('Time frequency is required'),
  rewardPoints: Yup.number().required('Reward points are required'),
  isActive: Yup.boolean().required('Active status is required'),
  criteriaConfig: Yup.object().shape({
    minScore: Yup.number(),
    entriesRequired: Yup.number(),
  }),
});

const AddAward = () => {
  const router = useRouter();
  const queryClient = useQueryClient();
  const [selectedModule, setSelelectedModule] = useState(null);

  const { data: awardCriteria, isLoading } = useDataFetch({
    queryKey: ['award-criteria', selectedModule],
    endPoint: `/awards/criteria?module=${selectedModule}`,
    enabled: !!selectedModule,
  });

  const criteriaOptions = awardCriteria?.map((item) => ({
    value: item.id,
    label: item.name,
  }));

  const moduleOptions = [
    { value: 'diary', label: 'Diary' },
    // { value: 'play', label: 'Play' },
    // { value: 'qa', label: 'QA' },
    { value: 'novel', label: 'Novel' },
    { value: 'essay', label: 'Essay' },
  ];

 
const frequencyOptions = [
  { value: 'one_time', label: 'One Time' },
  { value: 'weekly', label: 'Weekly' },
  { value: 'monthly', label: 'Monthly' },
  { value: 'quarterly', label: 'Quarterly' },
  { value: 'yearly', label: 'Yearly' },
];
  const activeStatusOptions = [
    { value: true, label: 'Active' },
    { value: false, label: 'Inactive' },
  ];

  const handleSubmit = async (
    values,
    { setSubmitting, resetForm, setFieldError }
  ) => {
    try {
      const response = await api.post('/awards', values);
      console.log(response);
      queryClient.invalidateQueries('/awards');
      router.push('/dashboard/awards');
      resetForm();
    } catch (error) {
      console.log(error);
      if (error?.response?.data?.validationErrors) {
        const validationErrors = error.response.data.validationErrors;
        Object.keys(validationErrors).forEach((field) => {
          setFieldError(`${field}`, validationErrors[field][0]);
        });
      }
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div className="h-[85vh] overflow-y-auto">
      <RegularGoBack className="pb-5 max-w-32" />

      <Formik
        initialValues={{
          name: '',
          description: '',
          module: '',
          criteria: [],
          frequency: '',
          rewardPoints: '',
          isActive: true,
          criteriaConfig: {
            minScore: '',
            entriesRequired: '',
          },
        }}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
      >
        {({ values, isSubmitting }) => {
          useEffect(() => {
            setSelelectedModule(values?.module);
          }, [values?.module]);
          return (
            <Form className="space-y-6 ">
              <div className=" grid grid-cols-1 md:grid-cols-2 items-center gap-6 bg-gray-50 border p-5 rounded-xl">
                <FormInput
                  label="Award Name"
                  name="name"
                  placeholder="Enter award name"
                  required
                />

                <NumberInput
                  type="number"
                  label="Reward Points"
                  name="rewardPoints"
                  placeholder="Enter reward points"
                  required
                />

                <FormInput
                  label="Award Description"
                  name="description"
                  placeholder="Write award description"
                  isTextarea={true}
                  required
                />

                <FormRadio
                  label="Active Status"
                  name="isActive"
                  options={activeStatusOptions}
                  isHorizontal={true}
                  required
                />
              </div>

              <div className="space-y-6 bg-gray-50 border p-5 rounded-xl">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <FormSelect
                    label="Module"
                    name="module"
                    options={moduleOptions}
                    placeholder="Select Module"
                    required
                  />

                  <FormSelect
                    label="Select Time Frequency"
                    name="frequency"
                    options={frequencyOptions}
                    placeholder="Select time frequency"
                    required
                  />
                </div>

                {values?.module && (
                  <div className="space-y-2">
                    <FormCheckbox
                      label="Award Criteria"
                      name="criteria"
                      options={criteriaOptions}
                      isHorizontal={true}
                      required
                    />
                  </div>
                )}
              </div>

              <div className="flex justify-end">
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="bg-yellow-400 px-4 py-2 rounded-md hover:bg-yellow-500"
                >
                  {isSubmitting ? 'Submitting...' : 'Submit'}
                </button>
              </div>
            </Form>
          );
        }}
      </Formik>
    </div>
  );
};

export default AddAward;
