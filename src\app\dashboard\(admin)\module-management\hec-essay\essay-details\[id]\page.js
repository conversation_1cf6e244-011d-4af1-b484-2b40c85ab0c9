'use client';
import { useState, useEffect, use } from 'react';
import { useRouter } from 'next/navigation';
import useDataFetch from '@/hooks/useDataFetch';
import DiaryCanvas from '@/app/dashboard/(tutor)/submission-management/hec-diary/review/_components/DiaryCanvas';
import TeacherFeedbackModal from '../../teacher-feedback/page';
import Image from 'next/image';

const EssayDetails = ({ params }) => {
  const router = useRouter();
  const [showFeedbackModal, setShowFeedbackModal] = useState(false);
  const [essay, setEssay] = useState(null);
  const [taskId, setTaskId] = useState(null);
  const [userId, setUserId] = useState(null);
  
  // Unwrap the params Promise using React.use()
  const resolvedParams = use(params);
  const essayId = resolvedParams?.id;
  
  useEffect(() => {
    console.log("Essay ID from params:", essayId);
  }, [essayId]);

  // Step 1: Fetch essay details using the useDataFetch hook
  const { 
    data: essayData, 
    isLoading: isEssayLoading, 
    error: essayError 
  } = useDataFetch({
    queryKey: [`/admin-essay/list/${essayId}`],
    endPoint: `/admin-essay/list/${essayId}`,
    enabled: !!essayId
  });

  // Step 2: Extract taskId and userId from essayData and fetch skin data
  useEffect(() => {
    if (essayData && essayData?.id) {
      // Extract taskId and userId from essayData
      const extractedTaskId = essayData.task.id;
      const extractedUserId = essayData.createdById;
      
      console.log("Extracted taskId:", extractedTaskId, "userId:", extractedUserId);
      
      if (extractedTaskId && extractedUserId) {
        setTaskId(extractedTaskId);
        setUserId(extractedUserId);
      }
    }
  }, [essayData]);

  // Step 3: Fetch skin data using taskId and userId
  const {
    data: skinData,
    isLoading: isSkinLoading,
    error: skinError
  } = useDataFetch({
    queryKey: [`/admin-essay/skins/${taskId}/${userId}`],
    endPoint: `/admin-essay/skins/${taskId}/${userId}`,
    enabled: !!(taskId && userId)
  });
  
  useEffect(() => {
    if (skinData) {
      console.log("Fetched skinData:", skinData);
    }
  }, [skinData]);

  // Process data whenever essayData or skinData changes
  useEffect(() => {
    if (essayData && essayData?.id) {
      // Get the latest submission from submissionHistory
      const latestSubmission = essayData?.submissionHistory && 
        essayData?.submissionHistory.length > 0 ?
        essayData?.submissionHistory[0] : null;
      
      // Format the essay data, combining essayData and skinData if available
      setEssay({
        id: essayData?.id,
        title: essayData?.title || 'Untitled Essay',
        status: essayData?.status,
        taskId: essayData?.task,
        userId: essayData?.createdById,
        submittedBy: latestSubmission ? (latestSubmission.createdBy || 'Student User') : 'Student User',
        submissionDate: latestSubmission ? 
          new Date(latestSubmission.submissionDate).toLocaleDateString('en-US', {
            day: 'numeric',
            month: 'long',
            year: 'numeric'
          }) : 'Unknown',
        originalDate: latestSubmission ? 
          new Date(latestSubmission.createdAt).toLocaleDateString('en-US', {
            day: 'numeric',
            month: 'short',
            year: '2-digit'
          }) : 'Unknown',
        tutorEditDate: essayData.updatedAt ? 
          new Date(essayData.updatedAt).toLocaleDateString('en-US', {
            day: 'numeric',
            month: 'short',
            year: '2-digit'
          }) : 'Unknown',
        content: latestSubmission ? latestSubmission.content : 'No content available',
        points: latestSubmission && latestSubmission.submissionMark ? 
          latestSubmission.submissionMark.points : 'N/A',
        feedback: latestSubmission && latestSubmission.submissionMark ? 
          latestSubmission.submissionMark.submissionFeedback : 'No feedback provided',
        remarks: latestSubmission && latestSubmission.submissionMark ? 
          latestSubmission.submissionMark.taskRemarks : 'No remarks provided',
        reviewedBy: essayData.status === 'reviewed' ? (essayData.updatedBy || 'Tutor User') : '',
        // Add skin data if available
        skinData: skinData || null
      });
    }
  }, [essayData, skinData]);

  // Handle back button click
  const handleBackClick = () => {
    router.back();
  };

  // Loading state - show loading when either API is loading
  if (isEssayLoading || (taskId && userId && isSkinLoading)) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-white">
        <div className="flex flex-col items-center space-y-4">
          <div className="w-16 h-16 border-4 border-amber-500 border-t-transparent rounded-full animate-spin"></div>
          <p className="text-gray-600">Loading essay details...</p>
        </div>
      </div>
    );
  }

  // Error state - show error if either API fails
  if (essayError || (taskId && userId && skinError) || !essay) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-white">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md">
          <h2 className="text-red-600 text-lg font-semibold mb-2">Error Loading Essay</h2>
          <p className="text-gray-700 mb-4">
            We couldn't load the essay details. Please try again or contact support.
          </p>
          <button
            onClick={handleBackClick}
            className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
          >
            Back to List
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white min-h-screen w-full">
      <div className="max-w-7xl mx-auto pt-16 right-0 sm:px-6 lg:px-8">
        {/* Header with view essay title */}
        <h1 className="font-normal mb-4 text-xl">View Essay</h1>
        
        {/* User info and date section */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
          <div className="flex flex-col items-start space-y-1 mb-4 mt-3 sm:mb-0">
            <span className="text-sm font-normal">Submitted by:</span>
            <div className="flex items-center space-x-2">
              <div className="w-6 h-6 rounded-full bg-amber-100 flex items-center justify-center text-amber-800 overflow-hidden mt-2">
                {/* User Avatar Circle */}
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                </svg>
              </div>
              <span className="text-xs text-gray-700 mt-2">{essay.submittedBy}</span>
            </div>
          </div>
          
          <div className="flex flex-col items-start sm:items-center text-xs text-gray-700 space-y-1">
            <span className="text-sm font-normal">Date:</span>
            <div className="flex items-center space-x-2">
              <div className="text-yellow-500 bg-yellow-50 rounded-full p-1 flex items-center justify-center">
                {/* Calendar icon */}
                <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                  <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                </svg>
              </div>
              <span>{essay.submissionDate}</span>
            </div>
          </div>
        </div>
        
        {/* Main content grid */}
        <div className="flex flex-col lg:flex-row bg-[#FDE7E9] rounded-md mt-9">
          {/* Left Panel - Frame with decorative elements */}
          <div className="w-full lg:w-1/2 p-1 relative">
            <div className="w-full h-[500px] flex items-center justify-center overflow-hidden">
              <div className="bg-white h-full flex items-center justify-center p-2 overflow-hidden shadow-xl">
                <div
                  className="canvas-container-wrapper"
                  style={{ width: '100%', height: '100%', padding: '20px' }}
                >
                  {skinData?.moduleDefaultSkin && (
                    <DiaryCanvas data={skinData.moduleDefaultSkin} />
                  )}            
                </div>
              </div>
            </div>
          </div>
          
          {/* Right Panel - Editor sections */}
          <div className="w-[600px] flex flex-col  space-y-3 p-0 mt-2">
            <div className='bg-white p-2'>
              {/* Top right box - Original Essay */}
              <div className="bg-white border border-white shadow-lg rounded-lg p-4 flex-1">
                <div className="flex justify-between mb-2">
                  <span className="font-semibold text-sm">{essay.title}</span>
                  <span className="text-xs">Date: {essay.originalDate}</span>
                </div>
                <div className="h-40 overflow-auto bg-white text-xs text-gray-700 p-2 border border-gray-200 rounded">
                  <p>{essay.content}</p>
                </div>
              </div>
              
              {/* Bottom right box - Tutor Editing Zone */}
              <div className="bg-white border border-white shadow-lg rounded-lg p-4 flex-1 mt-2">
                <div className="text-center text-xs text-yellow-700 mb-2">
                  Tutor Editing Zone
                </div>
                <div className="flex justify-between mb-1">
                  <span className="font-semibold text-sm">{essay.title}</span>
                  <span className="text-xs">Date: {essay.tutorEditDate}</span>
                </div>
                <div className="h-40 overflow-auto bg-white text-xs text-gray-700 p-2 border border-gray-200 rounded relative">
                  <p>{essay.feedback}</p>
                  
                 <button
                onClick={() => setShowFeedbackModal(true)}
                className=" absolute bottom-4 right-1 flex items-center hover:opacity-80 transition-opacity "
              >
                <Image
                  src="/assets/images/all-img/feedback-bg.png"
                  alt="Teacher Feedback"
                  width={150}
                  height={50}
                  className="h-10 w-auto"
                  priority
                />
              </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      
       
      </div>
      
      {/* TeacherFeedbackModal */}
      {showFeedbackModal && (
        <TeacherFeedbackModal 
          essay={essay} 
          onClose={() => setShowFeedbackModal(false)} 
        />
      )}
    </div>
  );
};

export default EssayDetails;