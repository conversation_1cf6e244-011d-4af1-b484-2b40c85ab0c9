'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import { motion, AnimatePresence } from 'framer-motion';
import { toast } from 'sonner';
import api from '@/lib/api';

const EssayFeedbackModal = ({
  isOpen,
  onClose,
  submissionId,
  feedbacks = '',
  onSuccess,
  isPostMethod
}) => {
  const [feedback, setFeedback] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Handle feedback submission
  const handleSubmit = async () => {
    if (!feedback.trim()) {
      toast.error('Please enter feedback before submitting');
      return;
    }

    setIsSubmitting(true);
    try {
      const response = await api[isPostMethod ? 'post' : 'patch'](`/tutor-essay/markEssay`, {
        submissionId,
        submissionFeedback: feedback.trim(),
      });

      if (response.success) {
        setFeedback('');
        if (onSuccess) {
          onSuccess();
        }
        onClose();
      }
    } catch (error) {
      console.error('Error submitting feedback:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle modal close
  const handleClose = () => {
    if (!isSubmitting) {
      setFeedback('');
      onClose();
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onClick={handleClose}
        >
          <motion.div
            className="bg-white rounded-xl shadow-xl w-full max-w-2xl overflow-hidden relative"
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            transition={{ type: 'spring', damping: 20, stiffness: 300 }}
            onClick={(e) => e.stopPropagation()}
          >
            {/* Wooden sign header */}
            <div className="relative">
              <div className="bg-[#FFF9FB] pt-2 pb-2 px-2 shadow-xl flex justify-center">
                <Image
                  src="/assets/images/all-img/wooden-feedback-sign.png"
                  alt="Teacher's Feedback"
                  width={300}
                  height={80}
                  priority
                />
              </div>
            </div>

            {/* Modal content */}
            <div className="p-6">
              <div className="max-h-60 overflow-y-auto mb-5">
                <h3 className="text-sm font-semibold text-gray-700">Feedback:</h3>
                <ul className="space-y-1">
                  {feedbacks ? (
                    <li>{feedbacks}</li>
                  ) : (
                    <li>No feedback given yet. </li>
                  )}
                </ul>
              </div>
              {/* Feedback Input */}
              <div className="mb-4">
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  Teachers Feedback
                </label>
                <textarea
                  value={feedback}
                  onChange={(e) => setFeedback(e.target.value)}
                  className="w-full p-3 border border-gray-300 rounded-lg h-32"
                  placeholder="Write here"
                  disabled={isSubmitting}
                />
              </div>

              {/* Action buttons */}
              <div className="flex justify-end space-x-3 mt-6">
                <button
                  type="button"
                  onClick={handleClose}
                  disabled={isSubmitting}
                  className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 disabled:opacity-50"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={handleSubmit}
                  disabled={isSubmitting || !feedback.trim()}
                  className="px-4 py-2 bg-yellow-500 text-white rounded-md hover:bg-yellow-600 disabled:opacity-50"
                >
                  {isSubmitting ? 'Submitting...' : 'Confirm'}
                </button>
              </div>
            </div>

            {/* Close button */}
            <button
              className="absolute top-2 right-2 text-gray-500 hover:text-gray-700"
              onClick={handleClose}
              disabled={isSubmitting}
            >
              <Image
                src="/assets/images/all-img/cross-bg.png"
                alt="Close"
                width={40}
                height={40}
                className="w-full h-auto"
                priority
              />
            </button>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default EssayFeedbackModal;
