'use client';
import React from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { Icon } from '@iconify/react';

const HecNovelStudents = ({ students = [] }) => {
    const router = useRouter();

    if (students.length === 0) {
        return (
            <div className="flex items-center justify-center h-64">
                <div className="text-center">
                    <Icon icon="mdi:book-open-page-variant-outline" className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                    <p className="text-gray-500 text-lg">No HEC Novel students found</p>
                    <p className="text-gray-400 text-sm">Students will appear here when they are assigned to the HEC Novel module</p>
                </div>
            </div>
        );
    }

    return (
        <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                    <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            #
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            User Name
                        </th>
                        {/* <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Assign Task
                        </th> */}
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Action
                        </th>
                    </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                    {students.map((student, index) => (
                        <tr key={student.id} className="hover:bg-gray-50">
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {index + 1}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                                <div className="flex items-center">
                                    <div className="w-10 h-10 rounded-full bg-yellow-200 flex items-center justify-center mr-3 overflow-hidden">
                                        <Image 
                                            src={student?.profilePicture || '/assets/images/all-img/avatar.png'} 
                                            alt={student.name || 'Profile'} 
                                            width={40} 
                                            height={40}
                                            className="w-full h-full object-cover"
                                        />
                                    </div>
                                    <div>
                                        <div className="text-sm font-medium text-gray-900">
                                            {student.name}
                                        </div>
                                        <div className="text-sm text-gray-500">
                                            {student.email}
                                        </div>
                                    </div>
                                </div>
                            </td>
                            {/* <td className="px-6 py-4 whitespace-nowrap">
                                <span className="text-sm text-gray-500">
                                    No task assignment available
                                </span>
                            </td> */}
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button
                                    onClick={() => router.push(`/dashboard/students/profile/${student.id}`)}
                                    className="text-blue-600 hover:text-blue-900 p-2 rounded-md hover:bg-blue-50 transition-colors"
                                    title="View Profile"
                                >
                                    <Icon icon="material-symbols:visibility" className="w-5 h-5" />
                                </button>
                            </td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
};

export default HecNovelStudents;
