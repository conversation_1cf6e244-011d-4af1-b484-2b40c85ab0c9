import React, { use, useEffect, useRef } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Icon } from '@iconify/react';
import { motion, AnimatePresence } from 'framer-motion';
import { useRouter } from 'next/navigation';
import {
  closeNotificationPanel,
  fetchNotifications,
  fetchUnreadCount,
  markAsRead,
  markAllAsRead,
  selectNotifications,
  selectNotificationPanelState,
  selectNotificationMeta,
  selectNotificationLoading,
} from '@/store/features/notificationSlice';
import NotificationItem from './NotificationItem';
import { setFriendActiveTab } from '@/store/features/commonSlice';

const NotificationPanel = () => {
  const dispatch = useDispatch();
  const router = useRouter();
  const panelRef = useRef(null);
  const isOpen = useSelector(selectNotificationPanelState);
  const notifications = useSelector(selectNotifications);
  const meta = useSelector(selectNotificationMeta);
  const loading = useSelector(selectNotificationLoading);
  const { isAuth } = useSelector((state) => state.auth);
  const { user } = useSelector((state) => state.auth);
  const isStudent = user?.type === 'student';
  const isTutor = user?.type === 'tutor';
  const isAdmin = user?.type === 'admin';

  // Fetch notifications when panel opens and user is authenticated
  useEffect(() => {
    if (isOpen && isAuth) {
      dispatch(fetchNotifications({}));
    }
  }, [isOpen, dispatch, isAuth]);

  // Close panel when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (panelRef.current && !panelRef.current.contains(event.target)) {
        dispatch(closeNotificationPanel());
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, dispatch]);

  const handleMarkAllAsRead = () => {
    if (isAuth) {
      dispatch(markAllAsRead()).then(() => {
        // Update unread count after marking all as read
        dispatch(fetchUnreadCount());
      });
    }
  };

  const handleLoadMore = () => {
    if (isAuth && meta.currentPage < meta.totalPages) {
      dispatch(fetchNotifications({ page: meta.currentPage + 1 }));
    }
  };

  const handleNotificationClick = (notification) => {
    // Mark as read if authenticated
    const isUnread =
      notification.isRead === false || notification.read === false;
    if (isAuth && isUnread) {
      dispatch(markAsRead(notification.id)).then(() => {
        // Update unread count after marking as read
        dispatch(fetchUnreadCount());
      });
    }
    console.log(notification);
    // Handle navigation based on notification type and relatedEntityType
    switch (notification.type) {
      case 'tutor_assignment':
        // Navigate to students page for tutor assignment notifications
        router.push('/tutors?planFeatureType=hec_user_diary');
        break;
      case 'tutor_verification':
        // Navigate to member management page for tutor verification
        router.push('/dashboard/member-management/manage-request');
        break;
      case 'system':
        // For system notifications, no specific navigation
        break;
      case 'chat_message':
        // Navigate to chat app for chat message notifications
        router.push(`/chat-app?conversationId=${notification.relatedEntityId}`);
        break;
      case 'qa_review':
        router.push(`/question-answer?taskId=${notification.relatedEntityId}`);
        break;
      case 'DIARY_COMMENT':
        router.push(`/diary/my/item?entryId=${notification.relatedEntityId}`);
        break;
      case 'diary_review':
        router.push(`/diary/my/item?entryId=${notification.relatedEntityId}`);
        break;
      case 'diary_feedback':
        router.push(
          `/diary/my/item?entryId=${
            notification.relatedEntityId
          }&review=${true}`
        );
        break;
      case 'DIARY_REVIEWED':
        router.push(`/diary/my/item?entryId=${notification.relatedEntityId}`);
        break;
      case 'DIARY_SUBMITED':
        router.push(`/diary/my/item?entryId=${notification.relatedEntityId}`);
        break;
      case 'TUTOR_ASSIGNED':
        router.push(`tutors/profile/${notification.relatedEntityId}`);
        break;
      case 'NEW_MESSAGE':
        router.push(`/chat-app?conversationId=${notification.relatedEntityId}`);
        break;
      case 'PLAN_PURCHASED':
        router.push(`/subscriptions/${notification.relatedEntityId}`);
        break;
      case 'ESSAY_SUBMITTED':
        router.push(`/essay/list/${notification.relatedEntityId}`);
        break;
      case 'essay_review':
        router.push(`/essay/list/${notification.relatedEntityId}`);
        break;
      case 'ESSAY_REVIEWED':
        router.push(`/essay/list/${notification.relatedEntityId}`);
        break;
      case 'novel_submission':
        // Navigate to novel submission review page with dynamic relatedEntityId
        router.push(
          `/dashboard/submission-management/hec-novel/review/${notification.relatedEntityId}?tab=NovelSubmissionList`
        );
        break;
      case 'diary_submission':
        // Navigate to diary submission review page with dynamic relatedEntityId
        router.push(
          `/dashboard/submission-management/hec-diary/review/${notification.relatedEntityId}?status=pending`
        );
        break;
      case 'essay_submission':
        // Navigate to essay submission review page with dynamic relatedEntityId
        router.push(
          `/dashboard/submission-management/hec-essay/review/${notification.relatedEntityId}?tab=essaySubmissions`
        );
        break;
      case 'mission_diary_submission':
        // Navigate to mission diary submission review page with dynamic relatedEntityId
        router.push(
          `/dashboard/submission-management/hec-diary/review/mission/${notification.relatedEntityId}?tab=missionDiary`
        );
        break;
      case 'mission_essay_submission':
        // Navigate to mission essay submission review page with dynamic relatedEntityId
        router.push(
          `/dashboard/submission-management/hec-essay/review/mission/${notification.relatedEntityId}?tab=missionEssay`
        );
        break;
      case 'diary_update':
        // Navigate to diary entry for diary update notifications
        if (notification.relatedEntityId) {
          router.push(
            `/dashboard/submission-management/hec-diary/review/${notification.relatedEntityId}?status=pending`
          );
        } else {
          router.push('/dashboard/submission-management/hec-diary');
        }
        break;
      case 'mission_submission':
        // Navigate to mission diary submission review page
        if (notification.relatedEntityId) {
          router.push(
            `/dashboard/submission-management/hec-diary/review/mission/${notification.relatedEntityId}?tab=missionDiary`
          );
        } else {
          router.push(
            '/dashboard/submission-management/hec-diary?tab=missionDiary'
          );
        }
        break;
      case 'story_submission':
        // Navigate to story submission review page
        if (notification.relatedEntityId) {
          router.push(
            `/dashboard/submission-management/hec-play/storymaker/review/${notification.relatedEntityId}`
          );
        } else {
          router.push(
            '/dashboard/submission-management/hec-play?tab=storymaker'
          );
        }
        break;
      case 'story_maker_evaluated':
        if (notification.relatedEntityId && isStudent) {
          router.push(
            `/story-maker/${notification?.relatedEntityId}/submission`
          );
        }
        break;
      case 'diary_follow_request':
        if (notification?.relatedEntityId) {
          dispatch(setFriendActiveTab('requests'));
          router.push('/invite-friends');
        }
        break;
      case 'story_maker_submitted':
        if (notification.relatedEntityId) {
          router.push(
            `/story-maker/${notification?.relatedEntityId}/submission`
          );
        }
        break;
      case 'diary_friend_share':
        if (notification?.relatedEntityId) {
          router.push(`/diary/shared/${notification.relatedEntityId}`);
        }
        break;
      default:
        // For notifications without specific actions
        console.log(
          'No specific action for notification type:',
          notification.type
        );
        break;
    }

    // Close the panel after clicking
    dispatch(closeNotificationPanel());
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          ref={panelRef}
          className="absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg overflow-hidden z-50"
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          transition={{ duration: 0.2 }}
        >
          <div className="p-4 border-b border-gray-200 flex justify-between items-center">
            <h3 className="text-lg font-semibold text-gray-800">
              Notifications
            </h3>
            <button
              className="text-gray-500 hover:text-gray-700"
              onClick={handleMarkAllAsRead}
              disabled={loading}
            >
              Mark all as read
            </button>
          </div>

          <div className="max-h-96 overflow-y-auto">
            {loading && notifications.length === 0 ? (
              <div className="p-4 text-center text-gray-500">
                <Icon
                  icon="lucide:loader"
                  className="w-6 h-6 mx-auto animate-spin"
                />
                <p className="mt-2">Loading notifications...</p>
              </div>
            ) : notifications.length === 0 ? (
              <div className="p-8 text-center text-gray-500">
                <Icon icon="lucide:bell-off" className="w-8 h-8 mx-auto mb-2" />
                <p>No notifications yet</p>
              </div>
            ) : (
              <div>
                {notifications.map((notification) => (
                  <NotificationItem
                    key={notification.id}
                    notification={notification}
                    onClick={() => handleNotificationClick(notification)}
                  />
                ))}

                {meta.currentPage < meta.totalPages && (
                  <div className="p-3 text-center">
                    <button
                      className="text-yellow-600 hover:text-yellow-700 text-sm font-medium"
                      onClick={handleLoadMore}
                      disabled={loading}
                    >
                      {loading ? 'Loading...' : 'Load more'}
                    </button>
                  </div>
                )}
              </div>
            )}
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default NotificationPanel;
