'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import MissionQAList from './_components/MissionQAList';
import QuestionSubmission from './_components/QuestionSubmission';
import HecQALayout from './review/_components/HecQALayout';

const HecNovel = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const tabParam = searchParams.get('tab');

  // Set default active tab or use the one from URL
  const [activeTab, setActiveTab] = useState(tabParam || 'questionSubmission');

  // Update state when URL changes
  useEffect(() => {
    if (tabParam) {
      setActiveTab(tabParam);
    }
  }, [tabParam]);

  
  // Function to render the correct content based on active tab
  const renderContent = () => {
    switch(activeTab) {
      case 'questionSubmission':
        return <QuestionSubmission />;
      case 'missionQAList':
        return <MissionQAList />;
      default:
        return <div>Select a tab</div>;
    }
  };

  return (
    <HecQALayout activeTab={activeTab}>
      {renderContent()}
    </HecQALayout>
  );
};

export default HecNovel;