'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import { motion, AnimatePresence } from 'framer-motion';
import { toast } from 'sonner';
import api from '@/lib/api';

const FeedbackModal = ({
  isOpen,
  onClose,
  entryId,
  submissionId,
  feedbacks = [],
  refetch,
  marking,
  submissionHistory,
}) => {
  // Use whichever ID is provided
  const actualSubmissionId = submissionId || entryId;
  const [feedback, setFeedback] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Handle feedback submission - Only send feedback, don't override score or corrections
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!feedback.trim()) {
      toast.error('Please enter feedback before submitting');
      return;
    }

    if (!actualSubmissionId) {
      toast.error('Submission ID is missing');
      return;
    }

    setIsSubmitting(true);
    
    console.log('FeedbackModal - Submitting feedback only:', {
      submissionId: actualSubmissionId,
      feedback: feedback.trim()
    });

    try {
      // Use the PUT endpoint and only send feedback to avoid overwriting other fields
      const response = await api.put(`/tutor/qa/submissions/${actualSubmissionId}/review`, {
        feedback: feedback.trim()
        // Don't send score or corrections to preserve existing values
      });

      console.log('FeedbackModal - API Response:', response);

      if (response.success) { 
        setFeedback('');
        if (refetch) {
          refetch();
        }
        onClose();
      } else {
        throw new Error(response.message || 'Failed to submit feedback');
      }
    } catch (error) {
      console.error('FeedbackModal - Error submitting feedback:', error);
      console.error('FeedbackModal - Full error:', error.response?.data);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle modal close
  const handleClose = () => {
    if (!isSubmitting) {
      setFeedback('');
      onClose();
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onClick={handleClose}
        >
          <motion.div
            className="bg-white rounded-xl shadow-xl w-full max-w-2xl overflow-hidden relative max-h-[90vh] overflow-y-auto"
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            transition={{ type: 'spring', damping: 20, stiffness: 300 }}
            onClick={(e) => e.stopPropagation()}
          >
            {/* Wooden sign header */}
            <div className="relative">
              <div className="bg-[#FFF9FB] pt-2 pb-2 px-2 shadow-xl flex justify-center">
                <Image
                  src="/assets/images/all-img/wooden-feedback-sign.png"
                  alt="Teacher's Feedback"
                  width={300}
                  height={80}
                  priority
                />
              </div>
            </div>

            {/* Modal content */}
            <div className="p-6">
              {/* Previous Feedbacks Section */}
              <div className="max-h-60 overflow-y-auto mb-5">
                <h3 className="text-lg font-semibold text-gray-800 mb-3">Previous Feedback:</h3>
                <div className="space-y-3">
                  {/* Show current submission feedback from submissionHistory */}
                  {submissionHistory?.feedback && (
                    <div className="p-3 bg-blue-50 rounded-lg border-l-4 border-yellow-400 shadow-sm">
                      <div className="flex justify-between items-center mb-1">
                        <span className="text-xs text-yellow-600 font-medium">
                          Latest Submission Feedback
                        </span>
                        {submissionHistory?.updatedAt && (
                          <span className="text-xs text-gray-500">
                            {new Date(submissionHistory.updatedAt).toLocaleDateString('en-US', {
                              year: 'numeric',
                              month: 'short',
                              day: 'numeric',
                              hour: '2-digit',
                              minute: '2-digit'
                            })}
                          </span>
                        )}
                      </div>
                      <p className="text-black text-sm leading-relaxed mb-2">
                        {submissionHistory.feedback}
                      </p>
                      
                      {/* Show grammar corrections if they exist */}
                      {submissionHistory?.corrections?.grammar?.length > 0 && (
                        <div className="mt-2">
                          <span className="text-xs font-medium text-gray-600">Grammar Corrections:</span>
                          <ul className="list-disc list-inside text-xs text-gray-700 mt-1">
                            {submissionHistory.corrections.grammar.map((error, idx) => (
                              <li key={idx}>{error}</li>
                            ))}
                          </ul>
                        </div>
                      )}
                      
                     
                    </div>
                  )}

                  {/* Show marking submission feedback if exists and different from submission history */}
                  {marking?.submissionFeedback && marking.submissionFeedback !== submissionHistory?.feedback && (
                    <div className="p-3 bg-green-50 rounded-lg border-l-4 border-green-400 shadow-sm">
                      <div className="flex justify-between items-center mb-1">
                        <span className="text-xs text-green-600 font-medium">
                          Marking Feedback
                        </span>
                        {marking?.updatedAt && (
                          <span className="text-xs text-gray-500">
                            {new Date(marking.updatedAt).toLocaleDateString('en-US', {
                              year: 'numeric',
                              month: 'short',
                              day: 'numeric',
                              hour: '2-digit',
                              minute: '2-digit'
                            })}
                          </span>
                        )}
                      </div>
                      <p className="text-green-800 text-sm leading-relaxed mb-2">
                        {marking.submissionFeedback}
                      </p>
                      {marking?.corrections?.grammar?.length > 0 && (
                        <div className="mt-2">
                          <span className="text-xs font-medium text-gray-600">Grammar Corrections:</span>
                          <ul className="list-disc list-inside text-xs text-gray-700 mt-1">
                            {marking.corrections.grammar.map((error, idx) => (
                              <li key={idx}>{error}</li>
                            ))}
                          </ul>
                        </div>
                      )}
                      {marking?.score && (
                        <div className="mt-2 text-xs text-gray-500">
                          Score: <span className="font-medium text-green-600">{marking.score}</span>
                        </div>
                      )}
                    </div>
                  )}
                  
                  {/* Show other historical feedbacks if any */}
                  {feedbacks?.length > 0 && (
                    feedbacks?.map((item, idx) => (
                      <div key={idx} className="p-3 bg-gray-50 rounded-lg border-l-4 border-gray-400 shadow-sm">
                        <div className="flex justify-between items-center mb-1">
                          <span className="text-xs text-gray-600 font-medium">
                            Historical Feedback #{idx + 1}
                          </span>
                          {item?.createdAt && (
                            <span className="text-xs text-gray-500">
                              {new Date(item.createdAt).toLocaleDateString('en-US', {
                                year: 'numeric',
                                month: 'short',
                                day: 'numeric',
                                hour: '2-digit',
                                minute: '2-digit'
                              })}
                            </span>
                          )}
                        </div>
                        <p className="text-gray-800 text-sm leading-relaxed">
                          {item?.submissionFeedback || item?.feedback || item?.content || 'No feedback content'}
                        </p>
                        {item?.corrections?.grammar?.length > 0 && (
                          <div className="mt-2">
                            <span className="text-xs font-medium text-gray-600">Grammar Corrections:</span>
                            <ul className="list-disc list-inside text-xs text-gray-700 mt-1">
                              {item.corrections.grammar.map((error, idx) => (
                                <li key={idx}>{error}</li>
                              ))}
                            </ul>
                          </div>
                        )}
                        {item?.givenBy && (
                          <p className="text-xs text-gray-500 mt-1">
                            By: {item.givenBy}
                          </p>
                        )}
                      </div>
                    ))
                  )}

                  {/* Show empty state only if no feedback exists at all */}
                  {!submissionHistory?.feedback && !marking?.submissionFeedback && (!feedbacks || feedbacks.length === 0) && (
                    <div className="text-center py-6">
                      <div className="text-gray-400 text-4xl mb-2">📝</div>
                      <p className="text-gray-500 italic">No feedback given yet.</p>
                      <p className="text-xs text-gray-400 mt-1">Be the first to provide feedback!</p>
                    </div>
                  )}
                </div>
              </div>
              
              <form onSubmit={handleSubmit}>
                <div className="mb-4">
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    Add New Feedback
                    <span className="text-xs text-gray-500 font-normal block">
                      (This will only update the feedback field and won't affect existing scores or corrections)
                    </span>
                  </label>
                  <textarea
                    value={feedback}
                    onChange={(e) => setFeedback(e.target.value)}
                    className="w-full p-3 border border-gray-300 rounded-lg h-32 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Write your feedback here..."
                    disabled={isSubmitting}
                  />
                </div>

                {/* Action buttons */}
                <div className="flex justify-end space-x-3 mt-6">
                  <button
                    type="button"
                    onClick={handleClose}
                    disabled={isSubmitting}
                    className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 disabled:opacity-50 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={isSubmitting || !feedback.trim()}
                    className="px-4 py-2 bg-yellow-500 text-white rounded-md hover:bg-yellow-600 disabled:opacity-50 transition-colors flex items-center gap-2"
                  >
                    {isSubmitting ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                        Submitting...
                      </>
                    ) : (
                      'Submit Feedback'
                    )}
                  </button>
                </div>
              </form>
            </div>

            {/* Close button */}
            <button
              className="absolute top-2 right-2 text-gray-500 hover:text-gray-700 transition-colors"
              onClick={handleClose}
              disabled={isSubmitting}
            >
              <Image
                src="/assets/images/all-img/cross-bg.png"
                alt="Close"
                width={40}
                height={40}
                className="w-full h-auto"
                priority
              />
            </button>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default FeedbackModal;