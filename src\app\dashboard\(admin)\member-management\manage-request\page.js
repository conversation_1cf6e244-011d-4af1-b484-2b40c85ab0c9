'use client';

import NewTablePage from "@/components/form/NewTablePage";
import useDataFetch from "@/hooks/useDataFetch";
import React, { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import api from '@/lib/api';
import { useRouter } from 'next/navigation';

// Add these imports for the modal
import { FiXCircle } from 'react-icons/fi';
import { motion } from 'framer-motion';

const ManageLists = () => {

    const router = useRouter();
    // Handle back button click - moved inside the component
    const handleBackClick = () => {
      router.back();
    };
    
  // Pagination states
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  
  // Query params now includes pagination
  const [queryParams, setQueryParams] = useState({
    page: 1,
    limit: 10
  });
  
  const [itemStatuses, setItemStatuses] = useState({});
  
  // Modal states
  const [isRejectModalOpen, setIsRejectModalOpen] = useState(false);
  const [isApproveModalOpen, setIsApproveModalOpen] = useState(false);
  const [rejectionNotes, setRejectionNotes] = useState('');
  const [approvalNotes, setApprovalNotes] = useState('');
  const [currentRow, setCurrentRow] = useState(null);
  
  // Update query params when pagination changes
  useEffect(() => {
    setQueryParams(prev => ({
      ...prev,
      page: currentPage,
      limit: rowsPerPage
    }));
  }, [currentPage, rowsPerPage]);
  
  // Fetch tutor approval data using the custom hook
  const {
    data: response,
    isLoading,
    error,
    refetch
  } = useDataFetch({
    queryKey: ['tutor-approval', queryParams],
    endPoint: '/tutor-approval',
    params: queryParams
  });

  // Extract items data
  const items = response?.items || [];
  
  // Function to get total count handling different response structures
  const getTotalCount = () => {
    if (!response) return 0;
    
    // First try to get from meta.totalItems (current structure)
    if (response.meta?.totalItems !== undefined) {
      return response.meta.totalItems;
    }
    
    // Fallback to response.totalCount if available
    if (response.totalCount !== undefined) {
      return response.totalCount;
    }
    
    // Last resort: count the items in the array
    return (response.items || []).length;
  };
  
  // Extract metadata for pagination
  const totalItems = getTotalCount();
  const totalPages = response?.meta?.totalPages || Math.ceil(totalItems / rowsPerPage) || 1;
  
  // Handle page change
  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  // Log the response to debug
  useEffect(() => {
    console.log("API Response:", response);
  }, [response]);

  // Initialize statuses from fetched data
  useEffect(() => {
    if (items.length > 0) {
      const initialStatuses = {};
      items.forEach(item => {
        initialStatuses[item.id] = item.status || 'pending';
      });
      setItemStatuses(prev => ({...prev, ...initialStatuses}));
    }
  }, [items]);
  
  // Open the approval modal
  const openApproveModal = (row) => {
    setCurrentRow(row);
    setApprovalNotes('Approved after verifying credentials');
    setIsApproveModalOpen(true);
  };
  
  // Handle the actual approval after notes are entered
  const handleApprove = async () => {
    if (!currentRow) return;
    
    try {
      // Optimistically update UI
      setItemStatuses(prev => ({
        ...prev,
        [currentRow.id]: 'approved'
      }));
      
      // Close the modal
      setIsApproveModalOpen(false);
      
      // Make API call to approve tutor using the api instance
      const approveResponse = await api.post(`/tutor-approval/approve`, {
        approvalId: currentRow.id,
        adminNotes: approvalNotes || "Approved after verifying credentials"
      });
      
      console.log('Approve API response:', approveResponse);
      
      // Refresh data
      refetch();
    } catch (error) {
      console.error('Error approving tutor:', error);
      
      // Revert status on error
      setItemStatuses(prev => ({
        ...prev,
        [currentRow.id]: 'pending'
      }));
    }
  };

  // Open the rejection modal
  const openRejectModal = (row) => {
    setCurrentRow(row);
    setRejectionNotes('Rejected due to incomplete information');
    setIsRejectModalOpen(true);
  };
  
  // Handle the actual rejection after notes are entered
  const handleReject = async () => {
    if (!currentRow) return;
    
    try {
      // Optimistically update UI
      setItemStatuses(prev => ({
        ...prev,
        [currentRow.id]: 'rejected'
      }));
      
      // Close the modal
      setIsRejectModalOpen(false);
      
      // First, get the tutor approval details by ID
      const getResponse = await api.get(`/tutor-approval/${currentRow.id}`);
      const approvalId = getResponse.id || currentRow.id;
      
      // Then, make API call to reject tutor with correct payload
      const rejectResponse = await api.post(`/tutor-approval/reject`, {
        approvalId: approvalId,
        adminNotes: rejectionNotes || "Rejected due to incomplete information",
        rejectionReason: "Missing required qualifications"
      });
      
      console.log('Reject API response:', rejectResponse);
      
      // Refresh data
      refetch();
    } catch (error) {
      console.error('Error rejecting tutor:', error);
      
      // Revert status on error
      setItemStatuses(prev => ({
        ...prev,
        [currentRow.id]: prev[currentRow.id] || 'pending'
      }));
    }
  };
  
  const preparedData = items.map(item => {
    // Extract user fields to the top level for the table display
    const preparedItem = { 
      ...item,
      name: item.user?.name || 'N/A',
      userId : item.user?.userId || 'N/A',
      email: item.user?.email || 'N/A',
      phoneNumber: item.user?.phoneNumber || 'N/A'
    };
    
    const status = itemStatuses[item.id] || 'pending';
    
    if (status === 'approved') {
      preparedItem.action = (
        <button 
          className="bg-green-100 text-green-800 px-4 py-1 rounded text-sm font-bold"
          disabled
        >
          Approved ✓
        </button>
      );
    } else if (status === 'rejected') {
      preparedItem.action = (
        <button 
          className="bg-red-100 text-red-800 px-4 py-2 rounded text-sm font-bold"
          disabled
        >
          Rejected ✕
        </button>
      );
    } else {
      preparedItem.action = (
        <div className="flex space-x-2">
          <button 
            className="bg-[#FFDE34] text-white px-4 py-1 rounded text-sm font-bold"
            onClick={() => openApproveModal(item)}
          >
            Approve
          </button>
          <button 
            className="bg-red-500 text-white px-4 py-1 rounded text-sm font-bold"
            onClick={() => openRejectModal(item)}
          >
            Reject
          </button>
        </div>
      );
    }
    
    return preparedItem;
  });

 const UserAvatar = ({ name }) => {
    return (
      <div className="flex items-center justify-center h-8 w-8 rounded-full bg-amber-100 text-amber-800">
        <svg 
          className="h-5 w-5" 
          xmlns="http://www.w3.org/2000/svg" 
          viewBox="0 0 24 24" 
          fill="currentColor"
        >
          <path 
            fillRule="evenodd" 
            d="M7.5 6a4.5 4.5 0 119 0 4.5 4.5 0 01-9 0zM3.751 20.105a8.25 8.25 0 0116.498 0 .75.75 0 01-.437.695A18.683 18.683 0 0112 22.5c-2.786 0-5.433-.608-7.812-1.7a.75.75 0 01-.437-.695z" 
            clipRule="evenodd" 
          />
        </svg>
      </div>
    );
  };
  const tutorColumns = [
    {
      label: 'TUTOR NAME',
      field: 'name',
      cellRenderer: (value, row) => (
        <div className="flex items-center space-x-2">
          <UserAvatar name={value} />
          <span className="font-medium text-gray-900">{value}</span>
        </div>
      )
    },
    {
      label: 'TUTOR ID',
      field: 'userId',
    },
    {
      label: 'EMAIL ADDRESS',
      field: 'email',
    },
    {
      label: 'PHONE NUMBER',
      field: 'phoneNumber',
    },
    {
      label: 'ACTIONS',
      field: 'action',
    }
  ];

  // Cute Approval Modal Component
  const ApprovalModal = () => {
    if (!isApproveModalOpen) return null;
    
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 px-4">
        <motion.div 
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          className="bg-white rounded-2xl shadow-xl max-w-md w-full p-6 relative"
        >
          {/* Close button */}
          <button 
            onClick={() => setIsApproveModalOpen(false)}
            className="absolute top-4 right-4 text-gray-400 hover:text-gray-600"
          >
            <FiXCircle size={24} />
          </button>
          
          {/* Modal header with cute styling */}
          <div className="text-center mb-6">
            <div className="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-3xl" role="img" aria-label="happy face">😊</span>
            </div>
            <h3 className="text-xl font-bold text-gray-800">
              Approve {currentRow?.user?.name || 'Tutor'}
            </h3>
            <p className="text-gray-500 mt-1 text-sm">
              Please provide approval notes
            </p>
          </div>
          
          {/* Cute styled textarea */}
          <div className="mb-6">
            <textarea
              value={approvalNotes}
              onChange={(e) => setApprovalNotes(e.target.value)}
              placeholder="Enter your approval notes here..."
              className="w-full px-4 py-3 border-2 border-green-200 rounded-xl focus:border-green-400 focus:ring focus:ring-green-100 transition-all outline-none min-h-[120px] text-gray-700"
            />
          </div>
          
          {/* Cute action buttons */}
          <div className="flex items-center justify-end space-x-3">
            <button
              onClick={() => setIsApproveModalOpen(false)}
              className="px-5 py-2 bg-gray-100 hover:bg-gray-200 text-gray-800 rounded-xl font-medium transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={handleApprove}
              className="px-5 py-2 bg-[#FFDE34] hover:bg-[#FFD700] text-white rounded-xl font-medium transition-colors flex items-center"
            >
              <span className="mr-1">Approve</span>
              <span role="img" aria-label="approve">✅</span>
            </button>
          </div>
        </motion.div>
      </div>
    );
  };

  // Cute Rejection Modal Component
  const RejectionModal = () => {
    if (!isRejectModalOpen) return null;
    
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 px-4">
        <motion.div 
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          className="bg-white rounded-2xl shadow-xl max-w-md w-full p-6 relative"
        >
          {/* Close button */}
          <button 
            onClick={() => setIsRejectModalOpen(false)}
            className="absolute top-4 right-4 text-gray-400 hover:text-gray-600"
          >
            <FiXCircle size={24} />
          </button>
          
          {/* Modal header with cute styling */}
          <div className="text-center mb-6">
            <div className="bg-red-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-3xl" role="img" aria-label="sad face">😢</span>
            </div>
            <h3 className="text-xl font-bold text-gray-800">
              Reject {currentRow?.user?.name || 'Tutor'}
            </h3>
            <p className="text-gray-500 mt-1 text-sm">
              Please provide a reason for rejection
            </p>
          </div>
          
          {/* Cute styled textarea */}
          <div className="mb-6">
            <textarea
              value={rejectionNotes}
              onChange={(e) => setRejectionNotes(e.target.value)}
              placeholder="Enter your rejection notes here..."
              className="w-full px-4 py-3 border-2 border-red-200 rounded-xl focus:border-red-400 focus:ring focus:ring-red-100 transition-all outline-none min-h-[120px] text-gray-700"
            />
          </div>
          
          {/* Cute action buttons */}
          <div className="flex items-center justify-end space-x-3">
            <button
              onClick={() => setIsRejectModalOpen(false)}
              className="px-5 py-2 bg-gray-100 hover:bg-gray-200 text-gray-800 rounded-xl font-medium transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={handleReject}
              className="px-5 py-2 bg-red-500 hover:bg-red-600 text-white rounded-xl font-medium transition-colors flex items-center"
            >
              <span className="mr-1">Reject</span>
              <span role="img" aria-label="reject">❌</span>
            </button>
          </div>
        </motion.div>
      </div>
    );
  };

  if (error) {
    return (
      <div className="p-4 text-red-600">
        Error loading data: {error.message}
      </div>
    );
  }

  return (
    <div className="w-full px-4">
      

      <div className="overflow-auto max-h-[80vh]">
        <NewTablePage
          showSearch={false}
          showNameFilter={false}
          showSortFilter={false}
          showCreateButton={false}
          title="Manage Request"
          data={preparedData}
          columns={tutorColumns}
          loading={isLoading}
          onBack={handleBackClick}
          showCheckboxes={false}
          
          // Pagination props - added these from User_list
          currentPage={currentPage}
          totalPages={totalPages}
          changePage={handlePageChange}
          totalItems={totalItems}
          rowsPerPage={rowsPerPage}
          setRowsPerPage={setRowsPerPage}
          hideTableFooter={false} // Changed to false to show pagination
        />
      </div>
      
      {/* Render both modals */}
      <ApprovalModal />
      <RejectionModal />
    </div>
  );
};

export default ManageLists;