import React from 'react';
import { Icon } from '@iconify/react';
import { motion, AnimatePresence } from 'framer-motion';

const TimeExpiredModal = ({ isOpen, onSkip, onPlayAgain, currentQuestion, totalQuestions }) => {
  if (!isOpen) return null;

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 bg-black/60 backdrop-blur-sm"
          />
          
          {/* Modal */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 20 }}
            transition={{ type: "spring", duration: 0.5 }}
            className="relative bg-white rounded-2xl shadow-2xl max-w-md w-full mx-4 overflow-hidden"
          >
            {/* Header */}
            <div className="bg-gradient-to-r from-red-500 to-orange-500 p-6 text-white text-center">
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.2, type: "spring" }}
                className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4"
              >
                <Icon icon="mdi:clock-alert" className="w-8 h-8" />
              </motion.div>
              <h2 className="text-2xl font-bold mb-2">Time's Up!</h2>
              <p className="text-red-100">
                The time limit for this question has expired.
              </p>
            </div>

            {/* Content */}
            <div className="p-6">
              <div className="text-center mb-6">
                <div className="bg-gray-100 rounded-lg p-4 mb-4">
                  <p className="text-sm text-gray-600 mb-1">Current Progress</p>
                  <p className="text-lg font-semibold text-gray-800">
                    Question {currentQuestion} of {totalQuestions}
                  </p>
                </div>
                <p className="text-gray-600">
                  What would you like to do next?
                </p>
              </div>

              {/* Action Buttons */}
              <div className="space-y-3">
                {/* Play Again Button */}
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={onPlayAgain}
                  className="w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold py-3 px-6 rounded-xl flex items-center justify-center gap-3 transition-all duration-200 shadow-lg hover:shadow-xl"
                >
                  <Icon icon="mdi:refresh" className="w-5 h-5" />
                  Try This Question Again
                </motion.button>

                {/* Skip Button */}
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={onSkip}
                  className="w-full bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white font-semibold py-3 px-6 rounded-xl flex items-center justify-center gap-3 transition-all duration-200 shadow-lg hover:shadow-xl"
                >
                  <Icon icon="mdi:skip-next" className="w-5 h-5" />
                  {currentQuestion < totalQuestions ? 'Skip to Next Question' : 'Finish Game'}
                </motion.button>
              </div>

              {/* Additional Info */}
              <div className="mt-6 p-4 bg-yellow-50 rounded-lg border border-yellow-200">
                <div className="flex items-start gap-3">
                  <Icon icon="mdi:lightbulb" className="w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="text-sm font-medium text-yellow-800 mb-1">Tip</p>
                    <p className="text-sm text-yellow-700">
                      {currentQuestion < totalQuestions 
                        ? "You can retry this question or move on to the next one. Your progress will be saved either way!"
                        : "You can retry this question or finish the game to see your results."
                      }
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
};

export default TimeExpiredModal;
