import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Icon } from '@iconify/react';

const AchievementNotification = ({ 
  show, 
  title, 
  message, 
  icon = "mdi:trophy", 
  color = "text-yellow-600",
  bgColor = "bg-yellow-50",
  borderColor = "border-yellow-200",
  onComplete 
}) => {
  return (
    <AnimatePresence>
      {show && (
        <motion.div
          initial={{ opacity: 0, scale: 0.8, y: -50 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.8, y: -50 }}
          transition={{ 
            type: "spring", 
            stiffness: 300, 
            damping: 20,
            duration: 0.6 
          }}
          onAnimationComplete={() => {
            if (onComplete) {
              setTimeout(onComplete, 2000); // Auto-hide after 2 seconds
            }
          }}
          className="fixed top-20 left-1/2 transform -translate-x-1/2 z-50"
        >
          <motion.div
            animate={{ 
              boxShadow: [
                "0 4px 20px rgba(0,0,0,0.1)",
                "0 8px 30px rgba(0,0,0,0.2)",
                "0 4px 20px rgba(0,0,0,0.1)"
              ]
            }}
            transition={{ duration: 2, repeat: Infinity }}
            className={`${bgColor} ${borderColor} border-2 rounded-2xl p-4 shadow-lg max-w-sm`}
          >
            <div className="flex items-center gap-3">
              <motion.div
                animate={{ 
                  rotate: [0, 10, -10, 0],
                  scale: [1, 1.1, 1]
                }}
                transition={{ 
                  duration: 1.5, 
                  repeat: Infinity,
                  repeatType: "reverse"
                }}
                className={`${color} ${bgColor} p-2 rounded-full border-2 ${borderColor}`}
              >
                <Icon icon={icon} className="w-6 h-6" />
              </motion.div>
              
              <div className="flex-1">
                <h3 className={`font-bold ${color} text-sm`}>
                  {title}
                </h3>
                <p className="text-xs text-gray-600">
                  {message}
                </p>
              </div>
              
              <motion.div
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ duration: 1, repeat: Infinity }}
              >
                <Icon icon="mdi:star-four-points" className={`w-4 h-4 ${color}`} />
              </motion.div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default AchievementNotification;
