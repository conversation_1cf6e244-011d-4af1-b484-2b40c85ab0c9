import React, { useEffect } from 'react';

const WordValidationMsg = ({
  wordCount,
  minimumWords,
  maximumWords,
  setIsValidWord,
}) => {
  if (wordCount >= minimumWords && wordCount <= (maximumWords || Infinity)) {
    setIsValidWord(true);
  } else {
    setIsValidWord(false);
  }
  return (
    <p
      className={`text-sm ${
        wordCount < minimumWords || wordCount > (maximumWords || Infinity)
          ? 'text-red-500'
          : 'text-green-600'
      }`}
    >
      {wordCount < minimumWords &&
        `Minimum ${minimumWords} words required to submit.`}
      {wordCount > (maximumWords || Infinity) &&
        `Maximum ${maximumWords || Infinity} words allowed.`}
      {wordCount >= minimumWords &&
        wordCount <= (maximumWords || Infinity) &&
        'Word count is within limits.'}
    </p>
  );
};

export default WordValidationMsg;
