'use client';

import { useEffect, useCallback, useState, useRef } from 'react';
import { format } from 'date-fns';
import { toast } from 'sonner';
import { useRouter, useSearchParams } from 'next/navigation';
import { useDispatch, useSelector } from 'react-redux';
import * as yup from 'yup';

import api from '@/lib/api';
import DiaryIconsSidebar from '../../_component/DiaryIconsSidebar';
import SelectSkinModal from '../../_component/SelectSkinModal';
import StageSelector from '../../_component/StageSelector';
import DiaryForm from '../../_component/DiaryForm';

import {
  setPreviewMode,
  updateCanvasItem,
  changeBackground,
  addImageToCanvas,
  resetCanvas,
  selectCanvasItems,
  selectCanvasBackground,
  setSelectedId,
} from '@/store/features/canvasSlice';

import {
  setSubject,
  setMessage,
  setSelectedSkin,
  setIsSaving,
  setIsLoading,
  setTodayEntry,
  setIsSkinModalOpen,
  setLayoutBackground,
  selectDiarySubject,
  selectDiaryMessage,
  selectSelectedSkin,
  selectIsSaving,
  selectIsLoading,
  selectTodayEntry,
  selectIsSkinModalOpen,
  selectLayoutBackground,
  selectIsDecorating,
  selectDecorationItems,
  setDecorationItems,
  setIsSubmittingDecoration,
  setDecorationSubmissionError,
  setDecorationSubmissionSuccess,
  resetDecorationSubmissionState,
  setIsDecorating,
  setSelectedDecorationId,
} from '@/store/features/diarySlice';
import SkinPreview from '@/components/skin/SkinPreview';
import MessageModal from '../../_component/modalContents/MessageModal';
import { Icon } from '@iconify/react';
import Button, { ButtonIcon } from '@/components/Button';
import DiarySkin from '../../_component/DiarySkin';
import CalendarFilter from '../../_component/modalContents/share/CalendarFilter';
import useDataFetch from '@/hooks/useDataFetch';

// Yup validation schema for diary form
const diaryValidationSchema = yup.object().shape({
  subject: yup
    .string()
    .trim()
    .required('Subject is required')
    .min(1, 'Subject cannot be empty'),
  message: yup
    .string()
    .trim()
    .required('Content is required')
    .min(1, 'Content cannot be empty'),
});

export default function WriteDiaryItem() {
  const router = useRouter();
  const dispatch = useDispatch();
  const searchParams = useSearchParams();
  const isInitialMount = useRef(true);
  const { user } = useSelector((state) => state.auth);

  // Get entryId from URL parameters
  const entryId = searchParams.get('entryId');

  // Add state for selected stage template ID
  const [selectedStageTemplateId, setSelectedStageTemplateId] = useState(null);
  const isDecorating = useSelector(selectIsDecorating);
  const decorationItems = useSelector(selectDecorationItems);
  const [selectedStage, setSelectedStage] = useState(null);
  const [wordCount, setWordCount] = useState(0);
  const [nextStage, setNextStage] = useState(null);
  const { date } = useSelector((state) => state.diary);
  const canvasItems = useSelector(selectCanvasItems);

  // Diary state
  const subject = useSelector(selectDiarySubject);
  const message = useSelector(selectDiaryMessage);
  const selectedSkin = useSelector(selectSelectedSkin);
  const isSaving = useSelector(selectIsSaving);
  const isLoading = useSelector(selectIsLoading);
  const diaryEntry = useSelector(selectTodayEntry);
  const isSkinModalOpen = useSelector(selectIsSkinModalOpen);
  const layoutBackground = useSelector(selectLayoutBackground);
  const [isOpen, setIsOpen] = useState(false);
  const [zoomLevel, setZoomLevel] = useState(1);
  const [isUpdating, setUpdating] = useState(false);
  const [hasTutorGreeting, setHasTutorGreeting] = useState(true);
  const [validationErrors, setValidationErrors] = useState({});
  const [isEdit, setIsEdit] = useState(false);
  const [showSideIcons, setShowSideIcons] = useState(false);
  const [showCover, setShowCover] = useState(true);
  const [showCalendar, setShowCalendar] = useState(false);

  // Separate hook for diary settings (only fetch when needed)
  const { data: diarySettingsData, refetch: fetchDiarySettings } = useDataFetch(
    {
      queryKey: ['diary-settings'],
      endPoint: '/diary/settings',
      method: 'GET',
      params: {},
      enabled: false, // Only fetch when manually triggered
    }
  );

  function formatDateToYMD(inputDate) {
    const date = new Date(inputDate);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0'); // Months are 0-based
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  // get data with useDataFetch hook
  const {
    data: diaryEntryData,
    isLoading: isDiaryEntryLoading,
    refetch: fetchDiaryEntry,
    error: diaryEntryError,
  } = useDataFetch({
    queryKey: ['diary-entry', entryId, date, user?.defaultDiarySkinId],
    endPoint: entryId ? `/diary/entries/${entryId}` : '/diary/entries',
    method: entryId ? 'GET' : 'POST',
    params: entryId
      ? {}
      : {
          entryDate: formatDateToYMD(new Date(date)),
          title: '',
          content: '',
          skinId: selectedSkin?.id,
          backgroundColor: '#f5f5f5',
          isPrivate: false,
          settingsTemplateId: selectedStage?.id || selectedStageTemplateId,
        },
    apiConfig: entryId ? {} : { showSuccessToast: false }, // Disable success toast for POST requests
    // enabled: isLoading, // Only fetch when loading is true
  });

  useEffect(() => {
    if (
      !isDiaryEntryLoading &&
      !diaryEntryData?.diary?.defaultSkinId &&
      !user?.defaultDiarySkinId
    ) {
      dispatch(setIsSkinModalOpen(true));
    }
  }, [dispatch, user, diaryEntryData, isDiaryEntryLoading]);

  // useEffect(() => {
  //   if (user?.defaultDiarySkinId) {
  //     fetchDiaryEntry();
  //   }
  // }, [user, fetchDiaryEntry]);

  // Process diary entry data when it arrives
  useEffect(() => {
    if (diaryEntryData) {
      if (diaryEntryData && !entryId) {
        router.push(`/diary/my/item?entryId=${diaryEntryData.id}`);
        return;
      }
      dispatch(setTodayEntry(diaryEntryData));
      dispatch(setSubject(diaryEntryData.title || ''));
      dispatch(setMessage(diaryEntryData.content || ''));

      // Load decorations if they exist
      if (diaryEntryData.decoration) {
        try {
          const decorations = JSON.parse(diaryEntryData.decoration);
          dispatch(setDecorationItems(decorations));
          console.log('Loaded decorations from API:', decorations);
        } catch (error) {
          console.error('Error parsing decoration data:', error);
          dispatch(setDecorationItems([]));
        }
      } else {
        dispatch(setDecorationItems([]));
      }

      if (diaryEntryData.settings?.settingsTemplateId) {
        setSelectedStageTemplateId(diaryEntryData.settings.settingsTemplateId);

        console.log(
          'Setting stage from diary entry:',
          diaryEntryData.settings.settingsTemplateId
        );

        // Fetch diary settings to determine next stage
        fetchDiarySettings();
      }

      if (diaryEntryData.skin) {
        dispatch(setSelectedSkin(diaryEntryData.skin));
      }

      dispatch(setIsLoading(false));
      isInitialMount.current = false;
    } else {
      setSelectedStageTemplateId(diarySettingsData?.items?.[0].id);
      setSelectedStage(diarySettingsData?.items?.[0]);
    }
  }, [diaryEntryData, dispatch, fetchDiarySettings]);

  // Handle diary settings data for next stage calculation
  useEffect(() => {
    if (
      diarySettingsData?.items &&
      diaryEntryData?.settings?.settingsTemplateId
    ) {
      const currentStage = diarySettingsData.items.find(
        (item) => item.id === diaryEntryData.settings.settingsTemplateId
      );

      if (currentStage) {
        // Set the current stage
        setSelectedStage(currentStage);

        if (currentStage.level !== undefined) {
          const nextStageItem = diarySettingsData.items.find(
            (item) => item.level === currentStage.level + 1
          );
          setNextStage(nextStageItem || null);
        } else {
          setNextStage(null);
        }
      } else {
        setNextStage(null);
      }
    }
  }, [diarySettingsData, diaryEntryData]);

  // Handle cases where no skin is selected
  // useEffect(() => {
  //   if (!isDiaryEntryLoading && !diaryEntryData && !selectedSkin) {
  //     router.push('/select-skin');
  //   }
  // }, [isDiaryEntryLoading, diaryEntryData, selectedSkin, router]);

  // Handle errors
  useEffect(() => {
    if (diaryEntryError) {
      console.error('Error fetching diary entry:', diaryEntryError);
      dispatch(setIsLoading(false));
      isInitialMount.current = false;
    }
  }, [diaryEntryError, dispatch]);

  // Update loading state based on hook loading state
  useEffect(() => {
    if (isDiaryEntryLoading && isLoading) {
      // Keep loading state as is when hook is loading
    } else if (!isDiaryEntryLoading) {
      // Only set loading to false when hook is done loading
      dispatch(setIsLoading(false));
    }
  }, [isDiaryEntryLoading, isLoading, dispatch]);

  //  todayEntry?.entryDate
  //     ? format(new Date(todayEntry.entryDate), 'dd MMM yyyy')
  //     :
  // Selectors
  const currentEntryDate =
    (diaryEntry?.entryDate &&
      format(new Date(diaryEntry?.entryDate), 'dd MMM yyyy')) ||
    date ||
    format(new Date(), 'dd MMM yyyy');

  // Function to open diary from cover page
  const openDiary = () => {
    setShowCover(false);
    setIsOpen(true);
  };

  useEffect(() => {
    if (entryId) {
      setIsOpen(true);
    }
  }, [entryId]);

  // Reset decoration state when component unmounts or when navigating away
  useEffect(() => {
    return () => {
      // Cleanup decoration state when leaving WriteDiary component
      dispatch(setIsDecorating(false));
      dispatch(setSelectedDecorationId(null));
      console.log('WriteDiary item component unmounting - resetting decoration state');
    };
  }, [dispatch]);

  // Handle responsive sidebar behavior
  useEffect(() => {
    const handleResize = () => {
      const isDesktop = window.innerWidth >= 1280; // xl breakpoint
      if (isDesktop) {
        setShowSideIcons(true); // Always show sidebar on desktop
      } else {
        setShowSideIcons(false); // Hide sidebar on mobile/tablet by default
      }
    };

    // Initial check
    handleResize();

    // Add event listener
    window.addEventListener('resize', handleResize);
    // Cleanup
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Clear validation errors when user starts typing
  useEffect(() => {
    if (validationErrors.subject && subject.trim()) {
      setValidationErrors((prev) => ({ ...prev, subject: undefined }));
    }
  }, [subject, validationErrors.subject]);

  useEffect(() => {
    if (validationErrors.message && message.trim()) {
      setValidationErrors((prev) => ({ ...prev, message: undefined }));
    }
  }, [message, validationErrors.message]);

  // Function to count words in a string
  const countWords = (text) => {
    if (!text || text.trim() === '') return 0;
    return text.trim().split(/\s+/).length;
  };

  const handleApplySkin = (newSkin) => {
    if (!newSkin.templateContent) {
      toast.error('This skin has no valid template content');
      return;
    }
    dispatch(setSelectedSkin(newSkin));
    dispatch(setIsSkinModalOpen(false));
    toast.success(`Skin "${newSkin.name}" applied successfully`);
  };

  // Detect browser zoom level
  useEffect(() => {
    const detectZoom = () => {
      const zoom =
        Math.round((window.outerWidth / window.innerWidth) * 100) / 100;
      setZoomLevel(zoom);
    };

    detectZoom();
    window.addEventListener('resize', detectZoom);

    return () => window.removeEventListener('resize', detectZoom);
  }, []);

  // Handle stage change
  const handleStageChange = useCallback(
    async (stage) => {
      // console.log(diaryEntry);
      if (!diaryEntry?.id) {
        console.log('diary entry not found');
        return;
      }

      try {
        const response = await api.patch(
          `/diary/entries/${diaryEntry?.id}/settings`,
          {
            settingsTemplateId: stage.id,
          },
          { showSuccessToast: false }
        );

        // console.log('Selected stage:', stage);
        setSelectedStageTemplateId(stage.id);
        setSelectedStage(stage);

        // Update word count when stage changes
        setWordCount(countWords(message));
      } catch (error) {
        console.error('Error changing stage:', error);
      }
    },
    [message, diaryEntry]
  );

  // Background color listener
  useEffect(() => {
    const storedColor = localStorage.getItem('diaryLayoutBackground');
    if (storedColor) {
      dispatch(setLayoutBackground(storedColor));
    }

    const handleBackgroundChange = (event) => {
      dispatch(setLayoutBackground(event.detail));
    };

    window.addEventListener('layoutBackgroundChanged', handleBackgroundChange);
    return () =>
      window.removeEventListener(
        'layoutBackgroundChanged',
        handleBackgroundChange
      );
  }, [dispatch]);

  // Enable preview mode on mount
  useEffect(() => {
    dispatch(setPreviewMode(true));
  }, [dispatch]);

  // Set loading to true whenever entryId changes (for both edit mode and create mode)
  useEffect(() => {
    dispatch(setIsLoading(true));
  }, [entryId, dispatch]);

  // Update word count whenever message changes
  useEffect(() => {
    setWordCount(countWords(message));
  }, [message]);

  // Reactive effect to apply skin template when selectedSkin changes
  useEffect(() => {
    if (!selectedSkin || !selectedSkin.templateContent) return;

    // For subsequent skin changes by the user, preserve current content
    const contentToPreserve = {
      subject: isInitialMount.current ? diaryEntry?.title || '' : subject,
      message: isInitialMount.current ? diaryEntry?.content || '' : message,
    };

    try {
      dispatch(resetCanvas());
      const templateData = JSON.parse(selectedSkin.templateContent);

      if (templateData.background) {
        dispatch(changeBackground(templateData.background));
      }

      if (templateData.items?.length) {
        templateData.items.forEach((item) => {
          if (item.type === 'text') {
            let content = item.content;
            let itemId = item.id;

            if (item.id === 'subject' || item.id.startsWith('subject')) {
              itemId = 'subject';
              content = contentToPreserve.subject || item.content;
            } else if (item.id === 'body' || item.id.startsWith('body')) {
              itemId = 'body';
              content = contentToPreserve.message || item.content;
            } else if (item.id === 'date' || item.id.startsWith('date')) {
              itemId = 'date';
              content = format(new Date(), item.dateFormat || 'dd MMM yyyy');
            }

            dispatch({
              type: 'canvas/addTextItem',
              payload: {
                ...item,
                id: itemId,
                content,
                styles: {
                  ...item.styles,
                  width: item.styles?.width || 300,
                  height: item.styles?.height || 40,
                  x: item.styles?.x || 50,
                  y: item.styles?.y || 20,
                },
              },
            });
          } else if (item.type === 'image') {
            dispatch(
              addImageToCanvas({
                id: `${item.id}-${Date.now()}`,
                imageSrc: item.image,
                styles: item.styles,
                zIndex: item.zIndex || 1,
              })
            );
          }
        });
      }

      dispatch(setSelectedId(null));
      dispatch(setPreviewMode(false));
      setTimeout(() => dispatch(setPreviewMode(true)), 50);
    } catch (error) {
      console.error('Error applying skin template:', error);
      // toast.error('Failed to apply skin template');
    }
  }, [selectedSkin, dispatch, subject, message, diaryEntry]); // Only re-run when skin changes

  // Listen for the openGreetingModal event
  useEffect(() => {
    if (!isDiaryEntryLoading) {
      setHasTutorGreeting(diaryEntry?.hasGreeting);
    }
  }, [diaryEntry]);

  // Listen for the greetingSent event
  useEffect(() => {
    const handleGreetingSent = async (event) => {
      if (event.detail?.success) {
        // Refresh the diary entry data
        dispatch(setIsLoading(true));
      }
    };

    window.addEventListener('greetingSent', handleGreetingSent);
    return () => {
      window.removeEventListener('greetingSent', handleGreetingSent);
    };
  }, [dispatch]);

  // Initialize selected stage from API data when selectedStageTemplateId changes
  // useEffect(() => {
  //   const fetchStageData = async () => {
  //     if (!selectedStageTemplateId) return;

  //     try {
  //       const response = await api.get('/diary/settings');
  //       if (response.success && response.data?.items) {
  //         const stage = response.data.items.find(
  //           (item) => item.id === selectedStageTemplateId
  //         );
  //         if (stage) {
  //           setSelectedStage(stage);

  //           // Set the next stage during initialization using level field
  //           const currentStage = response.data.items.find(
  //             (item) => item.id === selectedStageTemplateId
  //           );
  //           if (currentStage && currentStage.level !== undefined) {
  //             const nextStageItem = response.data.items.find(
  //               (item) => item.level === currentStage.level + 1
  //             );
  //             setNextStage(nextStageItem || null);
  //           } else {
  //             // If current stage not found or no level field, set nextStage to null
  //             setNextStage(null);
  //           }
  //         }
  //       }
  //     } catch (error) {
  //       console.error('Error fetching stage data:', error);
  //     }
  //   };

  //   fetchStageData();
  // }, [selectedStageTemplateId]);

  // Update canvas when form values change
  useEffect(() => {
    if (isLoading || isInitialMount.current) return;

    const subjectItem = canvasItems.find((item) => item.id === 'subject');
    if (subjectItem && subjectItem.content !== subject) {
      dispatch(
        updateCanvasItem({ id: 'subject', updates: { content: subject } })
      );
    }

    const bodyItem = canvasItems.find((item) => item.id === 'body');
    if (bodyItem && bodyItem.content !== message) {
      dispatch(updateCanvasItem({ id: 'body', updates: { content: message } }));
    }
  }, [subject, message, isLoading, dispatch, canvasItems]);

  // Remove the old fetchTodayEntry and useEffect that called it
  // The hook now handles the fetching automatically

  useEffect(() => {
    if (diaryEntry?.content?.length > 0) {
      setIsEdit(false);
    } else if (diaryEntry?.status === 'new') {
      setIsEdit(true);
    }
  }, [diaryEntry]);

  // Handle save
  const handleSave = useCallback(async () => {
    // Clear previous validation errors
    setValidationErrors({});

    // Validate form data using Yup schema
    try {
      await diaryValidationSchema.validate(
        { subject, message },
        { abortEarly: false }
      );
    } catch (validationError) {
      // Handle validation errors - set them in state instead of showing toast
      const errors = {};
      if (validationError.inner && validationError.inner.length > 0) {
        validationError.inner.forEach((error) => {
          errors[error.path] = error.message;
        });
      } else if (validationError.path) {
        errors[validationError.path] = validationError.message;
      }
      setValidationErrors(errors);
      return;
    }

    dispatch(setIsSaving(true));
    try {
      const payload = {
        title: subject,
        content: message,
        skinId: selectedSkin?.id || null,
        backgroundColor: layoutBackground,
        isPrivate: false,
        settingsTemplateId: selectedStage?.id || selectedStageTemplateId, // Include the selected stage ID
        // entryId: todayEntry?.id || null,
      };

      let apiCall;
      apiCall = api.post(`/diary/entries/${diaryEntry?.id}/submit`, payload);
      // Check if entry exists
      // if (todayEntry?.id) {
      //   // Check if status is "new" - if so, use the submit endpoint
      // } else {
      //   // If no entry exists yet, create a new one
      //   apiCall = api.post('/diary/entries', {
      //     ...payload,
      //     entryDate: format(new Date(), 'yyyy-MM-dd'),
      //   });
      // }

      const response = await apiCall;

      if (response.success) {
        dispatch(setTodayEntry(response.data));
        setIsEdit(false);
        // Show appropriate success message based on the action performed
        if (diaryEntry?.status === 'new' && diaryEntry?.id) {
          // toast.success('Diary submitted successfully!');
        } else {
          // toast.success('Diary saved successfully!');
        }
      }
    } catch (error) {
      console.error('Error saving diary entry:', error);
      // toast.error('Failed to save diary entry. Please try again.');
    } finally {
      dispatch(setIsSaving(false));
    }
  }, [
    subject,
    message,
    selectedSkin,
    layoutBackground,
    diaryEntry,
    dispatch,
    selectedStageTemplateId,
  ]);

  // Handle save as draft
  const handleSaveAsDraft = useCallback(async () => {
    // Clear previous validation errors
    setValidationErrors({});

    if (!isEdit || !selectedSkin?.id) return;

    // Basic validation - only check if fields are not empty
    // if (!subject.trim() || !message.trim()) {
    //   setValidationErrors({
    //     subject: !subject.trim() ? 'Subject is required' : '',
    //     message: !message.trim() ? 'Content is required' : '',
    //   });
    //   return;
    // }

    setUpdating(true);

    try {
      const payload = {
        title: subject,
        content: message,
        skinId: selectedSkin?.id || null,
        backgroundColor: layoutBackground,
        isPrivate: false,
        settingsTemplateId: selectedStageTemplateId,
      };

      let apiCall;

      // Always use patch for draft saves if entry exists
      if (diaryEntry?.id) {
        apiCall = api.patch(`/diary/entries/${diaryEntry.id}`, payload, {
          showSuccessToast: false,
        });
      } else {
        // If no entry exists yet, create a new one as draft
        apiCall = api.post(
          '/diary/entries',
          {
            ...payload,
            entryDate: format(new Date(currentEntryDate), 'yyyy-MM-dd'),
          },
          { showSuccessToast: false }
        );
      }

      const response = await apiCall;

      // if (response.success) {
      //   dispatch(setTodayEntry(response.data));
      //   toast.success('Draft saved successfully!');
      // }
    } catch (error) {
      console.error('Error saving draft:', error);
    } finally {
      setUpdating(false);
    }
  }, [
    subject,
    message,
    selectedSkin,
    layoutBackground,
    diaryEntry,
    dispatch,
    selectedStageTemplateId,
  ]);

  // Handle decoration submission
  const handleSubmitDecoration = useCallback(async () => {
    if (!diaryEntry?.id) {
      console.error('No diary entry ID available for decoration submission');
      return;
    }

    dispatch(setIsSubmittingDecoration(true));
    dispatch(resetDecorationSubmissionState());

    try {
      // Format decoration data as JSON string
      const decorationData = JSON.stringify(decorationItems);

      const response = await api.patch(
        `/diary/entries/${diaryEntry.id}/decoration`,
        { decoration: decorationData }
      );

      if (response.success) {
        dispatch(setDecorationSubmissionSuccess(true));
        // toast.success('Decoration submitted successfully!');
        console.log('Decoration submitted successfully:', response.data);
      } else {
        throw new Error(response.message || 'Failed to submit decoration');
      }
    } catch (error) {
      console.error('Error submitting decoration:', error);
      dispatch(setDecorationSubmissionError(error.message || 'Failed to submit decoration'));
      // toast.error('Failed to submit decoration. Please try again.');
    } finally {
      dispatch(setIsSubmittingDecoration(false));
    }
  }, [diaryEntry?.id, decorationItems, dispatch]);

  // Debounced auto-save as draft when user types
  useEffect(() => {
    // if (
    //   todayEntry?.status !== 'submit'
    // )
    //   return;
    // if (!subject.trim() && !message.trim()) return;

    const handler = setTimeout(() => {
      handleSaveAsDraft();
    }, 800); // 2 seconds debounce

    return () => {
      clearTimeout(handler);
    };
  }, [
    subject,
    message,
    selectedSkin,
    layoutBackground,
    selectedStageTemplateId,
  ]);

  // Simple cover page component
  const CoverPage = () => (
    <div className="flex justify-center min-h-[calc(100vh-200px)] transition-all duration-500 ease-in-out">
      <div className="relative w-full max-w-[647px] h-[658px] bg-yellow-100 rounded-lg shadow-lg border border-gray-300 transition-all duration-300">
        <div className="absolute left-0 top-0 bottom-0 w-[60px] bg-[#1E3A8A] rounded-l-lg" />
        <div
          className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 bg-white/30 backdrop-blur-lg rounded-2xl shadow-xl px-12 py-6 max-sm:ml-6 text-center min-w-[200px] border border-white/40 ring-1 ring-black/5 cursor-pointer transition-all duration-300 hover:bg-white/40 hover:scale-105 hover:shadow-2xl"
          onClick={openDiary}
        >
          <h1 className="text-2xl max-sm:text-xl font-bold text-[#1E3A8A] mb-2">
            Write Today's Diary
          </h1>
          <div className="mt-3 text-xs text-gray-600">
            {format(new Date(), 'EEEE, MMMM do, yyyy')}
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <>
      <div className="flex justify-center items-start min-h-[calc(100vh-200px)] p-6">
        {/* className={`relative max-h-[658px] ${
            isOpen ? 'w-full max-w-[1280px]' : 'w-full max-w-[648px]'
          } transition-all duration-300`} */}

        <div
          className={`relative max-h-[658px] w-full max-w-[1280px] transition-all duration-300`}
        >
          {/* {isOpen ? ( */}
          <>
            <div
              className={`absolute left-1/2 -translate-x-1/2 -top-2 max-sm:max-w-64 bg-gradient-to-b from-yellow-300 via-yellow-400 to-yellow-500 px-4 pr-8 py-1 rounded-full`}
            >
              <DiaryIconsSidebar
                isOpen={showSideIcons}
                onClose={() => setShowSideIcons(false)}
                isEmojiSelectorOpen={isDecorating}
                // showSidbeIcons={showSideIcons}
              />
            </div>
            <div className="min-h-6 sm:hidden"></div>

            <div className="absolute right-0 -top-14 sm:top-3">
              <Button
                icon="material-symbols:calendar-month-outline"
                buttonText="Find Diary"
                onClick={() => setShowCalendar(!showCalendar)}
              />

              {showCalendar && (
                <div
                  className={
                    showCalendar
                      ? 'block absolute max-w-xl min-w-80 max-h-[400px] max-sm:max-h-[300px] overflow-y-auto bg-white border rounded-lg shadow-lg z-20 mt-8 top-0 right-0'
                      : 'hidden'
                  }
                >
                  <CalendarFilter
                    onDateSelect={() => {
                      // The CalendarFilter component will update the Redux store
                      // This callback is for any additional actions needed
                      if (fetchDiaryEntry) {
                        fetchDiaryEntry();
                      }
                    }}
                    onClose={() => setShowCalendar(false)}
                  />
                </div>
              )}
            </div>

            <div
              className={`container border border-gray-300 rounded-lg ${
                isDecorating ? 'mt-[350px]' : 'mt-10 sm:mt-20'
              }  shadow-lg ${
                isOpen ? '' : 'min-h-[500px]'
              } bg-pink-100 p-2 mx-auto`}
            >
              {isLoading ? (
                <div className="flex items-center justify-center h-[500px]">
                  <div className="text-center">
                    <div className="inline-block animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-yellow-400 mb-4"></div>
                    <h2 className="text-xl font-semibold">
                      Loading your diary...
                    </h2>
                  </div>
                </div>
              ) : (
                <>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2 items-center relative">
                    <DiarySkin
                      today={currentEntryDate}
                      todayEntry={diaryEntry}
                      subject={subject}
                      message={message}
                      selectedSkin={selectedSkin}
                      handleStageChange={handleStageChange}
                      selectedStageTemplateId={selectedStageTemplateId}
                      fetchTodayEntry={fetchDiaryEntry}
                      onSubmitDecoration={handleSubmitDecoration}
                      isDecorating={isDecorating}
                    />

                    <div className="bg-white h-full p-2 overflow-hidden shadow-xl">
                      <DiaryForm
                        today={currentEntryDate}
                        todayEntry={diaryEntry}
                        subject={subject}
                        message={message}
                        wordCount={wordCount}
                        selectedStage={selectedStage}
                        validationErrors={validationErrors}
                        fetchTodayEntry={fetchDiaryEntry}
                        nextStage={nextStage}
                        handleStageChange={handleStageChange}
                        isEditable={isEdit}
                        setIsEdit={setIsEdit}
                        isEdit={isEdit}
                        handleSave={handleSave}
                        isSaving={isSaving}
                        setIsMessageModalOpen={setHasTutorGreeting}
                      />
                    </div>
                  </div>
                </>
              )}

              {/* <div className="flex justify-between items-center mb-4 absolute -top-16 right-2 z-10">
                  <ButtonIcon
                    icon={'iconamoon:menu-kebab-vertical-bold'}
                    innerBtnCls="h-10 w-10"
                    btnIconCls="h-5 w-5"
                    aria-label={'Skin'}
                    onClick={() => setShowSideIcons(!showSideIcons)}
                  />
                </div> */}

              <SelectSkinModal
                isOpen={isSkinModalOpen}
                onClose={() => dispatch(setIsSkinModalOpen(false))}
                onApply={handleApplySkin}
                currentSkinId={selectedSkin?.id}
              />
              <MessageModal
                isOpen={!hasTutorGreeting}
                onClose={() => setHasTutorGreeting(true)}
              />
            </div>
          </>
          {/* ) : (
            <CoverPage />
          )} */}
        </div>
      </div>
    </>
  );
}
