// components/DiaryPage.js
'use client';
import DiaryWithDecorations from '@/components/diary/DiaryWithDecorations';
import ActionDrawer from './ActionDrawer';
import Image from 'next/image';
import React, { useState, useRef, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { selectIsDecorating } from '@/store/features/diarySlice';
import FeedbackViewModal from './FeedbackViewModal';

const DiaryPage = ({ entry, isDrawerOpen, onDrawerToggle }) => {
  const [showFeedbackModal, setShowFeedbackModal] = useState(false);
  const drawerRef = useRef(null);
  const toggleButtonRef = useRef(null);
  const isDecorating = useSelector(selectIsDecorating);

  const formatDate = (dateString) =>
    new Date(dateString).toLocaleDateString(undefined, {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });

  // Parse decoration data from entry
  const parseDecorationData = (decorationString) => {
    // Handle null, undefined, or empty string cases
    if (decorationString === null || decorationString === undefined || decorationString === '') {
      return null;
    }
    try {
      const parsed = JSON.parse(decorationString);
      return Array.isArray(parsed) ? parsed : null;
    } catch (error) {
      console.error('Error parsing decoration data:', error);
      return null;
    }
  };

  const decorationData = parseDecorationData(entry?.decoration);

  // Handle click outside to close drawer
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        isDrawerOpen &&
        drawerRef.current &&
        toggleButtonRef.current &&
        !drawerRef.current.contains(event.target) &&
        !toggleButtonRef.current.contains(event.target)
      ) {
        onDrawerToggle();
      }
    };

    if (isDrawerOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isDrawerOpen, onDrawerToggle]);

  if (!entry) return null;

  return (
    <div className="h-full flex flex-col p-2 overflow-hidden">
      <div className="flex justify-between items-center bg-white rounded-t-lg px-2 py-1">
        <div className="flex items-center gap-2">
          <div
            className="px-4 py-1 text-white rounded-md"
            style={{
              background: 'linear-gradient(180deg, #ECB306 0%, #AE6E33 100%)',
            }}
          >
            <p className="text-xs">Stage: {entry?.settings?.level}</p>
          </div>
          <p>:</p>
          <div className="px-4 py-1 text-[#864D0D] font-medium bg-[#FFF189] rounded-md border border-dashed border-[#ECB306]">
            <p className="text-xs">Words: {entry?.settings?.wordLimit}</p>
          </div>
        </div>
        {entry?.score && (
          <div className=" font-medium  border border-dashed border-[#ECB306] bg-[#FFF189] rounded-md">
            <p className="text-sm text-[#864D0D] py-1 px-3">
              score: {entry?.score}
            </p>
          </div>
        )}
      </div>
      <div>
        <DiaryWithDecorations
          skin={entry.skin?.templateContent}
          contentData={{
            subject: entry.title,
            body: entry.content,
            date: entry.entryDate,
          }}
          decorationData={decorationData}
        />
      </div>
      <div className="bg-white shadow-xl rounded-b-lg p-2 h-full relative">
        <div>
          <h6 className="text-[#864D0D] font-semibold text-base mb-2 text-center">
            Tutor Correction
          </h6>
        </div>
        <div className="border-b-2 py-1 flex justify-between items-center">
          <h6 className="text-lg font-medium">{entry.title}</h6>
          <p className="text-sm text-gray-500">{formatDate(entry.entryDate)}</p>
        </div>
        <div className="h-[200px] overflow-auto custom-scrollbar whitespace-pre-wrap">
          {entry?.correction?.correctionText ? (
            <p
              className="text-justify"
              dangerouslySetInnerHTML={{
                __html: entry.correction.correctionText,
              }}
            />
          ) : (
            <p className="text-center p-5">
              {entry?.status === 'reviewed'
                ? 'Reviewed'
                : 'No correction yet'}
            </p>
          )}
        </div>
        {entry?.status === 'reviewed' && (
          <div className="text-center">
            <h1 className="text-lg text-[#14AE5C] font-medium">Reviewed</h1>
          </div>
        )}
        <div className="absolute bottom-1 left-2 z-10">
          <button ref={toggleButtonRef} onClick={onDrawerToggle}>
            <Image
              src="/assets/images/all-img/main-button.svg"
              alt="Tutor"
              width={50}
              height={50}
            />
          </button>
        </div>
        {/* {entry?.feedbacks?.length > 0 && ( */}
        <div className="absolute right-0 bottom-0">
          <button
            onClick={() => setShowFeedbackModal(true)}
            aria-label="View feedback"
          >
            <Image
              src="/assets/images/all-img/feedback-bg.png"
              alt="Feedback"
              width={50}
              height={50}
            />
          </button>
        </div>
        {/* )} */}
        <div ref={drawerRef}>
          <ActionDrawer
            isOpen={isDrawerOpen}
            entry={entry}
            onFeedback={() => setShowFeedbackModal(true)}
            onClose={onDrawerToggle}
          />
        </div>
        {showFeedbackModal && (
          <FeedbackViewModal
            isOpen={showFeedbackModal}
            onClose={() => setShowFeedbackModal(false)}
            feedbacks={entry?.feedbacks || []}
          />
        )}
      </div>
    </div>
  );
};

export default DiaryPage;
