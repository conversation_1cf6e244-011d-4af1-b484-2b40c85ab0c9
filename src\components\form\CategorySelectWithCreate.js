'use client';

import React, { useState } from 'react';
import { useField } from 'formik';
import { Icon } from '@iconify/react';
import { toast } from 'sonner';

const CategorySelectWithCreate = ({ 
  label, 
  options, 
  required, 
  placeholder = 'Select a category',
  onCreateCategory,
  isCreating = false,
  ...props 
}) => {
  const [field, meta, helpers] = useField(props);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [newCategoryName, setNewCategoryName] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleCreateCategory = async () => {
    if (!newCategoryName.trim()) {
      toast.error('Please enter a category name');
      return;
    }

    setIsSubmitting(true);
    try {
      const newCategory = await onCreateCategory(newCategoryName.trim());
      
      if (newCategory) {
        // Set the newly created category as selected
        helpers.setValue(newCategory.id || newCategory.value);
        
        // Reset form
        setNewCategoryName('');
        setShowCreateForm(false);
        
        toast.success('Category created successfully');
      }
    } catch (error) {
      console.error('Error creating category:', error);
      toast.error('Failed to create category');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancelCreate = () => {
    setNewCategoryName('');
    setShowCreateForm(false);
  };

  return (
    <div className="">
      <label className="block text-base font-medium mb-1 text-gray-700">
        {label}{required && <span className="text-red-500">*</span>}
      </label>
      
      {!showCreateForm ? (
        <div className="relative">
          <select
            {...field}
            className={`w-full px-3 py-2.5 pr-10 border ${
              meta.touched && meta.error ? 'border-red-500' : 'border-gray-300'
            } rounded-lg focus:outline-none focus:ring-1 focus:ring-yellow-500`}
          >
            <option value="" className="text-gray-500">{placeholder}</option>
            {options?.map((option, idx) => (
              <option key={idx} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
          
          {/* Add New Category Button */}
          <button
            type="button"
            onClick={() => setShowCreateForm(true)}
            className="absolute right-4 top-1/2 transform -translate-y-1/2 p-1 text-gray-500 hover:text-yellow-600 transition-colors"
            title="Add new category"
          >
            <Icon icon="mdi:plus-circle" className="w-5 h-5" />
          </button>
        </div>
      ) : (
        <div className="space-y-2">
          <div className="flex gap-2">
            <input
              type="text"
              value={newCategoryName}
              onChange={(e) => setNewCategoryName(e.target.value)}
              placeholder="Enter new category name"
              className="flex-1 px-3 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-yellow-500"
              onKeyPress={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                  handleCreateCategory();
                }
              }}
              disabled={isSubmitting}
            />
            <button
              type="button"
              onClick={handleCreateCategory}
              disabled={isSubmitting || !newCategoryName.trim()}
              className={`px-3 py-2.5 rounded-lg text-white font-medium transition-colors ${
                isSubmitting || !newCategoryName.trim()
                  ? 'bg-gray-400 cursor-not-allowed'
                  : 'bg-green-600 hover:bg-green-700'
              }`}
            >
              {isSubmitting ? (
                <Icon icon="mdi:loading" className="w-4 h-4 animate-spin" />
              ) : (
                <Icon icon="mdi:check" className="w-4 h-4" />
              )}
            </button>
            <button
              type="button"
              onClick={handleCancelCreate}
              disabled={isSubmitting}
              className="px-3 py-2.5 bg-gray-500 hover:bg-gray-600 text-white rounded-lg transition-colors disabled:opacity-50"
            >
              <Icon icon="mdi:close" className="w-4 h-4" />
            </button>
          </div>
          <p className="text-sm text-gray-600">
            Press Enter or click the check button to create the category
          </p>
        </div>
      )}
      
      {meta.touched && meta.error ? (
        <div className="text-red-500 text-xs mt-1">{meta.error}</div>
      ) : null}
    </div>
  );
};

export default CategorySelectWithCreate;
