'use client';

import React, { useEffect, useState, useRef } from 'react';
import { formatDate } from '@/utils/dateFormatter';
import CanvasPreview from '@/components/skin/CanvasPreview';

const NovelCanvas = ({ data }) => {
  const [zoomLevel, setZoomLevel] = useState(1);
  const canvasWrapperRef = useRef(null);

  // Detect browser zoom level
  useEffect(() => {
    const detectZoom = () => {
      const zoom = Math.round((window.outerWidth / window.innerWidth) * 100) / 100;
      setZoomLevel(zoom);
    };

    detectZoom();
    window.addEventListener('resize', detectZoom);

    return () => window.removeEventListener('resize', detectZoom);
  }, []);

  // Parse skin template content and prepare skin data for CanvasPreview
  const parsedSkin = React.useMemo(() => {
    if (!data?.skin?.templateContent) return null;

    try {
      const templateData = JSON.parse(data.skin.templateContent);
      return {
        canvasItems: templateData.items || [],
        canvasWidth: templateData.width || 800,
        canvasHeight: templateData.height || 600,
        canvasBackground: templateData.background || '#fff'
      };
    } catch (error) {
      console.error('Error parsing skin template:', error);
      return null;
    }
  }, [data?.skin?.templateContent]);

  // Prepare content data for CanvasPreview
  const contentData = {
    subject: data?.topic?.title || '',
    date: data?.entryDate ? formatDate(data.entryDate, 'ordinal') : '',
    body: data?.content || ''
  };

  return (
    <div className="">
      <div className="canvas-container">
        {parsedSkin ? (
          <div
            ref={canvasWrapperRef}
            className="canvas-wrapper"
            style={{
              width: '100%',
              height: '100%',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              transform: `scale(${zoomLevel > 1 ? (1 / zoomLevel) * 0.9 : 1})`,
              transformOrigin: 'center'
            }}
          >
            <CanvasPreview
              skin={parsedSkin}
              contentData={contentData}
            />
          </div>
        ) : (
          <div
            className="h-[400px] overflow-auto whitespace-pre-wrap"
            style={{ backgroundColor: data?.backgroundColor || '#ddd' }}
          >
            {data?.content}
          </div>
        )}
      </div>
    </div>
  );
};

export default NovelCanvas;