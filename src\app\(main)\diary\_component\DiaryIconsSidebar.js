'use client';
import React, { useState, useRef, useEffect } from 'react';
import Button, { ButtonIcon } from '@/components/Button';
import Modal from '@/components/Modal';
import Tooltip from '@/components/Tooltip';
import SelectSkinContent from './modalContents/SelectSkinContent';
import ShareModalContent from './modalContents/ShareModalContent';
import { useRouter } from 'next/navigation';
import { useDispatch } from 'react-redux';
import { setIsDecorating, setIsSkinModalOpen } from '@/store/features/diarySlice';
import EmojiSelectorModal from './EmojiSelectorModal';
import { setFriendActiveTab } from '@/store/features/commonSlice';

const DiaryIconsSidebar = ({
  todayEntry,
  className = '',
  showSkin = true,
  showShare = true,
  showDecoration = true,
  showMessage = true,
  showAddFriend = true,
  showSidbeIcons = true,
  setShowSideIcons,
  isEmojiSelectorOpen,
  setIsEmojiSelectorOpen,
}) => {
  const [activeModal, setActiveModal] = useState(null);
  const DecorationButtonRef = useRef(null);
  const [isDesktop, setIsDesktop] = useState(false);
  const router = useRouter();
  const dispatch = useDispatch();

  // Check for desktop/mobile screen size
  useEffect(() => {
    const checkScreenSize = () => {
      setIsDesktop(window.innerWidth >= 1280); // xl breakpoint
    };

    // Initial check
    checkScreenSize();

    // Add event listener
    window.addEventListener('resize', checkScreenSize);

    // Cleanup
    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  const allIcons = [
    {
      icon: 'arcticons:image-combiner',
      alt: 'Skin',
      modalTitle: 'Change Skin',
      modalWidth: '4xl',
      action: () => dispatch(setIsSkinModalOpen(true)),
      show: showSkin,
    },
    {
      icon: 'ic:baseline-share',
      alt: 'Share',
      modalTitle: 'Share Diary',
      modalWidth: 'xl',
      show: showShare,
    },
    {
      icon: 'mdi:brush',
      alt: 'Decoration',
      modalTitle: 'Customize Diary',
      show: showDecoration,
    },
    {
      icon: 'tabler:mail',
      alt: 'Diary Follow Request',
      action: () => (router.push(`/invite-friends`), dispatch(setFriendActiveTab('requests'))),
      show: showMessage,
    },
    {
      icon: 'mdi:account-plus',
      alt: 'Add Friend',
      action: () => router.push('/find-friends'),
      show: showAddFriend,
    },
  ];

  const icons = allIcons.filter((icon) => icon.show);

  const handleIconClick = (modalId, customAction) => {
    if (customAction) {
      customAction();
    } else if (modalId === 'Decoration') {
      // Toggle decoration mode and show emoji selector with owned items
      dispatch(setIsDecorating(true));
      setIsEmojiSelectorOpen(true);
    } else {
      setActiveModal(modalId);
    }
  };

  const closeModal = () => {
    setActiveModal(null);
  };

  const handleEmojiSelect = () => {
    // Keep decoration mode active when selecting items
    // dispatch(setIsDecorating(false))
  };

  const handleEmojiSelectorClose = () => {
    dispatch(setIsDecorating(false));
    setIsEmojiSelectorOpen(false);
  };

  const getModalContent = () => {
    switch (activeModal) {
      case 'Skin':
        return <SelectSkinContent />;
      case 'Location':
        return (
          <div>
            <p className="mb-4">Add a location to your diary entry.</p>
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Search for a location
              </label>
              <input
                type="text"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-yellow-500 focus:border-yellow-500"
                placeholder="Enter a location..."
              />
            </div>
            <div className="h-64 bg-gray-100 rounded-md mb-4 flex items-center justify-center">
              <p className="text-gray-500">Map will be displayed here</p>
            </div>
          </div>
        );
      case 'Share':
        return <ShareModalContent />;
      default:
        return <p>Select an action from the sidebar.</p>;
    }
  };

  return (
    <>
      {/* $
      {isDesktop
        ? 'block'
        : showSidbeIcons
        ? 'absolute right-4 top-1/2 transform -translate-y-1/2 translate-x-0 transition-all duration-300 ease-in-out z-10'
        : 'hidden'} */}
      <div className={`flex items-center gap-5 mb-5 ${className}`}>
        {icons.map(({ icon, alt, action }, index) => (
          <div key={index} className={`w-6 h-6 sm:w-8 sm:h-8 cursor-pointer`}>
            <Tooltip
              content={alt}
              color="user"
              size="lg"
              delay={100}
              className="-ml-3"
              position="top"
            >
              <ButtonIcon
                icon={icon}
                innerBtnCls="h-10 w-10 sm:h-12 sm:w-12"
                btnIconCls=" h-4 w-4 sm:h-5  sm:w-5"
                aria-label={alt}
                onClick={() => handleIconClick(alt, action)}
                ref={alt === 'Decoration' ? DecorationButtonRef : null}
              />
            </Tooltip>
          </div>
        ))}
      </div>
      {activeModal && (
        <Modal
          isOpen={!!activeModal}
          onClose={closeModal}
          position="center"
          title={
            icons.find((icon) => icon.alt === activeModal)?.modalTitle ||
            activeModal
          }
          width={
            icons.find((icon) => icon.alt === activeModal)?.modalWidth || 'md'
          }
        >
          {getModalContent()}
        </Modal>
      )}

      {isEmojiSelectorOpen && (
        <EmojiSelectorModal
          isOpen={isEmojiSelectorOpen}
          onClose={handleEmojiSelectorClose}
          onSelect={handleEmojiSelect}
          triggerRef={DecorationButtonRef}
          // position="bottom"
        />
      )}
    </>
  );
};

export default DiaryIconsSidebar;
