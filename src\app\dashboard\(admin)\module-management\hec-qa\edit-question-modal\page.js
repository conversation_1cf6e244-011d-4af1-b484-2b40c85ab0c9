'use client';

import React, { useState, useEffect } from 'react';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import { toast } from 'sonner';
import FormInput from '@/components/form/FormInput';
import api from '@/lib/api';
import { useSelector } from 'react-redux';

const EditQuestionModal = ({ isOpen, onClose, questionId, onQuestionUpdated }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [initialValues, setInitialValues] = useState({
    question: '',
    points: '',
    minimumWords: ''
  });
  const auth = useSelector((state) => state.auth);
const getQAQuestionDetailEndpoint = (roles, id) =>
    roles?.includes('tutor') ? `/tutor/qa/${id}` : `/admin/qa/${id}`;

  const getQAQuestionUpdateEndpoint = (roles, id) =>
    roles?.includes('tutor') ? `/tutor/qa/questions/${id}` : `/admin/qa/questions/${id}`;
  // Define validation schema
  const validationSchema = Yup.object().shape({
    question: Yup.string().required('Question is required'),
    points: Yup.number().required('Points is required').positive('Points must be positive'),
    minimumWords: Yup.number().required('Minimum Words is required').positive('Minimum words must be positive')
  });

  // Fetch question data when modal opens
  useEffect(() => {
    if (isOpen && questionId) {
      fetchQuestionData();
    }
  }, [isOpen, questionId]);

  const fetchQuestionData = async () => {
    try {
      setIsLoading(true);
      const response = await api.get(getQAQuestionDetailEndpoint(auth?.user?.roles, questionId));
      
      // Extract question data depending on the API response structure
      const questionData = response.data.data || response.data;
      
      setInitialValues({
        question: questionData.question || '',
        points: questionData.points?.toString() || '',
        minimumWords: questionData.minimumWords?.toString() || ''
      });
    } catch (error) {
      console.error('Error fetching question data:', error);
      toast.error('Failed to load question data');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = async (values, { setSubmitting }) => {
    try {
      // Convert string values to appropriate types
      const payload = {
        question: values.question,
        points: parseInt(values.points, 10),
        minimumWords: parseInt(values.minimumWords, 10)
      };

      await api.put(getQAQuestionUpdateEndpoint(auth?.user?.roles, questionId), payload);
    
      onQuestionUpdated();
      onClose();
    } catch (error) {
      console.error('Error updating question:', error);
      
    } finally {
      setSubmitting(false);
    }
  };

  const resetForm = () => {
    setInitialValues({
      question: '',
      points: '',
      minimumWords: ''
    });
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-lg w-full max-w-2xl p-6 max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold">Edit Question</h2>
          <button 
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        
        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
          </div>
        ) : (
          <Formik
            initialValues={initialValues}
            validationSchema={validationSchema}
            onSubmit={handleSubmit}
            enableReinitialize={true}
          >
            {({ isSubmitting, resetForm: formikResetForm }) => (
              <Form className="mx-auto bg-gray-100 p-6 rounded-lg shadow-md">
                <div className="flex flex-wrap gap-4 mb-4">
                  <div className="flex-1 min-w-[200px]">
                    <FormInput
                      label="Question"
                      name="question"
                      id="question"
                      placeholder="Write question here"
                      isTextarea={true}
                      autoResize={true}
                      required={true}
                    />
                  </div>
                  
                  <div className="flex-1 min-w-[200px]">
                    <FormInput
                      label="Points"
                      type="number"
                      name="points"
                      id="points"
                      placeholder="Enter points"
                      required={true}
                    />
                  </div>
                  
                  <div className="flex-1 min-w-[200px]">
                    <FormInput
                      label="Minimum Words"
                      type="number"
                      name="minimumWords"
                      id="minimumWords"
                      placeholder="Enter minimum words required"
                      required={true}
                    />
                  </div>
                </div>
                
                <div className="flex justify-end gap-4 mt-7">
                  <button
                    type="button"
                    className="bg-gray-300 hover:bg-gray-400 text-black font-medium py-2 px-4 rounded"
                    onClick={resetForm}
                    disabled={isSubmitting}
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="bg-[#FFDE34] hover:bg-yellow-400 text-black font-medium py-2 px-4 rounded"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? 'Saving...' : 'Save Changes'}
                  </button>
                </div>
              </Form>
            )}
          </Formik>
        )}
      </div>
    </div>
  );
};

export default EditQuestionModal;