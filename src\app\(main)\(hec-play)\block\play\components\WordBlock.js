import React, { useState } from 'react';
import Image from 'next/image';

const WordBlock = ({
  words = [],
  usedWords = [],
  title,
  hide = false,
  disable = false,
  disableDrag = false,
  disableClick = false,
  dragType,
  onDragStart,
  onClick,
  showImage = true,
  imageSrc = "/assets/images/all-img/footer_butterfly.png",
  imageWidth = 50,
  imageHeight = 50,
  imageAlt = "Icon",
  containerClassName = "",
  wordsContainerClassName = "",
  wordClassName = "",
  selectedWord = null,
  style = {}
}) => {
  const [draggedWord, setDraggedWord] = useState(null);
  const [touchStartPos, setTouchStartPos] = useState(null);

  if (hide) return null;

  const handleDragStart = (word) => (e) => {
    if (disable || disableDrag) {
      e.preventDefault();
      return;
    }
    if (onDragStart) {
      onDragStart(word, dragType);
    }
  };

  const handleClick = (word, index) => (e) => {
    if (disable || disableClick) {
      e.preventDefault();
      return;
    }
    if (onClick) {
      onClick(word, index);
    }
  };

  // Touch event handlers for mobile drag and drop
  const handleTouchStart = (word) => (e) => {
    if (disable || disableDrag) return;

    const touch = e.touches[0];
    setTouchStartPos({ x: touch.clientX, y: touch.clientY });
    setDraggedWord(word);

    if (onDragStart) {
      onDragStart(word, dragType);
    }
  };

  const handleTouchMove = (e) => {
    if (!draggedWord || !touchStartPos) return;
    e.preventDefault(); // Prevent scrolling
  };

  const handleTouchEnd = (e) => {
    if (!draggedWord || !touchStartPos) return;

    const touch = e.changedTouches[0];
    const elementBelow = document.elementFromPoint(touch.clientX, touch.clientY);

    // Find the closest drop zone
    const dropZone = elementBelow?.closest('[data-drop-zone]');
    if (dropZone) {
      const dropEvent = new CustomEvent('mobileDrop', {
        detail: { draggedWord, dragType }
      });
      dropZone.dispatchEvent(dropEvent);
    }

    setDraggedWord(null);
    setTouchStartPos(null);
  };

  const getWordClassName = () => {
    let baseClass = "px-2 py-1 sm:px-3 sm:py-2 lg:px-4 lg:py-2 bg-[#FFF8E6] border border-yellow-500 rounded-md text-xs sm:text-sm";

    if (disable) {
      baseClass += " opacity-50 cursor-not-allowed";
    } else if (!disableDrag && !disableClick) {
      baseClass += " cursor-grab active:cursor-grabbing shadow hover:cursor-pointer";
    } else if (!disableClick) {
      baseClass += " cursor-pointer";
    }

    if (wordClassName) {
      baseClass += ` ${wordClassName}`;
    }

    return baseClass;
  };

  const getContainerClassName = () => {
    let baseClass = "relative bg-white rounded-[32px] border-4 border-orange-300 p-6";

    if (containerClassName) {
      baseClass += ` ${containerClassName}`;
    }

    return baseClass;
  };

  const getWordsContainerClassName = () => {
    let baseClass = "flex flex-wrap gap-2 sm:gap-3";

    if (wordsContainerClassName) {
      baseClass += ` ${wordsContainerClassName}`;
    }

    return baseClass;
  };

  return (
    <div className={getContainerClassName()} style={style}>
      {showImage && (
        <Image
          src={imageSrc}
          width={imageWidth}
          height={imageHeight}
          alt={imageAlt}
          className="absolute -right-5 -top-5"
        />
      )}
      {title && <h2 className="text-base sm:text-lg font-bold mb-3 sm:mb-4">{title}</h2>}
      <div className={getWordsContainerClassName()}>
        {words.map((word, i) =>
          !usedWords.includes(word) ? (
            <div
              key={i}
              draggable={!disable && !disableDrag}
              onDragStart={handleDragStart(word)}
              onTouchStart={handleTouchStart(word)}
              onTouchMove={handleTouchMove}
              onTouchEnd={handleTouchEnd}
              onClick={handleClick(word, i)}
              className={`${getWordClassName()} ${
                draggedWord === word ? 'opacity-50 scale-105' : ''
              } ${
                selectedWord === word ? 'ring-2 ring-yellow-500 bg-yellow-100' : ''
              } transition-all duration-200 cursor-pointer select-none`}
              title={!disable && !disableDrag ? "Drag and Drop or Tap" : ""}
            >
              {word}
            </div>
          ) : null
        )}
      </div>
    </div>
  );
};

export default WordBlock;
