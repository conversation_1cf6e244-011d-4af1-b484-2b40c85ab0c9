'use client';

import NewTablePage from "@/components/form/NewTablePage";
import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import api from '@/lib/api';

const NovelEntriesList = () => {
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const [novelEntries, setNovelEntries] = useState([]);
  const [filteredEntries, setFilteredEntries] = useState([]);
  const [totalItems, setTotalItems] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [isError, setIsError] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  
  // Tab states
  const [activeTab, setActiveTab] = useState('all');
  const [availableStatuses, setAvailableStatuses] = useState([]);
  
  const router = useRouter();

  // Fetch novel entries
  const fetchNovelEntries = async () => {
    try {
      setIsLoading(true);
      const endpoint = `/tutor/novel/entries?page=${currentPage}&limit=${rowsPerPage}`;
      
      // console.log(`Fetching from: ${endpoint}`);
      
      const response = await api.get(endpoint);
      // console.log('Raw API response:', response);
      
      // Handle the response structure based on your provided API response
      if (response?.data) {
        let items = [];
        let totalCount = 0;
        let totalPagesCount = 0;
        
        // Check if response has success property and data array
        if (response.data.success && Array.isArray(response.data.data)) {
          items = response.data.data;
          totalCount = items.length;
          totalPagesCount = Math.ceil(totalCount / rowsPerPage);
        } else if (response.data.items && Array.isArray(response.data.items)) {
          // Fallback to items structure
          items = response.data.items;
          totalCount = response.data.totalCount || response.data.totalItems || 0;
          totalPagesCount = response.data.totalPages || 0;
        } else if (Array.isArray(response.data)) {
          // Direct array response
          items = response.data;
          totalCount = items.length;
          totalPagesCount = Math.ceil(totalCount / rowsPerPage);
        }
        
        if (items.length > 0) {
          const formattedEntries = items.map(item => ({
            id: item.id,
            topicTitle: item.topic?.title || 'Unknown Topic',
            sequenceTitle: item.topic?.sequenceTitle || 'Unknown Sequence',
            category: item.topic?.category || 'uncategorized',
            content: item.content,
            wordCount: item.wordCount,
            status: item.status,
            submittedAt: item.submittedAt,
            reviewedAt: item.reviewedAt,
            createdAt: item.createdAt,
            updatedAt: item.updatedAt,
            topic: item.topic,
            feedbacks: item.feedbacks || [],
            studentId: item.studentId,
            backgroundColor: item.backgroundColor,
            skinId: item.skinId
          }));
          
          setNovelEntries(formattedEntries);
          
          // Extract unique statuses for tabs
          const statuses = [...new Set(formattedEntries.map(entry => entry.status).filter(Boolean))];
          setAvailableStatuses(['all', ...statuses]);
          
          setTotalItems(totalCount);
          setTotalPages(totalPagesCount);
          setIsError(false);
          setErrorMessage('');
        } else {
          console.log('No entries found in response');
          setNovelEntries([]);
          setFilteredEntries([]);
          setAvailableStatuses(['all']);
          setTotalItems(0);
          setTotalPages(0);
          setIsError(false);
          setErrorMessage('');
        }
      } else {
        console.error('Unexpected data structure:', response);
        setIsError(true);
        setErrorMessage('Unexpected data structure received from API');
        setNovelEntries([]);
        setFilteredEntries([]);
      }
    } catch (err) {
      console.error(`Error fetching novel entries:`, err);
      setIsError(true);
      setErrorMessage(err.message || `An error occurred while fetching novel entries`);
      setNovelEntries([]);
      setFilteredEntries([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Filter entries based on active tab
  useEffect(() => {
    if (activeTab === 'all') {
      setFilteredEntries(novelEntries);
      setTotalItems(novelEntries.length);
    } else {
      const filtered = novelEntries.filter(entry => entry.status === activeTab);
      setFilteredEntries(filtered);
      setTotalItems(filtered.length);
    }
    // Reset to first page when changing tabs
    setCurrentPage(1);
  }, [activeTab, novelEntries]);

  // Handle view entry - Navigate to detail page
  const handleViewEntry = (entry) => {
    router.push(`/dashboard/submission-management/hec-novel/review/${entry.id}?tab=NovelSubmissionList`);
  };

  useEffect(() => {
    fetchNovelEntries();
  }, [currentPage, rowsPerPage]);

  // Handle tab change
  const handleTabChange = (tabValue) => {
    setActiveTab(tabValue);
  };

  // Define columns for the novel entries table
  const novelEntryColumns = [
    {
      label: 'Sequence',
      field: 'sequenceTitle',
    },
    {
      label: 'Topic Title',
      field: 'topicTitle',
    },
    // {
    //   label: 'Word Count',
    //   field: 'wordCount',
    // },
    {
      label: 'Status',
      field: 'status',
      cellRenderer: (value) => (
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
          value === 'submitted' 
            ? 'bg-blue-100 text-blue-800' 
            : value === 'reviewed'
            ? 'bg-green-100 text-green-800'
            : 'bg-gray-100 text-gray-800'
        }`}>
          {value?.charAt(0).toUpperCase() + value?.slice(1)}
        </span>
      )
    },
    // {
    //   label: 'Submitted At',
    //   field: 'submittedAt',
    //   render: (value) => value ? new Date(value).toLocaleDateString() : 'Not submitted'
    // },
  ];

  // Define actions - Only view action (navigates to detail page)
  const novelEntryActions = [
    {
      name: 'view',
      icon: 'heroicons-outline:eye',
      className: 'text-blue-600 hover:text-blue-700 cursor-pointer',
      onClick: handleViewEntry,
      disabled: false,
      tooltip: 'View',
    },
  ];

  // Handle page change
  const handleChangePage = (page) => {
    setCurrentPage(page);
  };

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-6">HEC Novel Entries List</h1>
      
      {/* Tab Navigation */}
      <div className="">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8" aria-label="Tabs">
            {/* Status-based Tabs */}
            {availableStatuses.map((status) => {
              return (
                <button
                  key={status}
                  onClick={() => handleTabChange(status)}
                  className={`py-2 px-4 border-b-2 font-medium text-sm transition-all duration-200 hover:bg-yellow-100 ${
                    activeTab === status
                      ? 'border-yellow-400 text-black bg-yellow-50'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  {/* Status Name */}
                  <span className="capitalize">
                    {status === 'all' ? 'All Entries' : status.replace(/([A-Z])/g, ' $1').trim()}
                  </span>
                </button>
              );
            })}
          </nav>
        </div>
      </div>

      {/* Content Area - Novel Entries */}
      <div className="bg-white">
        <NewTablePage
          title=""
          createButton={false}
          columns={novelEntryColumns}
          data={filteredEntries}
          actions={novelEntryActions}
          currentPage={currentPage}
          changePage={handleChangePage}
          totalItems={totalItems}
          rowsPerPage={rowsPerPage}
          setRowsPerPage={setRowsPerPage}
          totalPages={Math.ceil(filteredEntries.length / rowsPerPage)}
          loading={isLoading}
          error={isError}
          errorMessage={errorMessage}
          showCheckboxes={false}
          showSearch={false}
          showNameFilter={false}
          showSortFilter={false}
          showCreateButton={false}
          hideTitle={true}
        />
      </div>
    </div>
  );
};

export default NovelEntriesList;