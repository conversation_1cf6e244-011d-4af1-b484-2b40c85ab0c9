'use client';
import React from 'react';
import { useSelector } from 'react-redux';
import { 
  selectIsDecorating,
  selectDecorationItems,
  selectCanUndo,
  selectCanRedo
} from '@/store/features/diarySlice';

const DecorationDebug = () => {
  const isDecorating = useSelector(selectIsDecorating);
  const decorationItems = useSelector(selectDecorationItems);
  const canUndo = useSelector(selectCanUndo);
  const canRedo = useSelector(selectCanRedo);
  const diaryState = useSelector(state => state.diary);

  return (
    <div style={{
      position: 'fixed',
      top: '10px',
      left: '10px',
      background: 'white',
      border: '2px solid red',
      padding: '10px',
      fontSize: '12px',
      zIndex: 9999,
      maxWidth: '300px'
    }}>
      <h4>Decoration Debug Info</h4>
      <p><strong>isDecorating:</strong> {isDecorating ? 'true' : 'false'}</p>
      <p><strong>decorationItems count:</strong> {decorationItems.length}</p>
      <p><strong>canUndo:</strong> {canUndo ? 'true' : 'false'}</p>
      <p><strong>canRedo:</strong> {canRedo ? 'true' : 'false'}</p>
      <p><strong>currentHistoryIndex:</strong> {diaryState.currentHistoryIndex}</p>
      <p><strong>historyLength:</strong> {diaryState.decorationHistory.length}</p>
      <details>
        <summary>Full State</summary>
        <pre style={{fontSize: '10px', maxHeight: '200px', overflow: 'auto'}}>
          {JSON.stringify({
            decorationItems: diaryState.decorationItems,
            decorationHistory: diaryState.decorationHistory,
            currentHistoryIndex: diaryState.currentHistoryIndex
          }, null, 2)}
        </pre>
      </details>
    </div>
  );
};

export default DecorationDebug;
