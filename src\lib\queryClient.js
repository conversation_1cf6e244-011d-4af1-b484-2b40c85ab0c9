import { QueryClient } from '@tanstack/react-query';
import { handleTokenExpiration, isTokenExpiredError } from './authUtils';

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: true,
      retry: (failureCount, error) => {
        // Don't retry on token expiration
        if (isTokenExpiredError(error)) {
          handleTokenExpiration();
          return false;
        }
        // Retry other errors up to 1 time
        return failureCount < 1;
      },
      // staleTime: 5 * 60 * 1000, // 5 minutes
    },
    mutations: {
      retry: (failureCount, error) => {
        // Don't retry on token expiration
        if (isTokenExpiredError(error)) {
          handleTokenExpiration();
          return false;
        }
        // Don't retry mutations by default
        return false;
      },
    },
  },
});
