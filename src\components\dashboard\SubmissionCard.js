import React from 'react';
import { Icon } from '@iconify/react';

const SubmissionCard = ({
  title,
  countNumber,
  icon = 'oui:token-module',
  bgColor = 'bg-white',
  className = '',
}) => {
  return (
    <div
      className={`${bgColor} rounded-xl p-5 shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200 ${className}`}
    >
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-sm font-medium text-gray-500 tracking-wider">
          {title}
        </h3>
        <div className="bg-yellow-50/80 rounded-lg p-2">
          <Icon icon={icon} className="w-7 h-7 text-yellow-600" />
        </div>
      </div>

      {typeof countNumber === 'object' ? (
        <div className="space-y-2">
          <div className="flex items-center gap-4">
            <div>
              <p className={`text-xl font-bold text-gray-800`}>
                {countNumber.review?.count}
              </p>
              <p className="text-xs font-medium text-gray-400">
                {countNumber.review?.title}
              </p>
            </div>
            <div className="h-8 w-px bg-gray-200"></div>
            <div>
              <p className="text-xl font-bold text-gray-800">
                {countNumber.feedback?.count}
              </p>
              <p className="text-xs font-medium text-gray-400">
                {countNumber.feedback?.title}
              </p>
            </div>
          </div>
        </div>
      ) : (
        <div>
          <p className="text-3xl font-bold text-gray-800">{countNumber}</p>
        </div>
      )}
    </div>
  );
};

export default SubmissionCard;
