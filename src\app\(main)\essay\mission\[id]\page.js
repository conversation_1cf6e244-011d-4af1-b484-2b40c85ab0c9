'use client';

import { useState, useEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { toast } from 'sonner';
import { useRouter, useParams } from 'next/navigation';
import SkinPreview from '@/components/skin/SkinPreview';
import useDataFetch from '@/hooks/useDataFetch';
import api from '@/lib/api';
import DetailsModal from '@/components/form/modal/MissionConfirmationModal';
import Button, { ButtonIcon } from '@/components/Button';
import EssayFeedBackModal from '../../_components/FeedbackModal';
import Tooltip from '@/components/Tooltip';
import {
  selectIsSkinModalOpen,
  selectLayoutBackground,
  setIsSkinModalOpen,
} from '@/store/features/diarySlice';
import SelectSkinModal from '../../../diary/_component/SelectSkinModal';
import GoBack from '@/components/shared/GoBack';
import <PERSON><PERSON>iewer from '@/components/EditorViewer';
import { Form, Formik } from 'formik';
import SimpleTiptapEditor from '@/components/form/SimpleTiptapEditor';
import WordValidationMsg from '@/components/form/validation/WordValidationMsg';
import { countWords } from '@/utils/submissionUtils';

// feedback data show
// correction data show

export default function WriteEssayMission() {
  const router = useRouter();
  const dispatch = useDispatch();
  const { id: taskId } = useParams();
  const editorRef = useRef(null);
  const [isSaving, setIsSaving] = useState(false);
  const [modalData, setModalData] = useState(null);
  const [showError, setShowError] = useState(false);
  const isSkinModalOpen = useSelector(selectIsSkinModalOpen);
  const [selectedSkin, setSelectedSkin] = useState(null);
  const layoutBackground = useSelector(selectLayoutBackground);
  const [subject, setSubject] = useState('');
  const [value, setValue] = useState('');
  const [currentWordCount, setCurrentWordCount] = useState(0);
  const [isTargetedWordCount, setIsTargetedWordCount] = useState(false);
  const [showSubmission, setShowSubmission] = useState(false);

  if (!taskId) return router.push('/essay/mission');

  const { data: submissionDetails, refetch } = useDataFetch({
    queryKey: ['/student-essay/submissions', taskId],
    endPoint: `/student-essay/submissions/${taskId}`,
    enabled: !!taskId,
  });

  const isEditing = submissionDetails?.status !== 'submitted';
  const latestSubmission = submissionDetails?.submissionHistory[0];

  useEffect(() => {
    if (submissionDetails?.task) {
      setSubject(submissionDetails?.task?.title);
    }

    if (latestSubmission?.content?.length > 0) {
      setValue(latestSubmission?.content);
      setShowSubmission(true);
    }
  }, [submissionDetails]);

  useEffect(() => {
    if (value) {
      const count = countWords(value);
      setCurrentWordCount(count);
    }
  }, [value]);

  // handle submit
  const handleSave = async () => {
    if (!isTargetedWordCount) {
      setShowError(true);
      return;
    } else {
      setShowError(false);
    }

    setIsSaving(true);

    const payload = {
      taskId: taskId,
      title: subject,
      content: value,
    };

    try {
      await api.post('/student-essay/submit/essay', payload);
      refetch();
      router.push('/essay/mission');
      setShowError(false);
    } catch (error) {
      console.error('Error saving:', error);
    } finally {
      setIsSaving(false);
    }
  };

  // handle auto draft save
  const handleAutoSave = async (title, content) => {
    const submissionId = submissionDetails?.id;
    if (!submissionId || submissionDetails?.status === 'submitted') return;

    try {
      const payload = {
        submissionId: submissionId,
        // skinId: selectedSkin?.id || null,
        title: title || subject,
        content: content || value,
      };
      // console.log(payload);

      await api.post('/student-essay/submit/essay/update', payload, {
        showSuccessToast: false,
      });
      console.log('Auto-saved successfully');
    } catch (error) {
      console.error('Auto-save error:', error);
    }
  };

  // Auto-update effect - moved before conditional return to maintain hook order
  useEffect(() => {
    const timeOut = setTimeout(() => {
      handleAutoSave(subject, value);
    }, 800);

    return () => clearTimeout(timeOut);
  }, [value]);

  return (
    <div className="relative">
      <div className="max-w-7xl mx-auto px-5 xl:px-0 relative z-10">
        <GoBack
          title={'Essay Missions'}
          linkClass="my-5 mb-8 w-full max-w-52"
        />

        <div className="p-5 rounded-lg bg-[#FFF9FB] shadow-lg space-y-5 mb-10">
          <div className="p-5 bg-[#FCF8EF] rounded-lg [box-shadow:2px_2px_12px_0px_#F5D1B066_inset,_-2px_-2px_12px_0px_#F5D1B066_inset] flex sm:items-center justify-between flex-col sm:flex-row sm:flex-row-reverse gap-2">
            <h1 className="text-3xl font-semibold text-yellow-600 font-serif">
              Mission Essay
            </h1>

            <div>
              <h1 className="text-2xl text-yellow-800 font-semibold">
                {submissionDetails?.task?.title}
              </h1>
              <div className="flex items-start gap-6">
                {latestSubmission?.picture && (
                  <Image
                    src={latestSubmission?.picture}
                    alt={latestSubmission?.title}
                    width={200}
                    height={200}
                  />
                )}
                <div>
                  {submissionDetails?.task?.instructions && (
                    <p className="font-semibold text-lg text-gray-700">
                      Instruction:
                    </p>
                  )}
                </div>
              </div>
              <EditorViewer
                data={
                  submissionDetails?.task?.instructions ||
                  latestSubmission?.description
                }
              />
            </div>
          </div>

          {showSubmission ? (
            <div className="space-y-3">
              <div className="p-5 shadow-lg rounded-lg border relative bg-white">
                <h1 className="text-xl text-yellow-800 font-semibold">
                  My Essay Mission Entry
                </h1>
                <EditorViewer
                  data={
                    latestSubmission?.content?.length > 200
                      ? latestSubmission?.content?.slice(0, 400) + '...'
                      : latestSubmission?.content
                  }
                />
                {(submissionDetails?.status !== 'submitted' ||
                  submissionDetails?.isResubmission) && (
                  <div className="absolute right-2 top-2 flex items-center gap-2">
                    <ButtonIcon
                      icon={'ri:edit-2-fill'}
                      innerBtnCls={'h-10 w-10'}
                      btnIconCls={'h-5 w-5'}
                      onClick={() => setShowSubmission(false)}
                    />
                  </div>
                )}
              </div>

              <div className="p-5 shadow-lg rounded-lg border relative bg-white">
                <h1 className="text-xl text-yellow-800 font-semibold">
                  Tutor Correction Zone
                </h1>
                {submissionDetails?.submissionMark?.submissionFeedback && (
                  <>
                    <div className="absolute right-2 top-2">
                      <ButtonIcon
                        icon={'arcticons:feedback-2'}
                        innerBtnCls={'h-10 w-10'}
                        btnIconCls={'h-4 w-4'}
                        onClick={() => setModalData(submissionDetails?.submissionMark?.submissionFeedback)}
                      />
                    </div>
                  </>
                )}

                {submissionDetails?.submissionMark && (
                  <div
                    dangerouslySetInnerHTML={{
                      __html: submissionDetails?.submissionMark?.taskRemarks,
                    }}
                    className={`mt-2`}
                  />
                )}

                <p
                  className={`mt-2 ${
                    submissionDetails?.status === 'reviewed' ||
                    submissionDetails?.status === 'resubmitted'
                      ? 'text-green-500 text-center'
                      : 'text-red-600 text-center'
                  }`}
                >
                  {submissionDetails?.status === 'reviewed' ||
                  submissionDetails?.status === 'resubmitted'
                    ? 'Reviewed'
                    : 'Not Reviewed Yet'}
                </p>
              </div>
            </div>
          ) : (
            <Formik
              initialValues={{
                content: value || latestSubmission?.submission?.answer || '',
              }}
              onSubmit={handleSave}
              enableReinitialize
            >
              {({ isSubmitting }) => (
                <Form>
                  <div className="space-y-2">
                    <div className="relative">
                      {/* <div className="absolute right-2 top-3 z-10">
                        <button
                          className="bg-[#FFF9E6] border border-[#D4A574] text-[#8B4513] hover:bg-[#FFF5D6] hover:border-[#C19A5B] text-xs font-medium px-4 py-1 rounded-full transition-colors duration-200 shadow"
                          onClick={() => setShowHistory(true)}
                          aria-label="View History"
                        >
                          View History
                        </button>
                      </div> */}

                      <SimpleTiptapEditor
                        ref={editorRef}
                        onInit={(_evt, editor) => (editorRef.current = editor)}
                        initialValue={value || '<p></p>'}
                        onEditorChange={(content) => {
                          setValue(content);
                          setCurrentWordCount(countWords(content));
                        }}
                        height={500}
                        maxWords={submissionDetails?.task?.wordLimitMaximum}
                      />

                      {/* Word count display */}
                      <div className="absolute left-2 w-[98.5%] z-20 flex justify-between items-center mt-2 text-sm">
                        <div className="font-medium text-gray-600">
                          <span>
                            {currentWordCount} /{' '}
                            {submissionDetails?.task?.wordLimitMaximum} words
                          </span>
                        </div>
                      </div>

                      <div className="h-6 rounded-b-lg w-[98.5%] bg-white absolute left-1 bottom-1 z-10"></div>
                    </div>

                    <div className="flex justify-end">
                      <WordValidationMsg
                        wordCount={currentWordCount}
                        minimumWords={submissionDetails?.task?.wordLimitMinimum}
                        maximumWords={submissionDetails?.task?.wordLimitMaximum}
                        setIsValidWord={setIsTargetedWordCount}
                      />
                    </div>
                  </div>

                  {(submissionDetails?.status === 'reviewed' ||
                    submissionDetails?.status === 'resubmitted' ||
                    submissionDetails?.isResubmission ||
                    submissionDetails?.status === 'draft') && (
                    <div className="flex justify-center mt-3 gap-3">
                      <Button
                        buttonText="Cancel"
                        type="button"
                        onClick={() => setShowSubmission(true)}
                      />
                      <Button
                        disabled={isSubmitting}
                        buttonText={
                          submissionDetails?.status === 'draft'
                            ? isSubmitting
                              ? 'Submitting...'
                              : 'Submit'
                            : submissionDetails?.status === 'reviewed'
                            ? isSubmitting
                              ? 'Updating...'
                              : 'Update'
                            : 'Update'
                        }
                        type="submit"
                        className="bg-yellow-400 hover:bg-yellow-500 text-black"
                      />
                    </div>
                  )}
                </Form>
              )}
            </Formik>
          )}
        </div>
      </div>

      <EssayFeedBackModal
        isOpen={!!modalData}
        onClose={() => setModalData(null)}
        data={modalData}
      />
    </div>
  );
}
