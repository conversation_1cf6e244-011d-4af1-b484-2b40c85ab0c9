'use client';
import React, { useEffect, useState } from 'react';
import NewTablePage from '@/components/form/NewTablePage';
import useDataFetch from '@/hooks/useDataFetch';
import Image from 'next/image';
import DeleteModal from '@/components/form/modal/DeleteModal';
import { useRouter } from 'next/navigation';
import api from '@/lib/api';

const Storymaker = ({ onChangeTab }) => {
  const router = useRouter();
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const [deleteData, setDeleteData] = useState(null);
  const [processedData, setProcessedData] = useState([]);

  const { data, isLoading, refetch } = useDataFetch({
    queryKey: ['storymaker', currentPage, rowsPerPage],
    endPoint: '/play/story-maker/admin/stories',
    params: { page: currentPage, limit: rowsPerPage },
  });

  const storyItems = data?.items || [];

  // Use server-side pagination data from API response
  const totalItems = data?.totalItems || data?.totalCount || 0;
  const totalPages = data?.totalPages || Math.ceil(totalItems / rowsPerPage);

  useEffect(() => {
    if (storyItems) {
      const formattedData = storyItems.map((item, index) => ({
        id: item.id,
        title: item?.title,
        picture: item.picture,
        score: item.score,
        is_active: { id: item.id, is_active: item.is_active },
      }));

      setProcessedData(formattedData);
    }
  }, [storyItems]);

  // Define columns for the table
  const columns = [
    {
      label: 'QUESTION SET TITLE',
      field: 'title',
    },
    {
      label: 'QUESTION IMAGE',
      field: 'picture',
      cellRenderer: (value) => (
        <Image
          src={value || '/assets/images/all-img/noImage.png'}
          width={60}
          height={60}
          alt="Question"
          className="h-10 w-10 object-cover rounded"
        />
      ),
    },
    // {
    //   label: 'TOTAL SCORE',
    //   field: 'score',
    // },
    {
      label: 'ACTIVE STATUS',
      field: 'is_active',
      cellRenderer: (row) => {
        const toggleStatus = async (id, currentStatus) => {
          try {
            await api.patch(
              `/play/story-maker/admin/stories/${id}/toggle-status`,
              { is_active: !currentStatus }
            );
            refetch();
          } catch (error) {
            console.log(error);
            alert('Failed to update plan status. Please try again.');
          }
        };

        return (
          <button
            className={`relative inline-flex h-6 w-11 items-center rounded-full ${
              row?.is_active ? 'bg-green-500' : 'bg-gray-300'
            }`}
            onClick={() => toggleStatus(row?.id, row?.is_active)}
          >
            <span
              className={`inline-block h-4 w-4 transform rounded-full bg-white transition ${
                row?.is_active ? 'translate-x-6' : 'translate-x-1'
              }`}
            />
          </button>
        );
      },
    },
  ];

  // Define actions
  const actions = [
    {
      name: 'view',
      icon: 'material-symbols:visibility',
      className: 'text-blue-600',
      onClick: (row) =>
        router.push(
          `/dashboard/module-management/hec-play/storymaker/${row?.id}`
        ),
    },
    {
      name: 'edit',
      icon: 'material-symbols:edit',
      className: 'text-gray-600',
      onClick: (row) =>
        router.push(
          `/dashboard/module-management/hec-play/storymaker/edit/${row?.id}`
        ),
    },
    {
      name: 'delete',
      icon: 'heroicons-outline:trash',
      className: 'text-red-600',
      onClick: (row) => setDeleteData(row),
    },
  ];

  // Handle page change
  const handleChangePage = (page) => {
    setCurrentPage(page);
  };

  // Handle rows per page change
  const handleRowsPerPageChange = (newRowsPerPage) => {
    setRowsPerPage(newRowsPerPage);
    setCurrentPage(1); // Reset to first page when changing rows per page
  };

  return (
    <div className="container mx-auto p-4">
      <NewTablePage
        title="Storymaker Games"
        createButton="Create Question Set"
        createBtnLink="/dashboard/module-management/hec-play/storymaker/add"
        columns={columns}
        data={processedData}
        actions={actions}
        currentPage={currentPage}
        changePage={handleChangePage}
        totalItems={totalItems}
        rowsPerPage={rowsPerPage}
        setRowsPerPage={handleRowsPerPageChange}
        totalPages={totalPages}
        showCheckboxes={false}
        showSearch={true}
        showNameFilter={false}
        showSortFilter={false}
      />

      <DeleteModal
        isOpen={!!deleteData}
        onClose={() => setDeleteData(null)}
        onSuccess={refetch}
        data={deleteData}
        endPoint={`/play/story-maker/admin/stories/${deleteData?.id}`}
        itemName="story"
      />
    </div>
  );
};

export default Storymaker;
