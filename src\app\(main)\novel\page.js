'use client';
import useDataFetch from '@/hooks/useDataFetch';
import { Icon } from '@iconify/react';
import Link from 'next/link';
import React, { useState } from 'react';
import SuggestTopicSection from './_components/SuggestTopicSection';
import api from '@/lib/api';
import { useRouter } from 'next/navigation';
import { useDispatch } from 'react-redux';
import SkinSelectionModal from './_components/SkinSelectionModal';
import GreetingModal from './_components/GreetingModal';

const HecNovel = () => {
  const router = useRouter();
  const dispatch = useDispatch();
  const [modalOpen, setModalOpen] = useState(false);
  const [showGreetingModal, setShowGreetingModal] = useState(false);
  const [monthlyProject, setMonthlyProject] = useState(true);
  const { data: topics, isLoading } = useDataFetch({
    queryKey: ['student-novel-topics', monthlyProject],
    endPoint: `/student/novel/topics${
      monthlyProject ? '?category=monthly' : '?category=quarterly'
    }`,
  });

  const handleGo = async (id) => {
    try {
      const response = await api.get(`/student/novel/entries/topic/${id}`);
      router.push(`/novel/${id}`);
    } catch (error) {
      console.log(error);

      if (
        error?.response?.data?.error?.type ===
        'NovelTutorGreetingRequiredException'
      ) {
        setShowGreetingModal(true);
      }
      if (
        error?.response?.data?.error?.type ===
        'NovelDefaultSkinRequiredException'
      ) {
        setModalOpen(true);
      }
    }
  };

  return (
    <div className="max-w-7xl mx-auto p-5 xl:px-0 my-6 space-y-5 relative">
      <SuggestTopicSection />

      <div className="p-5 bg-[#FCF8EF] shadow-md border rounded-lg">
        <div className="">
          <div className="flex items-center border-2 border-yellow-500 rounded-full bg-[#FEFCE8]">
            <div className="w-full flex-1 relative">
              <button
                onClick={() => setMonthlyProject(true)}
                className={`w-full flex-1 py-2.5 ${
                  monthlyProject && 'bg-yellow-500  rounded-l-full'
                }`}
              >
                Monthly Novel Projects
              </button>
              {monthlyProject && (
                <div className="h-6 w-6 bg-yellow-500 rotate-45 absolute z-0 -bottom-3 left-1/2 -translate-x-1/2"></div>
              )}
            </div>

            <div className="w-full flex-1 relative">
              <button
                onClick={() => setMonthlyProject(false)}
                className={`w-full py-2.5 ${
                  !monthlyProject && 'bg-yellow-500 rounded-r-full'
                }`}
              >
                Quarterly Novel Projects
              </button>

              {!monthlyProject && (
                <div className="h-6 w-6 bg-yellow-500 rotate-45 absolute z-0 -bottom-3 left-1/2 -translate-x-1/2"></div>
              )}
            </div>
          </div>
        </div>

        {isLoading ? (
          <div className="flex justify-center items-center py-20">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-yellow-500"></div>
          </div>
        ) : topics?.length === 0 ? (
          <div className="text-center py-20">
            <p className="text-lg text-gray-600">No topics found.</p>
          </div>
        ) : (
          <div className="space-y-5 my-10">
            {topics?.map((item, idx) => (
              <div key={idx} className="flex items-start gap-2">
                <div className="flex flex-col justify-center items-center gap-5 mt-1">
                  <span
                    className={`inline-block text-black font-medium py-2 sm:px-8 px-3 text-sm sm:text-base text-center rounded-lg whitespace-nowrap
                    border-2 border-yellow-100 shadow-[0px_4px_6px_-1px_rgba(0,0,0,0.1),0px_2px_4px_-2px_rgba(0,0,0,0.1),-4px_8px_12px_0px_#00000026] transition-all duration-300 bg-gradient-to-b from-yellow-300 to-yellow-500 relative sm:pr-8 ring-2 ring-[#A36105] `}
                  >
                    Topic {idx + 1}
                  </span>

                  {idx !== topics.length - 1 && (
                    <div className="w-[1px] border border-gray-400 border-dashed min-h-[100px] h-full"></div>
                  )}
                </div>
                <Icon
                  icon="mynaui:arrow-long-right"
                  width="24"
                  height="24"
                  className="mt-6"
                />
                <div className="space-y-3 p-5 border border-[#FDE7E9] rounded-xl bg-[#FFF9FB] text-gray-600 w-full shadow">
                  <h2 className="text-2xl text-yellow-800 font-semibold">
                    {item?.sequenceTitle}
                  </h2>
                  <div>
                    <h4>Instruction: </h4>

                    <p>{item?.instruction}</p>
                  </div>

                  <button
                    onClick={() => handleGo(item?.id)}
                    className={`inline-block text-black font-medium py-2 px-8  text-center rounded-full whitespace-nowrap
                    border-2 border-yellow-100
                    shadow-[0px_4px_6px_-1px_rgba(0,0,0,0.1),0px_2px_4px_-2px_rgba(0,0,0,0.1),-4px_8px_12px_0px_#00000026]
                    transition-all duration-300
                    bg-gradient-to-b from-yellow-300 to-yellow-500 hover:from-yellow-400 hover:to-yellow-600
                    relative pr-8
                    ring-2 ring-[#A36105] `}
                  >
                    Go
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      <SkinSelectionModal
        onClose={() => setModalOpen(false)}
        isOpen={modalOpen}
      />
      
      <GreetingModal
        onClose={() => setShowGreetingModal(false)}
        isOpen={showGreetingModal}
      />
    </div>
  );
};

export default HecNovel;
