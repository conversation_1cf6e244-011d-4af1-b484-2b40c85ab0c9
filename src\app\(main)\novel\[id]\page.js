'use client';
import { useState, useEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { toast } from 'sonner';
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import SkinPreview from '@/components/skin/SkinPreview';
import useDataFetch from '@/hooks/useDataFetch';
import api from '@/lib/api';
import DetailsModal from '@/components/form/modal/MissionConfirmationModal';
import { ButtonIcon } from '@/components/Button';
import Tooltip from '@/components/Tooltip';
import {
  selectIsSkinModalOpen,
  selectLayoutBackground,
  setIsSkinModalOpen,
} from '@/store/features/diarySlice';
import EssayFeedBackModal from '../../essay/_components/FeedbackModal';
import SelectSkinModal from '../../diary/_component/SelectSkinModal';
import CorrectionSection from '../_components/CorrectionSection';
import Link from 'next/link';
import HistoryModal from '../_components/HistoryModal';

const WriteNovel = () => {
  const dispatch = useDispatch();
  const router = useRouter();
  const { id } = useParams();

  const [isSaving, setIsSaving] = useState(false);
  const [showError, setShowError] = useState(false);
  const isSkinModalOpen = useSelector(selectIsSkinModalOpen);
  const [selectedSkin, setSelectedSkin] = useState(null);
  const layoutBackground = useSelector(selectLayoutBackground);
  const [isOpen, setIsOpen] = useState(false);
  const [isEdit, setIsEdit] = useState(false);

  const [subject, setSubject] = useState('');
  const [body, setBody] = useState('');
  const [wordCount, setWordCount] = useState(0); // Single declaration
  const [date, setDate] = useState(new Date().toISOString().slice(0, 10));

  const textareaRef = useRef(null);

  const {
    data: novelDetails,
    isLoading,
    refetch,
  } = useDataFetch({
    queryKey: ['novel-details', id],
    endPoint: `/student/novel/entries/topic/${id}`,
  });

  let haveCorrection = novelDetails?.correction?.correction;

  useEffect(() => {
    if (!isLoading) {
      setSelectedSkin(novelDetails?.skin);
      setSubject(novelDetails?.topic?.title);
      setBody(novelDetails?.content);
      setDate(
        novelDetails?.submissionDate
          ? new Date(novelDetails?.submissionDate).toISOString().slice(0, 10)
          : new Date().toISOString().slice(0, 10)
      );
    }
  }, [novelDetails]);

  const countWords = (html) => {
    if (!html) return 0;
    const text = html.replace(/<[^>]*>/g, ' ');
    const cleanText = text.replace(/&nbsp;|&|<|>|"|'/g, ' ');
    return cleanText
      .trim()
      .split(/\s+/)
      .filter((word) => word.length > 0).length;
  };

  useEffect(() => {
    setWordCount(countWords(body));
  }, [body]);

  // Auto-update effect - moved before conditional return to maintain hook order
  useEffect(() => {
    if (!body || isLoading) return; // Don't update if no content or still loading

    const timeOut = setTimeout(() => {
      handleUpdate();
    }, 800);

    return () => clearTimeout(timeOut);
  }, [body, isLoading, selectedSkin?.id]);

  useEffect(() => {
    if (novelDetails?.content?.length > 0) {
      setIsEdit(false);
    } else if (novelDetails?.status === 'new') {
      setIsEdit(true);
    }
  }, [novelDetails]);

  const handleSave = async () => {
    if (body?.length < 1) {
      toast.message('Please write something before submitting');
      return;
    }

    const payload = {
      entryId: novelDetails?.id,
      // skinId: essayId,
      skinId: selectedSkin?.id || null,
      backgroundColor: layoutBackground,
      //   title: subject,
      content: body,
    };
    console.log(payload);

    try {
      setIsSaving(true);
      const response = await api.post('/student/novel/entries/submit', payload);
      refetch();
      setShowError(false);
      router.push('/novel');
    } catch (error) {
      console.error('Error saving:', error);
    } finally {
      setIsSaving(false);
      setIsEdit(false);
    }
  };

  const handleUpdate = async () => {
    const payload = {
      skinId: selectedSkin?.id || null,
      backgroundColor: layoutBackground,
      //   title: subject,
      content: body,
    };

    try {
      const response = await api.put(
        `/student/novel/entries/${novelDetails?.id}`,
        payload,
        { showSuccessToast: false }
      );
      setShowError(false);
    } catch (error) {
      console.error('Error saving:', error);
    } finally {
      // setIsSaving(false);
    }
  };

  const handleEditClick = () => {
    setIsEdit(true);
    // Use setTimeout to ensure the textarea is rendered before focusing
    setTimeout(() => {
      if (textareaRef.current) {
        textareaRef.current.focus();
        // Set cursor position to the end of the text
        const textLength = textareaRef.current.value.length;
        textareaRef.current.setSelectionRange(textLength, textLength);
      }
    }, 0);
  };

  const handleSkinChange = async (
    newSkin,
    dispatch,
    setSelectedSkin,
    setSubject,
    setMessage
  ) => {
    if (!newSkin.templateContent) {
      toast.error('This skin has no valid template content');
      return;
    }

    try {
      // 1. First reset everything
      // setSubject('');
      // setMessage('');

      // // 3. Parse and apply template
      // const data = JSON.parse(newSkin.templateContent);
      setSelectedSkin(newSkin);

      toast.success(`Skin "${newSkin.name}" applied successfully`);
    } catch (error) {
      console.error('Error applying skin template:', error);
      toast.error('Failed to apply skin template');
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-yellow-500"></div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto p-5 xl:px-0 my-8 relative">
      <div className="p-2 bg-[#FDE7E9] grid grid-cols-1 lg:grid-cols-2 gap-3 rounded">
        <div className="bg-white rounded-lg text-center flex items-center relative py-5">
          <div className="space-y-5">
            {/* {console.log(novelDetails?.skin?.templateContent,'dif', selectedSkin.templateContent, 'HELLO')} */}
            <SkinPreview
              skin={
                selectedSkin
                  ? selectedSkin?.templateContent
                  : novelDetails?.skin?.templateContent
              }
              contentData={{
                subject,
                body,
                date,
              }}
            />

            <button
              className="bg-[#FFF9E6] border border-[#D4A574] text-[#8B4513] hover:bg-[#FFF5D6] hover:border-[#C19A5B] text-xs font-medium px-4 py-1 rounded-full transition-colors duration-200 shadow"
              onClick={() => setIsOpen(true)}
              aria-label="View History"
            >
              View History
            </button>
          </div>
        </div>

        {/* Example inputs for subject, body, and date */}
        <div className=" bg-white rounded-lg p-3 space-y-3 w-full">
          <div className="flex flex-col p-2 shadow-lg border rounded-lg gap-3">
            <div className="flex items-center justify-between border-b border-dashed pb-1 gap-3">
              <h2>{novelDetails?.topic?.title}</h2>
              <span className="min-w-24">{date}</span>
            </div>
            {isEdit ? (
              <textarea
                ref={textareaRef}
                autoComplete="off"
                autoCorrect="off"
                spellCheck="false"
                autoCapitalize="off"
                data-gramm="false"
                data-gramm_editor="false"
                data-enable-grammarly="false"
                value={body}
                onPaste={(e) => e.preventDefault()}
                onChange={(e) =>
                  novelDetails?.status !== 'submitted' &&
                  setBody(e.target.value)
                }
                placeholder="Novel Content"
                className={`border ${
                  haveCorrection ? 'min-h-60' : 'min-h-80'
                } ${
                  showError && 'border-red-500'
                } p-2 w-full rounded focus:outline-[1px] focus:outline-gray-400 shadow-[inset_2px_2px_6px_0px_#0000001F]`}
                rows={4}
              />
            ) : (
              <p
                className={`w-full rounded ${
                  haveCorrection ? 'min-h-60' : 'min-h-80'
                }`}
              >
                {body || 'No content available.'}
              </p>
            )}
            <span className="flex justify-end">
              {(novelDetails?.status !== 'submitted' ||
                novelDetails?.isResubmission) &&
                (isEdit ? (
                  <button
                    onClick={handleSave}
                    disabled={isSaving}
                    className={` text-black font-medium py-1 px-5 text-sm text-center rounded-full whitespace-nowrap
                border-2 border-yellow-100
                shadow-[0px_4px_6px_-1px_rgba(0,0,0,0.1),0px_2px_4px_-2px_rgba(0,0,0,0.1),-4px_8px_12px_0px_#00000026]
                transition-all duration-300
                bg-gradient-to-b from-yellow-300 to-yellow-500 hover:from-yellow-400 hover:to-yellow-600
                relative
                ring-2 ring-[#A36105] ${
                  isSaving
                    ? 'bg-gray-300 cursor-not-allowed'
                    : 'bg-yellow-400 hover:bg-yellow-300'
                }`}
                  >
                    {novelDetails?.status === 'new'
                      ? isSaving
                        ? 'Submitting...'
                        : 'Submit'
                      : novelDetails?.status === 'submitted'
                      ? isSaving
                        ? 'Updating...'
                        : 'Update'
                      : novelDetails?.status === 'reviewed'
                      ? isSaving
                        ? 'Updating...'
                        : 'Update'
                      : 'Submit'}
                  </button>
                ) : (
                  <button
                    onClick={handleEditClick}
                    className={`text-black font-medium py-1 px-5 text-sm text-center rounded-full
                        border-2 transition-all duration-300 bg-gradient-to-b from-yellow-300 to-yellow-500 hover:from-yellow-400 hover:to-yellow-600 shadow-[0px_4px_6px_-1px_rgba(0,0,0,0.1),0px_2px_4px_-2px_rgba(0,0,0,0.1),-4px_8px_12px_0px_#00000026] border-yellow-100  whitespace-nowrap
                        relative ring-2 ring-[#A36105]`}
                  >
                    Edit
                  </button>
                ))}
            </span>
          </div>

          {haveCorrection && (
            <CorrectionSection
              subject={subject}
              date={date}
              id={novelDetails?.id}
            />
          )}
        </div>
      </div>

      <div className="absolute top-5 -right-10">
        <div className="w-8 h-8 cursor-pointer">
          <Tooltip
            content={'Skin'}
            color="user"
            size="lg"
            delay={100}
            className="-ml-3 "
            position="right"
          >
            <ButtonIcon
              icon={'arcticons:image-combiner'}
              innerBtnCls="h-12 w-12"
              btnIconCls="h-5 w-5"
              aria-label={'Skin'}
              onClick={() => dispatch(setIsSkinModalOpen(true))}
            />
          </Tooltip>
        </div>
      </div>

      {isSkinModalOpen && (
        <SelectSkinModal
          isOpen={isSkinModalOpen}
          onClose={() => dispatch(setIsSkinModalOpen(false))}
          onApply={(skin) =>
            handleSkinChange(
              skin,
              dispatch,
              (skin) => setSelectedSkin(skin),
              (content) => setSubject(content),
              (content) => setBody(content)
            )
          }
          currentSkinId={novelDetails?.skin?.id}
        />
      )}

      {isOpen && novelDetails?.id && (
        <HistoryModal
          isOpen={isOpen}
          onClose={() => setIsOpen(false)}
          endPoint={`/student/novel/entries/${novelDetails?.id}/history`}
          moduleKey="novel"
          mainComRefetch={refetch}
        />
      )}
    </div>
  );
};

export default WriteNovel;
