'use client';
import React, { useRef, useMemo } from 'react';
import { CSS } from '@dnd-kit/utilities';
import { useDraggable, useDroppable } from '@dnd-kit/core';
import { motion } from 'framer-motion';
import { Icon } from '@iconify/react';
import Image from 'next/image';

// Draggable option component
const DraggableOption = ({ id, option, isActive, index, totalOptions }) => {
  // Use useMemo to ensure stable positioning that doesn't change on re-renders
  const stablePosition = useMemo(() => {
    const columns = Math.min(Math.max(Math.ceil(totalOptions / 2), 3), 5); // 3-5 columns based on total options
    const col = index % columns;
    const cellWidth = 90 / columns; // Use 90% width with better spacing

    // Create a deterministic "random" offset based on the option text and index
    // This ensures the same option always gets the same offset
    const seed = option.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0) + index;
    const pseudoRandom = (seed * 9301 + 49297) % 233280 / 233280; // Simple PRNG

    // Reduce offset to prevent overlapping and ensure better spacing
    const maxOffset = cellWidth * 0.15; // Smaller offset range
    const xOffset = (pseudoRandom * 2 - 1) * maxOffset;

    // Calculate position with better spacing
    const baseX = 5 + col * cellWidth + cellWidth * 0.5; // 5% margin from left
    const x = Math.max(5, Math.min(95, baseX + xOffset)); // Ensure within bounds

    const rotation = (pseudoRandom * 6 - 3); // Reduced rotation for better readability

    return { x, rotation };
  }, [option, index, totalOptions]);

  const duration = 8; // 8s per fall for smoother animation
  const delay = index * 0.6 + (stablePosition.x / 100) * 0.3; // Staggered delay based on position and index

  const { attributes, listeners, setNodeRef, transform, isDragging } =
    useDraggable({
      id: id,
      data: { option },
    });

  const style = transform
    ? {
        transform: CSS.Translate.toString(transform),
        touchAction: 'none',
        zIndex: 1000, // Higher z-index when dragging
      }
    : {
        touchAction: 'none',
        zIndex: 10,
      };

  if (isDragging || isActive) {
    return null;
  }

  // Animate y from -50 to 250 (goes under the box, box is overflow-hidden)
  return (
    <motion.div
      initial={{
        y: -50,
        opacity: 1,
        rotate: stablePosition.rotation * 2,
      }}
      animate={{
        y: 220, // go further down so it appears to go under the box
        opacity: 1,
        rotate: stablePosition.rotation,
      }}
      transition={{
        type: 'linear',
        duration,
        delay,
        repeat: Infinity,
        repeatType: 'loop',
        repeatDelay: 0,
        ease: 'linear',
      }}
      className="absolute"
      style={{
        left: `${stablePosition.x}%`,
        position: 'absolute',
        pointerEvents: 'auto',
      }}
    >
      <div
        ref={setNodeRef}
        style={style}
        {...listeners}
        {...attributes}
        className="bg-yellow-100 border border-yellow-300 rounded-md px-2 py-1 cursor-grab shadow-sm hover:shadow-md transition-shadow text-xs sm:text-sm md:text-base whitespace-nowrap max-w-[120px] sm:max-w-[150px] md:max-w-[200px] overflow-hidden text-ellipsis"
        title={option} // Show full text on hover
      >
        {option}
      </div>
    </motion.div>
  );
};

// Droppable blank component
const DroppableBlank = ({ id, value, index, onReset, correctAnswers }) => {
  const { isOver, setNodeRef, active } = useDroppable({
    id: id,
    data: { index },
  });

  // Determine if the currently dragged option is correct for this blank
  const isCorrectOption = () => {
    if (!active || !correctAnswers) return null;

    // Extract the option index from the active id (format: "option-X")
    const draggedOptionIndex = active.id.startsWith('option-')
      ? parseInt(active.id.split('-')[1])
      : null;

    if (draggedOptionIndex === null) return null;

    // Check if the option being dragged matches the correct answer for this blank
    return correctAnswers[index] === active.data.current?.option;
  };

  // Determine border color based on validation
  const getBorderColor = () => {
    if (!isOver) {
      return value
        ? 'border-yellow-300 bg-yellow-100'
        : 'border-yellow-300 border-dashed bg-yellow-50';
    }

    const validationResult = isCorrectOption();

    // If we're dragging but can't determine correctness, use default hover style
    if (validationResult === null) {
      return 'border-yellow-500 bg-yellow-100';
    }

    // Show green for correct, red for incorrect
    return validationResult
      ? 'border-green-500 bg-green-50'
      : 'border-red-500 bg-red-50';
  };

  return (
    <div className="inline-flex items-center relative">
      <div
        ref={setNodeRef}
        className={`inline-flex items-center justify-center min-w-12 sm:min-w-16 h-6 sm:h-8 mx-0.5 sm:mx-1 border-2 ${getBorderColor()} rounded-md px-1 sm:px-2 transition-colors duration-200 text-xs sm:text-sm md:text-base`}
      >
        <span className="truncate max-w-[80px] sm:max-w-[120px]" title={value || ''}>
          {value || ''}
        </span>
      </div>

      {/* Reset button for individual blank */}
      {value && onReset && (
        <button
          onClick={() => onReset(index)}
          className="absolute -right-1 sm:-right-2 -top-1 sm:-top-2 bg-white rounded-full w-4 h-4 sm:w-5 sm:h-5 flex items-center justify-center text-gray-500 hover:text-red-500 shadow-sm border border-gray-200"
          title="Remove this answer"
        >
          <Icon icon="mdi:close" width={10} className="sm:w-3" />
        </button>
      )}
    </div>
  );
};

// Drag overlay component
const DragOverlayContent = ({ option }) => {
  return (
    <div
      className="bg-yellow-100 border-2 border-yellow-400 rounded-md px-3 py-1 shadow-xl text-sm relative pointer-events-none transform rotate-2"
      style={{
        zIndex: 10000,
        filter: 'drop-shadow(0 4px 8px rgba(0,0,0,0.2))'
      }}
    >
      {option}
    </div>
  );
};

// Feedback component
const FeedbackComponent = ({
  button,
  isCorrect,
  correctAnswer,
  showFeedback,
}) => {
  return (
    <div>
      {/* {showFeedback && isCorrect && (
        <div>
          <Image
            src={'/assets/images/all-img/successGIF.gif'}
            alt={'cat'}
            width={200}
            height={200}
            className="mx-auto"
          />
        </div>
      )} */}

      {/* <div
        className={`w-full min-h-32 bg-pink-50 p-4 rounded-lg border-pink-200 flex items-center ${
          showFeedback ? 'justify-between' : 'justify-center'
        } [box-shadow:-2px_-2px_8px_0px_#B7212626_inset] [box-shadow:2px_2px_12px_0px_#A92E0024_inset]`}
      >
        {showFeedback && (
          <div className="flex items-center">
            <div className="mr-4">
              <div className="w-28 h-28 bg-yellow-200 rounded-full flex items-center justify-center">
                <Image
                  src={
                    isCorrect
                      ? '/assets/images/all-img/correctImg.png'
                      : '/assets/images/all-img/wrongImg.png'
                  }
                  alt={'cat'}
                  width={200}
                  height={200}
                />
              </div>
            </div>
            <div>
              {isCorrect ? (
                <p className="text-green-600 text-2xl font-medium">
                  Great job! That's correct!
                </p>
              ) : (
                <div>
                  <p className="text-orange-500 font-medium">
                    Practice makes you perfect.
                  </p>
                  <p className="text-gray-700 mt-1">
                    <span className="font-medium">Correct Answer: </span>
                    <span dangerouslySetInnerHTML={{__html: correctAnswer}} />
                  </p>
                </div>
              )}
            </div>
          </div>
        )}
        {button}
      </div> */}
    </div>
  );
};

export {
  DraggableOption,
  DroppableBlank,
  DragOverlayContent,
  FeedbackComponent,
};
