'use client';

import { useEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import io from 'socket.io-client';
import Cookies from 'js-cookie';
import { API_BASE_URL } from '@/lib/config';
import { handleTokenExpiration } from '@/lib/authUtils';
import {
  setIsConnected,
  addMessage,
  updateContact,
  setRemoteTyping,
  setUserTyping,
  clearExpiredTypingUsers,
  markMessagesAsDelivered,
  markMessagesAsRead,
  replaceOptimisticMessage,
  incrementUnreadCount,
  clearUnreadCount,
  selectConversationId,
  selectMe,
  markMessagesAsSent,
} from '@/store/features/chatSlice';

const SOCKET_URL = `${API_BASE_URL}/chat`;

export const useChatSocket = () => {
  const dispatch = useDispatch();
  const socket = useRef(null);
  const conversationId = useSelector(selectConversationId);
  const me = useSelector(selectMe);
  const { isAdmin } = useSelector((state) => state?.auth);
  // const isAdmin = user?.type === 'admin';

  // Refs to avoid stale closures in socket callbacks
  const conversationIdRef = useRef(null);
  const meRef = useRef(null);

  const token = Cookies.get('token') || '';

  // Update refs when values change
  useEffect(() => {
    conversationIdRef.current = conversationId;
  }, [conversationId]);

  useEffect(() => {
    meRef.current = me;
  }, [me]);

  // Initialize socket connection
  useEffect(() => {
    if (!token) return;

    socket.current = io(SOCKET_URL, {
      auth: { token },
      query: { token },
      extraHeaders: { Authorization: `Bearer ${token}` },
    });

    // Connection events
    socket.current.on('connect', () => {
      dispatch(setIsConnected(true));
    });

    // Typing events
    let typingTimeout = null;
    socket.current.on('typing_indicator', (data) => {
      // Only process typing events for the current conversation and from other users
      if (data) {
        dispatch(setRemoteTyping(true));
        dispatch(
          setUserTyping({
            userId: data.userId,
            conversationId: data.conversationId,
            userName: data.userName,
            isTyping: data.isTyping,
          })
        );

        // Reset the timeout every time a new event is received
        clearTimeout(typingTimeout);
        typingTimeout = setTimeout(() => {
          dispatch(setRemoteTyping(false));
          dispatch(setUserTyping({}));
        }, 500);
      }
    });
    socket.current.on('disconnect', () => {
      dispatch(setIsConnected(false));
    });

    socket.current.on('connect_error', (error) => {
      console.error('Chat socket connection error:', error);

      // Check if the error is due to authentication failure
      if (
        error?.message?.includes('Authentication') ||
        error?.message?.includes('Unauthorized') ||
        error?.message?.includes('401')
      ) {
        console.log('Chat socket authentication failed - token may be expired');
        handleTokenExpiration();
      }
    });

    // Message events
    socket.current.on('new_message', (message) => {
      if (message) {
        dispatch(
          markMessagesAsSent({
            conversationId: message.conversationId,
            userId: meRef.current?.id,
          })
        );
      }

      // Update contact's last message
      dispatch(
        updateContact({
          conversationId: message.conversationId,
          lastMessage: message.content,
          lastMessageTime: message.createdAt,
        })
      );

      // Get current conversation ID and user from refs to avoid stale closure
      const currentConversationId = conversationIdRef.current;
      const currentMe = meRef.current;

      // Add message to current conversation if it matches
      if (message.conversationId === currentConversationId) {
        // Only add messages from other users - our own messages are handled via optimistic updates
        if (message.senderId !== currentMe?.id) {
          const mappedMessage = mapServerMessage(message);
          dispatch(addMessage(mappedMessage));
        } else if (isAdmin && message?.isSenderVirtualAdmin) {
          const mappedMessage = mapServerMessage(message);
          dispatch(addMessage(mappedMessage));
        }
      } else {
        // Increment unread count for other conversations
        dispatch(
          incrementUnreadCount({
            conversationId: message.conversationId,
            messageId: message.id,
          })
        );
      }
    });

    // Message status events
    socket.current.on('messages_delivered', (data) => {
      const currentConversationId = conversationIdRef.current;
      const currentMe = meRef.current;

      if (data.conversationId === currentConversationId) {
        dispatch(
          markMessagesAsDelivered({
            conversationId: data.conversationId,
            userId: currentMe?.id,
          })
        );
      }
    });

    socket.current.on('messages_read', (data) => {
      const currentConversationId = conversationIdRef.current;
      const currentMe = meRef.current;

      if (data.conversationId === currentConversationId) {
        dispatch(
          markMessagesAsRead({
            conversationId: data.conversationId,
            userId: currentMe?.id,
          })
        );
      }
    });

    return () => {
      if (socket.current) {
        socket.current.disconnect();
        dispatch(setIsConnected(false));
      }
    };
  }, [token, dispatch]);

  // Subscribe to conversation when it changes
  useEffect(() => {
    if (socket.current && conversationId) {
      socket.current.emit('subscribe_conversation', {
        conversationId,
      });
    }
  }, [conversationId]);

  // Helper function to map server message format
  const mapServerMessage = (serverMessage) => {
    return {
      id: serverMessage.id,
      conversationId: serverMessage.conversationId,
      content: serverMessage.content,
      senderId: serverMessage.senderId, // Ensure senderId is always set
      sender: { id: serverMessage.senderId },
      createdAt: new Date(serverMessage.createdAt || Date.now()),
      isSenderVirtualAdmin: serverMessage.isSenderVirtualAdmin,
      status: serverMessage.status || 'sent',
      attachments: serverMessage.attachments || [],
      attachmentIds: serverMessage.attachmentIds || [],
    };
  };

  // Socket methods
  const sendMessage = (messageData, callback) => {
    if (socket.current) {
      socket.current.emit('send_message', messageData, (ack) => {
        if (callback) callback(ack);
      });
    }
  };

  const sendTyping = (isTyping) => {
    if (socket.current && conversationId) {
      socket.current.emit('typing', {
        conversationId,
        isTyping,
      });
    }
  };

  const subscribeToConversation = (convId) => {
    if (socket.current && convId) {
      socket.current.emit('subscribe_conversation', {
        conversationId: convId,
      });
    }
  };

  return {
    socket: socket.current,
    sendMessage,
    sendTyping,
    subscribeToConversation,
    mapServerMessage,
  };
};
