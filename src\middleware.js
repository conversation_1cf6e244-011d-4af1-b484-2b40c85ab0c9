import { NextResponse } from 'next/server';

/**
 * Middleware for role-based access control
 *
 * Access Rules:
 * - Students: Can access (auth) and (main) folders only
 * - Tutors/Admins: Can access (auth) and dashboard folders only
 * - Root route (/): Redirects based on role
 * - Auth routes: Public for all users
 */
export function middleware(request) {
  const token = request.cookies.get('token')?.value;
  const userRole = request.cookies.get('userRole')?.value;
  const pathname = request.nextUrl.pathname;

  // Debug logging
  console.log('🔍 Middleware Debug:', {
    pathname,
    token: token ? 'exists' : 'missing',
    userRole,
    isDashboard: pathname.startsWith('/dashboard'),
  });

  const publicRoutes = ['/contactus', '/aboutus', '/'];

  if (publicRoutes.includes(pathname)) {
    return NextResponse.next();
  }

  // Define route patterns
  const isAuthRoute =
    pathname.startsWith('/login') ||
    pathname.startsWith('/register') ||
    pathname.startsWith('/forgot-password') ||
    pathname.startsWith('/forgot-userid') ||
    pathname.startsWith('/reset-password') ||
    pathname.startsWith('/verify-email');

  const isMainRoute =
    pathname.startsWith('/diary') ||
    pathname.startsWith('/essay') ||
    pathname.startsWith('/novel') ||
    pathname.startsWith('/chat') ||
    pathname.startsWith('/friends') ||
    pathname.startsWith('/find-friends') ||
    pathname.startsWith('/invite-friends') ||
    pathname.startsWith('/profile') ||
    pathname.startsWith('/pricing-plans') ||
    pathname.startsWith('/aboutus') ||
    pathname.startsWith('/contactus') ||
    pathname.startsWith('/select-skin') ||
    pathname.startsWith('/skin-preview') ||
    pathname.startsWith('/block') ||
    pathname.startsWith('/waterfall') ||
    pathname.startsWith('/story-maker') ||
    pathname.startsWith('/qa') ||
    pathname.startsWith('/hec-mission') ||
    pathname.startsWith('/question-answer') ||
    pathname.startsWith('/tutors') ||
    pathname.startsWith('/diary-missions');

  const isDashboardRoute = pathname.startsWith('/dashboard');
  const isRootRoute = pathname === '/';
  const isProfileRoute = pathname.startsWith('/profile');

  // Handle authenticated users accessing auth pages
  if (isAuthRoute && token && userRole) {
    // Redirect authenticated users away from auth pages based on their role
    if (userRole === 'student') {
      return NextResponse.redirect(new URL('/diary/shared', request.url));
    } else if (userRole === 'tutor' || userRole === 'admin') {
      return NextResponse.redirect(new URL('/dashboard', request.url));
    }
  }

  // Handle root route - redirect all authenticated users based on role
  if (isRootRoute && token && userRole) {
    if (userRole === 'student') {
      return NextResponse.redirect(new URL('/diary/shared', request.url));
    } else if (userRole === 'tutor' || userRole === 'admin') {
      return NextResponse.redirect(new URL('/dashboard', request.url));
    }
  }

  // Handle unauthenticated users accessing protected routes
  if (!token && (isMainRoute || isDashboardRoute)) {
    const loginUrl = new URL('/login', request.url);
    loginUrl.searchParams.set('returnTo', pathname);
    return NextResponse.redirect(loginUrl);
  }

  // Role-based access control for authenticated users
  if (token && userRole) {
    console.log('🔐 Role-based access control:', {
      userRole,
      isDashboardRoute,
      isMainRoute,
      shouldBlockStudent: userRole === 'student' && isDashboardRoute,
      shouldBlockTutorAdmin:
        (userRole === 'tutor' || userRole === 'admin') && isMainRoute,
    });

    // Students: Block access to dashboard routes
    if (userRole === 'student' && isDashboardRoute) {
      console.log('🚫 Blocking student from dashboard');
      return NextResponse.redirect(new URL('/unauthorized', request.url));
    }

    // Tutors and Admins: Block access to main routes (except profile)
    if (
      (userRole === 'tutor' || userRole === 'admin') &&
      isMainRoute &&
      !isProfileRoute
    ) {
      console.log('🚫 Blocking tutor/admin from main routes');
      return NextResponse.redirect(new URL('/unauthorized', request.url));
    }
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    // Root route
    '/',

    // Auth routes
    '/login',
    '/register',
    '/forgot-password/:path*',
    '/forgot-userid/:path*',
    '/reset-password/:path*',
    '/verify-email/:path*',

    // Main routes (student area)
    '/diary/:path*',
    '/essay/:path*',
    '/novel/:path*',
    '/chat/:path*',
    '/friends/:path*',
    '/find-friends/:path*',
    '/invite-friends/:path*',
    '/profile/:path*',
    '/pricing-plans/:path*',
    '/aboutus/:path*',
    '/contactus/:path*',
    '/select-skin/:path*',
    '/skin-preview/:path*',
    '/block/:path*',
    '/waterfall/:path*',
    '/story-maker/:path*',
    '/qa/:path*',
    '/hec-mission/:path*',
    '/question-answer/:path*',
    '/tutors/:path*',
    '/diary-missions/:path*',

    // Dashboard routes (tutor/admin area)
    '/dashboard/:path*',

    // Utility routes
    '/unauthorized',
  ],
};
