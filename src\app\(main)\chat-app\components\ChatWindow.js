'use client';

import React from 'react';
import Image from 'next/image';
import { useDispatch, useSelector } from 'react-redux';
import {
  selectActiveContact,
  selectConversationId,
  selectRemoteTyping,
  setActiveContact,
  setConversationId,
  setMessages,
} from '@/store/features/chatSlice';
import MessageList from './MessageList';
import MessageInput from './MessageInput';
import EmptyChat from './EmptyChat';
import { Icon } from '@iconify/react';
import { useRouter } from 'next/navigation';

const ChatWindow = ({
  onSendMessage,
  onFileSelect,
  onTyping,
  showChat,
  setShowChat,
  isMobile,
}) => {
  const router = useRouter();
  const dispatch = useDispatch();
  const activeContact = useSelector(selectActiveContact);
  const conversationId = useSelector(selectConversationId);
  const remoteTyping = useSelector(selectRemoteTyping);


  const handleGoBack = () => {
    setShowChat(false);
    dispatch(setActiveContact(null));
    dispatch(setConversationId(null));
    dispatch(setMessages([]));
    router.push('/chat-app');
  };

  // Show empty chat if no conversation is selected
  if (!activeContact && !conversationId && !isMobile) {
    return <EmptyChat showChat={showChat} />;
  }

  return showChat && (
    <section
      className={`
        flex-1 flex flex-col bg-white w-full
        ${showChat ? 'flex' : 'hidden sm:flex'}
      `}
    >
      {/* Chat Header */}
      <div className="h-[70px] flex items-center px-4 sm:px-5 border-b border-gray-200 bg-white shadow-sm">
        <div className="flex-1 flex items-center gap-2 sm:gap-3">
          <button
            onClick={() => handleGoBack()}
            className="sm:hidden" // Only show on mobile
          >
            <Icon
              icon="eva:arrow-back-fill"
              className="w-5 h-5 group-hover:text-yellow-500"
            />
          </button>
          <div className="w-8 h-8 rounded-full overflow-hidden">
            <div
              className="w-full h-full bg-cover bg-center bg-no-repeat relative"
              style={{
                backgroundImage: `url(${
                  activeContact?.profilePicture ||
                  '/assets/images/all-img/avatar.png'
                })`,
              }}
            >
              <Image
                src={
                  activeContact?.profilePicture ||
                  '/assets/images/all-img/avatar.png'
                }
                alt={activeContact?.name || 'contact info'}
                width={32}
                height={32}
                className="object-cover absolute top-0 left-0 opacity-0"
                onError={(e) => {
                  e.target.style.opacity = 0;
                  e.target.parentElement.style.backgroundImage = `url('/assets/images/all-img/avatar.png')`;
                }}
              />
            </div>
          </div>
          <div>
            <h2 className="text-base font-semibold text-gray-900 m-0">
              {activeContact?.name}
            </h2>
            {activeContact?.email && (
              <p className="text-xs text-gray-500 m-0 mt-0.5">
                {activeContact?.email}
              </p>
            )}
          </div>
        </div>

        {/* Optional: Add status indicator or actions */}
        <div className="flex items-center gap-2">
          {/* Online status indicator */}
          <div
            className={`w-2 h-2 rounded-full ${
              activeContact?.isOnline ? 'bg-green-500' : 'bg-gray-300'
            }`}
            title={activeContact?.isOnline ? 'Online' : 'Offline'}
          />

          {/* Optional: More actions button */}
          <button
            className="bg-transparent border-none cursor-pointer p-1 rounded text-gray-500 text-base hover:bg-gray-100"
            title="More options"
          >
            ⋮
          </button>
        </div>
      </div>

      {/* Messages Area */}
      <MessageList />

      {/* Message Input */}
      <MessageInput
        onSendMessage={onSendMessage}
        onFileSelect={onFileSelect}
        onTyping={onTyping}
      />
    </section>
  );
};

export default ChatWindow;
