'use client';

import React from 'react';
import { useRouter, usePathname, useSearchParams } from 'next/navigation';
import { Icon } from '@iconify/react';

const HecNovelLayout = ({ children, activeTab }) => {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const handleTabChange = (tab) => {
    // If we're already on a diary review page, navigate back to the main diary page
    if (pathname.includes('/review/')) {
      const params = new URLSearchParams();
      params.set('tab', tab);
      router.push(`/dashboard/submission-management/hec-novel?${params.toString()}`);
    } else {
      // Otherwise, just update the tab parameter
      const params = new URLSearchParams(searchParams);
      params.set('tab', tab);
      router.push(`${pathname}?${params.toString()}`);
    }
  };

  const handleBack = () => {
    router.push('/dashboard/submission-management/hec-novel');
  };

  return (
    <div className="flex flex-col h-full">
      {/* Header with Back Button */}
      <div className="flex items-center mb-6">
        <button
          onClick={handleBack}
          className="flex items-center text-gray-600 hover:text-yellow-600 mr-4"
        >
          <Icon icon="eva:arrow-back-fill" className="w-6 h-6" />
        </button>
        <h1 className="text-2xl font-semibold">HEC Novel </h1>
      </div>

      {/* Main Content Area with Sidebar and Content */}
      <div className="flex flex-1">
        {/* Sidebar */}
        <div className="w-64 bg-[#FEFCE8] p-4 flex flex-col rounded-lg border border-[#FFDE34]">
          <button
            className={`mb-2 p-2 text-left rounded-md ${activeTab === 'NovelSubmissionList' ? 'bg-[#FFDE34] text-black' : 'hover:bg-gray-200'}`}
            onClick={() => handleTabChange('NovelSubmissionList')}
          >
           Novel Submission List
          </button>
        </div>

        {/* Content Area */}
        <div className="flex-1 px-6">
          {children}
        </div>
      </div>
    </div>
  );
};

export default HecNovelLayout;