'use client';
import React from 'react';

const ShapeRenderer = ({ item, size }) => {
  const renderShape = () => {
    switch (item.shapeType) {
      case 'circle':
        return <div className="w-full h-full bg-blue-500 rounded-full opacity-70"></div>;
      
      case 'square':
        return <div className="w-full h-full bg-green-500 opacity-70"></div>;
      
      case 'triangle':
        return (
          <div 
            className="w-full h-full opacity-70 flex items-end justify-center"
          >
            <div 
              style={{
                width: 0,
                height: 0,
                borderLeft: `${size.width/2}px solid transparent`,
                borderRight: `${size.width/2}px solid transparent`,
                borderBottom: `${size.height}px solid #f59e0b`,
              }}
            ></div>
          </div>
        );
      
      case 'star':
        return (
          <div 
            className="w-full h-full bg-yellow-500 opacity-70" 
            style={{ 
              clipPath: 'polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%)' 
            }}
          ></div>
        );
      
      case 'heart':
        return (
          <div 
            className="w-full h-full bg-red-500 opacity-70" 
            style={{ 
              clipPath: 'path("M12,21.35l-1.45-1.32C5.4,15.36,2,12.28,2,8.5 C2,5.42,4.42,3,7.5,3c1.74,0,3.41,0.81,4.5,2.09C13.09,3.81,14.76,3,16.5,3 C19.58,3,22,5.42,22,8.5c0,3.78-3.4,6.86-8.55,11.54L12,21.35z")' 
            }}
          ></div>
        );
      
      case 'arrow':
        return (
          <div 
            className="w-full h-full bg-purple-500 opacity-70" 
            style={{ 
              clipPath: 'polygon(0% 20%, 60% 20%, 60% 0%, 100% 50%, 60% 100%, 60% 80%, 0% 80%)' 
            }}
          ></div>
        );
      
      default:
        return <div className="w-full h-full bg-gray-500 opacity-70"></div>;
    }
  };

  return (
    <div className="w-full h-full">
      {renderShape()}
    </div>
  );
};

export default ShapeRenderer;