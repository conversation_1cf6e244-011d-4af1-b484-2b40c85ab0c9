'use client';
import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import useDataFetch from '@/hooks/useDataFetch';
import { toast } from 'sonner';
import { getUserRoleFromCookie } from '@/lib/auth';
import AdminProfile from './_components/AdminProfile';
import TutorProfile from './_components/TutorProfile';
import StudentProfile from './_components/StudentProfile';

const ProfileDetails = () => {
  const { user } = useSelector((state) => state.auth);
  const userId = user?.id;
  const [profileData, setProfileData] = useState(null);

  // Fetch profile data
  const {
    data: userDetails,
    isLoading,
    error,
  } = useDataFetch({
    queryKey: ['Profile-details', userId],
    endPoint: '/users/profile',
    // enabled: !!userId && !!userRole,
  });

  // Show loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-amber-500"></div>
      </div>
    );
  }

  // Render the appropriate profile component based on user role
  const renderProfileComponent = () => {
    if (userRole === 'admin') {
      return <AdminProfile profileData={profileData} />;
    } else if (userRole === 'tutor') {
      return <TutorProfile profileData={profileData} />;
    } else {
      return <StudentProfile profileData={profileData} />;
    }
  };

  return <StudentProfile profileData={userDetails} />;
};

export default ProfileDetails;
