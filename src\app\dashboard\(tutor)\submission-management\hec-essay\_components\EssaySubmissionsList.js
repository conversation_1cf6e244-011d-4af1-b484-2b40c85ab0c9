'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import NewTablePage from '@/components/form/NewTablePage'; // Assuming this component is correctly implemented
import useDataFetch from '@/hooks/useDataFetch'; // Assuming this hook is correctly implemented

const EssaySubmissionsList = () => {
  const router = useRouter();

  // State variables
  const [searchTerm, setSearchTerm] = useState('');
  const [searchField, setSearchField] = useState('userName'); // Initialized to 'userName' for consistency
  const [sortField, setSortField] = useState('submittedAt'); // Default sort field
  const [sortDirection, setSortDirection] = useState('DESC'); // Default sort direction
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  // Prepare API parameters, including search if searchTerm is present
  const apiParams = {
    page: currentPage,
    limit: rowsPerPage,
    sortBy: sortField,
    sortDirection: sortDirection,
  };

  if (searchTerm && searchField) {
    // This assumes your API takes the search field name as a dynamic query parameter key
    // e.g., if searchField is 'userName', it adds 'userName=searchTermValue' to the query
    // Adjust if your API expects different query param names for search (e.g., filterField, filterValue)
    apiParams[searchField] = searchTerm;
  }

  // Fetch essay submissions using useDataFetch hook
  const {
    data: essayData,
    isLoading: loading,
    error,
  } = useDataFetch({
    queryKey: [
      'tutor-essay-submissions',
      currentPage,
      rowsPerPage,
      sortField,
      sortDirection,
      searchTerm,
      searchField,
    ],
    endPoint: '/tutor-essay/list',
    params: apiParams, // Use the apiParams that includes search parameters
  });

  // Log any errors
  if (error) {
    console.error('Error fetching essay data:', error);
  }

  // Extract data from the API response
  const submissions = essayData?.items || essayData?.data || [];
  const totalItems = essayData?.totalItems || essayData?.data?.totalItems || essayData?.total || submissions.length;
  const totalPages = essayData?.totalPages || essayData?.data?.totalPages || Math.ceil(totalItems / rowsPerPage);

  // Debug: Log pagination values
  // console.log('Pagination Debug:', {
  //   totalItems,
  //   totalPages,
  //   currentPage,
  //   rowsPerPage,
  //   submissionsLength: submissions.length
  // });

  // Process submissions to match the required format
  const processedSubmissions = submissions.map((submission) => {
    // Get the latest submission from history (assuming submissionHistory is sorted, latest first)
    const latestSubmission =
      submission.submissionHistory && submission.submissionHistory.length > 0
        ? submission.submissionHistory[0]
        : null;

    return {
      id: submission.id,
      title: submission?.title,
      userName: latestSubmission?.createdBy || 'Unknown User', // Displayed as 'USER NAME'
      status: submission.status,
      studentAttempt: submission?.submissionHistory[0]?.metaData?.submissionAttempts || 0,
      submissionDate: latestSubmission?.submissionDate || submission.createdAt, // Fallback to main creation date
      wordCount: latestSubmission?.wordCount || 0,
      content: latestSubmission?.content || '', // Used for viewing the submission
      isReviewed: submission.status === 'reviewed',
    };
  });

  // Handle page change
  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  // Handle search term change
  const handleSearch = (term) => {
    setSearchTerm(term);
    setCurrentPage(1); // Reset to first page on new search
  };

  // Handle search field change (e.g., if more search options were available)
  const handleSearchFieldChange = (field) => {
    setSearchField(field);
    setCurrentPage(1); // Reset to first page on new search field
  };

  // Handle sort change
  const handleSort = (field, direction) => {
    setSortField(field);
    setSortDirection(direction);
    setCurrentPage(1); // Optionally reset to first page on sort change
  };

  // Handle view submission
  const handleViewSubmission = (submission) => {
    router.push(
      `/dashboard/submission-management/hec-essay/review/${submission.id}?tab=essaySubmissions`
    );
  };

  // Define table columns
  const columns = [
    {
      field: 'userName',
      label: 'USER NAME',
      sortable: false, // Assuming API supports sorting by userName (or the field it maps to)
      cellRenderer: (value) => (
        <div className="flex items-center">
          <div className="w-8 h-8 rounded-full bg-yellow-200 flex items-center justify-center mr-2 text-xs">
            {typeof value === 'string'
              ? value
                  .split(' ')
                  .map((n) => n[0])
                  .join('')
              : 'N/A'}
          </div>
          <span>{value}</span>
        </div>
      ),
    },
    {
      field: 'status', // This field itself from processedSubmissions is 'reviewed', 'pending' etc.
      label: 'REVIEW STATUS',
      sortable: false, // Assuming API supports sorting by status
      cellRenderer: (_, row) => {
        // row is an item from processedSubmissions
        return (
          <div className="flex items-center">
            {row.isReviewed ? (
              <div className="bg-green-100 text-green-800 px-3 py-1 rounded-md text-sm">
                Reviewed <span className="ml-1">✓</span>
              </div>
            ) : (
              <div className="bg-yellow-50 text-yellow-800 px-3 py-1 rounded-md text-sm flex items-center">
                Not Reviewed Yet <span className="ml-1">⏱</span>
              </div>
            )}
          </div>
        );
      },
    },
    
    { label: 'SUBMISSION TITLE', field: 'title' },
    // You might want to add a submissionDate column as well, if relevant for display
    // {
    //   field: 'submissionDate',
    //   label: 'SUBMISSION DATE',
    //   sortable: true,
    //   cellRenderer: (value) => (
    //     <div className="text-center">
    //       {new Date(value).toLocaleDateString()} {/* Example formatting */}
    //     </div>
    //   )
    // }
  ];

  // Define actions for table rows
  const actions = [
    {
      icon: 'heroicons-outline:eye', // Ensure you have an Icon component that handles this string
      className:
        'text-blue-600 hover:text-blue-700 bg-blue-50 p-2 rounded-md cursor-pointer',
      onClick: handleViewSubmission,
    },
  ];

  return (
    <div className="bg-white p-6 rounded-lg shadow">
      {/* Show loading or error states */}
      {error && (
        <div className="mb-4 p-4 bg-red-100 text-red-700 rounded-md">
          Error loading data: {error.message}
        </div>
      )}

      {/* Table */}
      <NewTablePage
        columns={columns}
        data={processedSubmissions}
        actions={actions}
        loading={loading}
        title="Essay Submissions"
        showCheckboxes={false}
        // Pagination props
        currentPage={currentPage}
        totalPages={totalPages}
        changePage={handlePageChange}
        totalItems={Math.max(totalItems, processedSubmissions.length)} // Ensure totalItems is at least the number of submissions
        rowsPerPage={rowsPerPage}
        setRowsPerPage={(value) => {
          setRowsPerPage(value);
          setCurrentPage(1); // Reset to page 1 when rows per page changes
        }}
        // Search and filter props
        showSearch={false}
        showNameFilter={false} // This controls visibility of the dropdown for searchField selection
        showSortFilter={false} // As per original code
        // Pass current state values for search and sort
        searchTerm={searchTerm}
        searchField={searchField} // Now defaults to 'userName'
        sortField={sortField}
        sortDirection={sortDirection}
        // Pass handlers for search, filter and sort
        onSearch={handleSearch}
        onNameFilterChange={handleSearchFieldChange}
        onSort={handleSort}
        // Name filter options for the dropdown
        // The comment "Search is now focused on userName only" implies this might be fixed.
        // If truly fixed, showNameFilter could be false and searchField hardcoded.
        nameFilterOptions={[
          { label: 'User Name', value: 'userName' },
          // Add other searchable fields if API supports them, e.g.:
          // { label: 'Status', value: 'status' }
        ]}
      />
    </div>
  );
};

export default EssaySubmissionsList;
