'use client';
import GoBack from '@/components/shared/GoBack';
import FriendList from './_components/FriendList';
import { useDispatch, useSelector } from 'react-redux';
import { setFriendActiveTab } from '@/store/features/commonSlice';
import RequestList from './_components/RequestList';
import SearchFriend from './_components/SearchFriend';
import { Icon } from '@iconify/react';
import useDataFetch from '@/hooks/useDataFetch';

const Friends = () => {
  const { friendActiveTab: activeTab } = useSelector((state) => state.common);

  const {
    data: requests,
    isLoading: isLoadingRequests,
    refetch: refetchRequests,
  } = useDataFetch({
    queryKey: ['friend-requests'],
    endPoint: '/student/friends/pending',
  });
  const friendRequests = requests?.incomingRequests || [];

  const dispatch = useDispatch();

  return (
    <div className="max-w-7xl mx-auto px-5 xl:px-0 py-5">
      <GoBack title="Friends" linkClass=" w-full max-w-40" />

      <div className="shadow-lg border border-sky-200 max-sm:p-5 p-10 mt-5 rounded-lg h-full">
        <div className="max-w-4xl mx-auto">
          {/* Tabs */}
          <div className="flex space-x-2 bg-white rounded-lg shadow-sm p-3 border mb-6">
            <button
              className={`px-4 py-2 rounded-lg ${
                activeTab === 'friends'
                  ? 'bg-yellow-100 text-yellow-800'
                  : 'text-gray-700'
              }`}
              onClick={() => dispatch(setFriendActiveTab('friends'))}
            >
              <Icon icon="formkit:people" width="22" height="22" />
            </button>
            <button
              className={`px-4 py-2 rounded-lg relative ${
                activeTab === 'requests'
                  ? 'bg-yellow-100 text-yellow-800'
                  : 'text-gray-700'
              }`}
              onClick={() => dispatch(setFriendActiveTab('requests'))}
            >
              <Icon
                icon="icon-park-outline:people-plus"
                width="22"
                height="22"
              />
              {friendRequests?.length > 0 && (
                <span className="absolute top-0 right-1 bg-yellow-300 px-1.5 rounded-full text-xs">
                  {friendRequests?.length > 9
                    ? '9+'
                    : friendRequests?.length || 0}
                </span>
              )}
            </button>
            <button
              className={`px-4 py-2 rounded-lg ${
                activeTab === 'search'
                  ? 'bg-yellow-100 text-yellow-800'
                  : 'text-gray-700'
              }`}
              onClick={() => dispatch(setFriendActiveTab('search'))}
            >
              <Icon icon="mingcute:user-search-line" width="22" height="22" />
            </button>
          </div>

          {/* Tab Content */}
          {activeTab === 'friends' && <FriendList />}

          {activeTab === 'requests' && (
            <RequestList
              friendRequests={friendRequests}
              isLoadingRequests={isLoadingRequests}
              refetchRequests={refetchRequests}
            />
          )}

          {activeTab === 'search' && <SearchFriend />}
        </div>
      </div>
    </div>
  );
};

export default Friends;
