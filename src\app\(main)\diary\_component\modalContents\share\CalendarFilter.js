import React, { useState, useCallback, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import FullCalendar from '@fullcalendar/react';
import dayGridPlugin from '@fullcalendar/daygrid';
import interactionPlugin from '@fullcalendar/interaction';
import { format } from 'date-fns';
import { Icon } from '@iconify/react';
import api from '@/lib/api';
import {
  setTodayEntry,
  setSubject,
  setMessage,
  setDate,
  setSelectedStage,
} from '@/store/features/diarySlice';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import useDataFetch from '@/hooks/useDataFetch';

const CalendarFilter = ({ onDateSelect, onClose }) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const { user } = useSelector((state) => state.auth);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedDate, setSelectedDate] = useState(null);
  const [attendanceData, setAttendanceData] = useState([]);
  const [calendarEvents, setCalendarEvents] = useState([]);
  const [searchDate, setSearchDate] = useState(null); // For triggering diary search

  // Fetch attendance data
  const {
    data: availableDiaries,
    isLoading: isDiariesLoading,
    error: diariesError,
  } = useDataFetch({
    queryKey: ['diary-attendance-student'],
    endPoint: '/diary-attendance/student',
  });

  // Fetch diary entry by date (only when searchDate is set)
  const {
    data: diarySearchResult,
    isLoading: isDiarySearchLoading,
    error: diarySearchError,
  } = useDataFetch({
    queryKey: ['diary-search', searchDate],
    endPoint: `/diary/entries/search`,
    method: 'GET',
    params: { date: searchDate },
    enabled: !!searchDate, // Only fetch when searchDate is set
  });

  // Process attendance data when it arrives
  useEffect(() => {
    if (availableDiaries) {
      setAttendanceData(availableDiaries);

      // Create calendar events from attendance data
      const events = availableDiaries.map((attendance) => {
        const title =
          attendance.diaryEntry?.title || attendance.diaryTitle || '';
        const displayTitle =
          !title || title === 'N/A' || title.trim() === '' ? 'N/A' : title;

        return {
          id: attendance.id,
          title: displayTitle,
          date: attendance.entryDate,
          backgroundColor:
            attendance.status === 'present' ? '#10b981' : '#f59e0b',
          borderColor: attendance.status === 'present' ? '#059669' : '#d97706',
          textColor: '#ffffff',
          classNames: ['diary-event'],
          extendedProps: {
            attendance: attendance,
            diaryEntry: attendance.diaryEntry,
            status: attendance.status,
            wordCount: attendance.wordCount,
            progress: attendance.progress,
          },
        };
      });

      // console.log('Calendar events created:', events);
      setCalendarEvents(events);
    }
  }, [availableDiaries]);

  // Handle diary search result
  useEffect(() => {
    if (diarySearchResult && searchDate) {
      const formattedDisplayDate = format(new Date(searchDate), 'dd MMM yyyy');
      dispatch(setDate(formattedDisplayDate));

      if (diarySearchResult.items?.length > 0) {
        const diaryEntry = diarySearchResult.items[0];

        // Update the diary state with the selected date's entry
        dispatch(setTodayEntry(diaryEntry));
        dispatch(setSubject(diaryEntry.title || ''));
        dispatch(setMessage(diaryEntry.content || ''));

        router.push(`/diary/my/item?entryId=${diaryEntry.id}`);
        toast.success(`Diary entry for ${formattedDisplayDate} loaded`);
      } else {
        // No entry found for this date, clear the current entry
        dispatch(setTodayEntry(null));
        dispatch(setSubject(''));
        dispatch(setMessage(''));
        dispatch(setSelectedStage(null));

        router.push(`/diary/my/item`);
        toast.info(`No diary entry found for ${formattedDisplayDate}`);
      }

      // Close the calendar after selection
      if (onClose) {
        onClose();
      }

      // Reset search date to prevent unwanted refetches
      setSearchDate(null);
    }
  }, [diarySearchResult, searchDate, dispatch, router, onClose]);

  // Handle diary search error
  useEffect(() => {
    if (diarySearchError && searchDate) {
      console.error('Error fetching diary entry by date:', diarySearchError);
      toast.error('Failed to load diary entry');
      setSearchDate(null);
    }
  }, [diarySearchError, searchDate]);

  // Handle loading state for diary search
  useEffect(() => {
    setIsLoading(isDiarySearchLoading);
  }, [isDiarySearchLoading]);

  // Fetch diary entry for a specific date (now just triggers the search)
  const fetchDiaryByDate = useCallback((date) => {
    const formattedDate = format(new Date(date), 'yyyy-MM-dd');
    setSearchDate(formattedDate);
  }, []);

  // Handle event click (when clicking on diary entries)
  const handleEventClick = useCallback(
    (clickInfo) => {
      const attendance = clickInfo.event.extendedProps.attendance;
      const diaryEntry = clickInfo.event.extendedProps.diaryEntry;

      if (diaryEntry && diaryEntry.id) {
        setSelectedDate(attendance.entryDate);

        // Update the diary state with the selected entry
        dispatch(setTodayEntry(diaryEntry));
        dispatch(setSubject(diaryEntry.title || ''));
        dispatch(setMessage(diaryEntry.content || ''));
        console.log('changing to date', attendance.entryDate);
        dispatch(
          setDate(format(new Date(attendance.entryDate), 'dd MMM yyyy'))
        );

        router.push(`/diary/my/item?entryId=${diaryEntry.id}`);
        toast.success(
          `Diary entry for ${format(
            new Date(attendance.entryDate),
            'dd MMM yyyy'
          )} loaded`
        );

        // Close the calendar after selection
        if (onClose) {
          onClose();
        }
      }
    },
    [dispatch, router, onClose]
  );

  // Handle date click (when clicking on empty dates)
  const handleDateClick = useCallback(
    (arg) => {
      const clickedDate = new Date(arg.dateStr);
      const today = new Date();
      today.setHours(23, 59, 59, 999); // Set to end of today
      const accountCreationDate = new Date(user?.createdAt);

      // Allow selection of past dates and today
      if (clickedDate <= today && clickedDate >= accountCreationDate) {
        setSelectedDate(arg.dateStr);
        fetchDiaryByDate(arg.dateStr);
      } else {
        toast.warning('You can only select dates from your account start date up to today');
      }
    },
    [fetchDiaryByDate]
  );

  return (
    <div className="p-4 w-full max-w-md">
      <style jsx global>{`
        .calendar-filter-container .fc .fc-daygrid-day {
          overflow: hidden !important;
          padding: 2px !important;
          box-sizing: border-box !important;
        }
        .calendar-filter-container .fc .fc-daygrid-day-frame {
          overflow: hidden !important;
          padding: 1px !important;
          box-sizing: border-box !important;
        }
        .calendar-filter-container .fc .fc-daygrid-day-events {
          display: block !important;
          overflow: hidden !important;
          padding: 0 1px !important;
        }
        .calendar-filter-container .fc .fc-daygrid-event {
          font-size: 0.625rem !important;
          padding: 1px 2px !important;
          margin: 1px 0 !important;
          border-radius: 2px !important;
          cursor: pointer !important;
          overflow: hidden !important;
          text-overflow: ellipsis !important;
          white-space: nowrap !important;
          max-width: calc(100% - 4px) !important;
          display: block !important;
          box-sizing: border-box !important;
        }
        .calendar-filter-container .fc .fc-daygrid-event:hover {
          opacity: 0.8 !important;
        }
        .calendar-filter-container .diary-event-content {
          overflow: hidden !important;
          text-overflow: ellipsis !important;
          white-space: nowrap !important;
          max-width: 100% !important;
          display: block !important;
          box-sizing: border-box !important;
          padding: 0 !important;
        }
        .calendar-filter-container .fc .fc-daygrid-event-harness {
          overflow: hidden !important;
          max-width: 100% !important;
        }
      `}</style>
      <div className="mb-4 flex items-center justify-between">
        {onClose && (
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 transition-colors"
            aria-label="Close calendar"
          >
            <Icon icon="mdi:close" className="w-5 h-5" />
          </button>
        )}
      </div>

      {isLoading && (
        <div className="mb-4 text-center">
          <div className="inline-flex items-center px-3 py-2 text-sm text-blue-600 bg-blue-100 rounded-md">
            <Icon
              icon="mdi:loading"
              className="animate-spin -ml-1 mr-2 h-4 w-4 text-blue-600"
            />
            Loading diaries...
          </div>
        </div>
      )}
      <div className="calendar-container calendar-filter-container">
        <FullCalendar
          plugins={[dayGridPlugin, interactionPlugin]}
          initialView="dayGridMonth"
          headerToolbar={{
            left: 'prev,next today',
            center: 'title',
            right: '',
          }}
          events={calendarEvents}
          eventContent={(arg) => (
            <div
              className="diary-event-content text-center text-white text-xs rounded cursor-pointer"
              title={arg.event.title}
              style={{
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
                maxWidth: '100%',
                padding: '1px 2px',
                boxSizing: 'border-box',
              }}
            >
              {arg.event.title}
            </div>
          )}
          eventClick={handleEventClick}
          dateClick={handleDateClick}
          height="auto"
          dayCellClassNames={(arg) => {
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            const cellDate = new Date(arg.date);
            const accountCreationDate = new Date(user?.createdAt);

            let classes = [];

            // Add cursor pointer for past dates and today
            if (cellDate <= today && cellDate >= accountCreationDate) {
              classes.push('cursor-pointer hover:bg-blue-50');
            } else {
              classes.push('opacity-50 cursor-not-allowed');
            }

            // Highlight selected date
            if (selectedDate === format(cellDate, 'yyyy-MM-dd')) {
              classes.push('bg-blue-100 border-blue-300');
            }

            return classes.join(' ');
          }}
          dayMaxEvents={false}
          moreLinkClick="popover"
        />
      </div>
    </div>
  );
};

export default CalendarFilter;
