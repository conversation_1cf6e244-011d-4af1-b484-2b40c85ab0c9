'use client';

import React, { useEffect, useRef, useState } from 'react';
import { useSelector } from 'react-redux';
import {
  selectMessages,
  selectMe,
  selectRemoteTyping,
  selectIsLoadingMessages,
  selectActiveContact,
  selectTypingUsers,
} from '@/store/features/chatSlice';
import ImagePreviewModal from '@/components/shared/ImagePreviewModal';
import Image from 'next/image';

const MessageList = () => {
  const messages = useSelector(selectMessages);
  const me = useSelector(selectMe);
  const remoteTyping = useSelector(selectRemoteTyping);
  const isLoading = useSelector(selectIsLoadingMessages);
  const messagesEndRef = useRef(null);
  const messagesContainerRef = useRef(null);
  const activeContact = useSelector(selectActiveContact);
  const { isAdmin } = useSelector((state) => state.auth);
  const typingUsers = useSelector(selectTypingUsers);

  // Get typing users excluding current user
  // Show typing indicator only if typing user is not the current user
  const shouldShowTyping =
    remoteTyping &&
    typingUsers?.userId &&
    typingUsers.userId !== me?.id &&
    typingUsers?.conversationId === activeContact?.conversationId;

  // Image preview modal state
  const [imagePreview, setImagePreview] = useState({
    isOpen: false,
    imageUrl: '',
    imageName: '',
    imageAlt: '',
  });

  const handleImageClick = (attachment) => {
    setImagePreview({
      isOpen: true,
      imageUrl: attachment.fileUrl || attachment.url,
      imageName: attachment.fileName || attachment.name || 'Image',
      imageAlt: attachment.fileName || attachment.name || 'Chat image',
    });
  };

  const closeImagePreview = () => {
    setImagePreview({
      isOpen: false,
      imageUrl: '',
      imageName: '',
      imageAlt: '',
    });
  };

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    const scrollToBottom = () => {
      if (messagesEndRef.current) {
        messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
      }
    };

    // Small delay to ensure DOM is updated
    const timeoutId = setTimeout(scrollToBottom, 100);
    return () => clearTimeout(timeoutId);
  }, [messages, shouldShowTyping]);

  const statusIcon = (status) => {
    switch (status) {
      case 'sending':
        return '…';
      case 'sent':
        return '✓';
      case 'delivered':
        return '✓✓';
      case 'read':
        return '✓✓';
      default:
        return '';
    }
  };

  const isImage = (attachment) => {
    const mimeType = attachment.mimeType || attachment.type || '';
    const url = attachment.url || attachment.fileUrl || '';
    return (
      mimeType.startsWith('image') || /\.(jpe?g|png|webp|gif|bmp)$/i.test(url)
    );
  };

  const formatTime = (timestamp) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false,
    });
  };

  // HTML parser utility function
  const parseMessageContent = (content) => {
    if (!content) return null;

    // Check if content contains HTML tags
    const htmlRegex = /<[^>]*>/;
    const isHtmlContent = htmlRegex.test(content);

    if (isHtmlContent) {
      // Parse and render HTML content safely
      return (
        <div
          dangerouslySetInnerHTML={{ __html: content }}
          style={{
            wordWrap: 'break-word',
            whiteSpace: 'pre-wrap',
          }}
        />
      );
    } else {
      // Render plain text content
      return (
        <div
          style={{
            wordWrap: 'break-word',
            whiteSpace: 'pre-wrap',
          }}
        >
          {content}
        </div>
      );
    }
  };

  if (isLoading) {
    return (
      <div
        style={{
          flex: 1,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          background: '#f7f9fb',
        }}
      >
        <div style={{ color: '#6b7280', fontSize: '14px' }}>
          Loading messages...
        </div>
      </div>
    );
  }

  return (
    <div
      ref={messagesContainerRef}
      style={{
        flex: 1,
        overflowY: 'auto',
        padding: '0px',
        background: '#f7f9fb',
      }}
    >
      {messages.length === 0 ? (
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            color: '#6b7280',
            fontSize: '14px',
          }}
        >
          No messages yet. Start the conversation!
        </div>
      ) : (
        <div className="p-2 overflow-y-auto max-sm:max-h-[calc(100vh-200px)]">
          {messages.map((message) => {
            // Use me.id for user identification
            // const currentUserId = me?.id || message?.senderName;

            // More robust isOwn calculation with debugging
            const senderIdMatch = isAdmin
              ? message?.isSenderVirtualAdmin
              : message?.senderId === me?.id;

            // const senderObjectMatch = message?.sender?.id === currentUserId;
            const isOwn = senderIdMatch;

            // console.log(message);
            return (
              <div
                key={message.id}
                style={{
                  display: 'flex',
                  justifyContent: isOwn ? 'flex-end' : 'flex-start',
                  marginBottom: '12px',
                }}
              >
                <div
                  style={{
                    maxWidth: '70%',
                    position: 'relative',
                    wordWrap: 'break-word',
                    position: 'relative',
                  }}
                >
                  {message?.isAdminMessage && isAdmin && isOwn && (
                    <div className="text-xs text-end group cursor-pointer hover:underline">
                      {message?.actualSender?.id === me?.id
                        ? 'Self'
                        : message?.actualSender?.name || 'admin'}

                      {message?.actualSender?.id !== me?.id && (
                        <div className="absolute z-20 group-hover:block hidden p-5 bg-yellow-50 rounded-lg -left-40 top-0">
                          {message?.actualSender?.profilePictureUrl && (
                            <Image
                              src={message?.actualSender?.profilePictureUrl}
                              alt={message?.actualSender?.name}
                              width={50}
                              height={50}
                              className="h-20 w-20 rounded-full mx-auto"
                            />
                          )}
                          <h2 className="text-lg font-semibold">
                            {message?.actualSender?.id === me?.id
                              ? 'Self'
                              : message?.actualSender?.name || 'admin'}
                          </h2>
                        </div>
                      )}
                    </div>
                  )}
                  {message.content && (
                    <div
                      style={{
                        padding: '10px 14px',
                        fontSize: '14px',
                        background: isOwn ? '#fad622ff' : '#e5e7eb',
                        color: isOwn ? '#000' : '#000',
                        fontWeight: 500,
                        borderRadius: '16px',
                        borderBottomRightRadius: isOwn ? '4px' : '16px',
                        borderBottomLeftRadius: isOwn ? '16px' : '4px',
                        marginBottom:
                          message.attachments?.length > 0 ? '8px' : '0',
                      }}
                    >
                      {parseMessageContent(message.content)}
                    </div>
                  )}

                  {message.attachments?.length > 0 && (
                    <div
                      style={{
                        display: 'flex',
                        flexDirection: 'column',
                        gap: '6px',
                      }}
                    >
                      {message.attachments.map((attachment) =>
                        isImage(attachment) ? (
                          <Image
                            key={attachment.id}
                            src={attachment.fileUrl || attachment.url || '..'}
                            alt={
                              attachment.fileName || attachment.name || 'Image'
                            }
                            onClick={() => handleImageClick(attachment)}
                            width={200}
                            height={200}
                            className="max-w-[200px] max-h-[200px] object-cover rounded-[8px] cursor-pointer transition-opacity duration-200"
                            onMouseEnter={(e) =>
                              (e.target.style.opacity = '0.8')
                            }
                            onMouseLeave={(e) => (e.target.style.opacity = '1')}
                          />
                        ) : (
                          <a
                            key={attachment.id}
                            href={attachment.fileUrl || attachment.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            style={{
                              fontSize: '12px',
                              color: isOwn ? '#bfdbfe' : '#2563eb',
                              textDecoration: 'underline',
                              display: 'flex',
                              alignItems: 'center',
                              gap: '4px',
                            }}
                          >
                            <span>📎</span>
                            {attachment.fileName ||
                              attachment.name ||
                              'Download'}
                          </a>
                        )
                      )}
                    </div>
                  )}

                  <div
                    style={{
                      fontSize: '11px',
                      color: isOwn ? '#9ca3af' : '#9ca3af',
                      marginTop: '4px',
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                    }}
                  >
                    <span>{formatTime(message.createdAt)}</span>
                    {isOwn ? (
                      <span
                        style={{
                          marginLeft: '8px',
                          color:
                            message.status === 'read'
                              ? '#60a5fa'
                              : message.status === 'delivered'
                              ? '#9ca3af'
                              : '#9ca3af',
                        }}
                      >
                        {statusIcon(message.status)}
                      </span>
                    ) : (
                      message?.isAdminMessage &&
                      !isAdmin && (
                        <div className="text-xs text-end group cursor-pointer hover:underline">
                          {message?.actualSender?.id === me?.id
                            ? 'Self'
                            : message?.actualSender?.name || 'admin'}

                          <div className="absolute z-20 group-hover:block hidden p-5 py-2 bg-yellow-50 rounded-lg -right-40 bottom-0">
                            <Image
                              src={message?.actualSender?.profilePictureUrl}
                              alt={message?.actualSender?.name}
                              width={50}
                              height={50}
                              className="h-20 w-20 rounded-full mx-auto"
                            />
                            <h2 className="text-lg font-semibold">
                              {message?.actualSender?.id == me?.id
                                ? 'Self'
                                : message?.actualSender?.name}
                            </h2>
                          </div>
                        </div>
                      )
                    )}
                  </div>
                </div>
              </div>
            );
          })}

          {/* Show typing indicator only for other users */}
          {shouldShowTyping && (
            <div
              style={{
                display: 'flex',
                justifyContent: 'flex-start',
                marginBottom: '12px',
              }}
            >
              <div
                style={{
                  padding: '10px 14px',
                  fontSize: '13px',
                  background: '#e5e7eb',
                  color: '#6b7280',
                  borderRadius: '16px',
                  borderBottomLeftRadius: '4px',
                  fontStyle: 'italic',
                }}
              >
                {typingUsers.name} is typing...
              </div>
            </div>
          )}

          <div ref={messagesEndRef} />
        </div>
      )}

      {/* Image Preview Modal */}
      <ImagePreviewModal
        isOpen={imagePreview.isOpen}
        onClose={closeImagePreview}
        imageUrl={imagePreview.imageUrl}
        imageName={imagePreview.imageName}
        imageAlt={imagePreview.imageAlt}
      />
    </div>
  );
};

export default MessageList;
