'use client';
import React from 'react';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import FormInput from '@/components/form/FormInput';
import api from '@/lib/api';
import { useSelector } from 'react-redux';

const CreateQuestions = () => {
  const queryClient = useQueryClient();
const auth = useSelector((state) => state.auth);
const getQAQuestionCreateEndpoint = (roles) =>
  roles?.includes('tutor') ? '/tutor/qa/create' : '/admin/qa/create';
  // Define validation schema
  const validationSchema = Yup.object().shape({
    question: Yup.string().required('Question is required'),
    points: Yup.number().required('Points is required').positive('Points must be positive'),
    minimumWords: Yup.number().required('Minimum Words is required').positive('Minimum words must be positive'),
    isActive: Yup.boolean().default(true)
  });

  // Define initial values
  const initialValues = {
    question: '',
    points: '',
    minimumWords: '',
    isActive: true
  };

  // Create mutation for API call
  const createQuestionMutation = useMutation({
      mutationFn: (questionData) =>
    api.post(getQAQuestionCreateEndpoint(auth?.user?.roles), questionData),
    onSuccess: () => {
      // Invalidate and refetch questions list if needed
      queryClient.invalidateQueries({ queryKey: ['questions'] });
      
    },
    onError: (error) => {
      console.error('Error creating question:', error);
   
    }
  });

  // Handle form submission
  const handleSubmit = (values, { setSubmitting, resetForm }) => {
    // Convert string values to appropriate types
    const payload = {
      question: values.question,
      points: parseInt(values.points, 10),
      minimumWords: parseInt(values.minimumWords, 10),
      isActive: values.isActive
    };

    createQuestionMutation.mutate(payload, {
      onSettled: () => {
        setSubmitting(false);
        resetForm();
      }
    });
  };

  return (
    <div className=''>
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
      >
        {({ isSubmitting, errors, touched, resetForm }) => (
          <Form className="mx-auto bg-gray-100 p-6 rounded-lg shadow-md">
            <div className="flex flex-wrap gap-4 mb-4">
              <div className="flex-1 min-w-[200px]">
                <FormInput
                  label="Question"
                  name="question"
                  id="question"
                  placeholder="Write question here"
                  isTextarea={true}
                  autoResize={true}
                  required={true}
                />
              </div>

              <div className="flex-1 min-w-[200px]">
                <FormInput
                  label="Points"
                  type="number"
                  name="points"
                  id="points"
                  placeholder="Enter points"
                  required={true}
                />
              </div>

              <div className="flex-1 min-w-[200px]">
                <FormInput
                  label="Minimum Words"
                  type="number"
                  name="minimumWords"
                  id="minimumWords"
                  placeholder="Enter minimum words required"
                  required={true}
                />
              </div>
            </div>

            <div className="flex justify-end gap-4 mt-7">
              <button
                type="button"
                className="bg-gray-300 hover:bg-gray-400 text-black font-medium py-2 px-4 rounded"
                onClick={() => resetForm()}
                disabled={isSubmitting}
              >
                Cancel
              </button>
              <button
                type="submit"
                className="bg-[#FFDE34] hover:bg-yellow-400 text-black font-medium py-2 px-4 rounded"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Creating...' : 'Create'}
              </button>
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default CreateQuestions;