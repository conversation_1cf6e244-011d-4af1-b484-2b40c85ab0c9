'use client';
import { useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Formik, Form, Field, ErrorMessage } from 'formik';
import * as Yup from 'yup';
import Button from '@/components/Button';
import Modal from '@/components/Modal';
import api from '@/lib/api';
import {
  selectCanvasItems,
  selectCanvasBackground,
  selectCanvasWidth,
  selectCanvasHeight,
  clearSelection,
} from '@/store/features/canvasSlice';
import { useRouter } from 'next/navigation';
import { setOpenCreateSkinModal } from '@/store/features/commonSlice';
import { queryClient } from '@/lib/queryClient';

const Topbar = ({ isAdmin }) => {
  const dispatch = useDispatch();
  const canvasItems = useSelector(selectCanvasItems);
  const canvasBackground = useSelector(selectCanvasBackground);
  const canvasWidth = useSelector(selectCanvasWidth);
  const canvasHeight = useSelector(selectCanvasHeight);
  const router = useRouter();

  // State for save dialog
  const [isSaveDialogOpen, setIsSaveDialogOpen] = useState(false);
  const [isDefaultSkin, setIsDefaultSkin] = useState(false);
  const [saveError, setSaveError] = useState('');

  // Validation schema for the form
  const validationSchema = Yup.object({
    name: Yup.string().required('Name is required'),
    description: Yup.string().required('Description is required'),
    isActive: Yup.boolean(),
  });

  const handleSaveAsSkin = () => {
    // First, deselect any selected element
    dispatch(clearSelection());
    setIsDefaultSkin(false);
    setIsSaveDialogOpen(true);
  };

  const handleSaveAsDefaultSkin = () => {
    // First, deselect any selected element
    dispatch(clearSelection());
    setIsDefaultSkin(true);
    setIsSaveDialogOpen(true);
  };

  const closeDialog = () => {
    setIsSaveDialogOpen(false);
    setSaveError('');
  };

  const handleSaveSkin = async (values, { setSubmitting }) => {
    setSaveError('');

    try {
      // Generate template content as a string
      const templateContent = JSON.stringify({
        items: canvasItems,
        background: canvasBackground,
        width: canvasWidth,
        height: canvasHeight,
      });

      // Create FormData object for multipart/form-data
      const formData = new FormData();
      formData.append('name', values.name);
      formData.append('description', values.description);
      formData.append('isActive', values.isActive);
      formData.append('templateContent', templateContent);

      // Capture the canvas as an image and use it as the preview image
      if (window.captureCanvasImage) {
        const previewImage = await window.captureCanvasImage();
        if (previewImage) {
          formData.append('previewImage', previewImage);
        } else {
          throw new Error(
            'Failed to generate preview image. Please try again.'
          );
        }
      } else {
        throw new Error(
          'Canvas capture function not available. Please refresh the page and try again.'
        );
      }

      // Call the API using the api utility
      const url = isAdmin ? '/admin/diary/skins' : '/diary/skins';
      const response = await api.post(url, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        showSuccessMessage: true,
        successMessage: 'Skin saved successfully!',
      });

      // Success - close the dialog
      closeDialog();
      if (isAdmin) {
        router.push('/dashboard/skins');
      } else {
        queryClient.invalidateQueries('diary/skins');
        dispatch(setOpenCreateSkinModal(false));
      }

      // If it's a default skin, also save to localStorage
      if (isDefaultSkin) {
        localStorage.setItem('selectedSkin', templateContent);
      }

      console.log('Skin saved successfully:', response);
    } catch (error) {
      console.error('Error saving skin:', error);
      setSaveError(
        error.response?.data?.message ||
          error.message ||
          'Failed to save skin. Please try again.'
      );
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div className="w-full bg-[#8B4513] text-white py-3 px-6 flex items-center justify-between">
      {/* Left side - Title */}
      <div className="text-xl font-medium tracking-wide">Create Skin</div>

      {/* Right side - Buttons */}
      <div className="flex items-center space-x-3">
        <Button
          buttonText="Save as Skin"
          onClick={handleSaveAsSkin}
          className="w-auto"
        />

        <Button
          buttonText="Save as Default Skin"
          onClick={handleSaveAsDefaultSkin}
          className="w-auto"
        />
      </div>

      {/* Save Dialog */}
      <Modal
        isOpen={isSaveDialogOpen}
        onClose={closeDialog}
        position="center"
        title={isDefaultSkin ? 'Save as Default Skin' : 'Save as Skin'}
        width="md"
      >
        <div className="p-4">
          {saveError && (
            <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-md">
              {saveError}
            </div>
          )}

          <Formik
            initialValues={{
              name: '',
              description: '',
              isActive: true,
            }}
            validationSchema={validationSchema}
            onSubmit={handleSaveSkin}
          >
            {({ isSubmitting, touched, errors }) => (
              <Form>
                <div className="mb-4">
                  <label
                    htmlFor="name"
                    className="block text-sm font-medium mb-1 text-gray-700"
                  >
                    Name <span className="text-red-500">*</span>
                  </label>
                  <Field
                    id="name"
                    name="name"
                    type="text"
                    className={`w-full p-2 border rounded-md ${
                      touched.name && errors.name
                        ? 'border-red-500'
                        : 'border-gray-300'
                    }`}
                    placeholder="Enter skin name"
                  />
                  <ErrorMessage
                    name="name"
                    component="p"
                    className="mt-1 text-sm text-red-500"
                  />
                </div>

                <div className="mb-4">
                  <label
                    htmlFor="description"
                    className="block text-sm font-medium mb-1 text-gray-700"
                  >
                    Description <span className="text-red-500">*</span>
                  </label>
                  <Field
                    as="textarea"
                    id="description"
                    name="description"
                    rows="3"
                    className={`w-full p-2 border rounded-md ${
                      touched.description && errors.description
                        ? 'border-red-500'
                        : 'border-gray-300'
                    }`}
                    placeholder="Enter skin description"
                  />
                  <ErrorMessage
                    name="description"
                    component="p"
                    className="mt-1 text-sm text-red-500"
                  />
                </div>

                <div className="mb-4">
                  <div className="flex items-center justify-between mb-1">
                    <p className="text-sm text-gray-700">Preview Image</p>
                    <span className="text-xs text-gray-500">
                      Will be automatically generated from the canvas
                    </span>
                  </div>
                  <div className="w-full p-3 border border-gray-300 rounded-md bg-gray-50 text-center">
                    <p className="text-sm text-gray-600">
                      A preview image will be generated when you save
                    </p>
                  </div>
                </div>

                <div className="mb-6">
                  <label className="flex items-center">
                    <Field
                      type="checkbox"
                      name="isActive"
                      className="mr-2 h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300 rounded"
                    />
                    <span className="text-sm text-gray-700">Active</span>
                  </label>
                </div>

                <div className="flex justify-end space-x-3">
                  <Button
                    buttonText="Cancel"
                    onClick={closeDialog}
                    className="w-auto bg-gray-200 text-gray-800 hover:bg-gray-300"
                    type="button"
                  />
                  <Button
                    buttonText={isSubmitting ? 'Saving...' : 'Save'}
                    type="submit"
                    className="w-auto"
                    disabled={isSubmitting}
                  />
                </div>
              </Form>
            )}
          </Formik>
        </div>
      </Modal>
    </div>
  );
};

export default Topbar;
