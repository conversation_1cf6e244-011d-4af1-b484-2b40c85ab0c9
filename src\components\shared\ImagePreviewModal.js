'use client';
import React from 'react';
import Modal from '../Modal';
import Image from 'next/image';
import { ButtonIcon } from '../Button';

const ImagePreviewModal = ({ 
  isOpen, 
  onClose, 
  imageUrl, 
  imageName = 'Image',
  imageAlt = 'Preview'
}) => {
  if (!imageUrl) return null;

  const handleDownload = () => {
    const link = document.createElement('a');
    link.href = imageUrl;
    link.download = imageName || 'image';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      position="center"
      width="4xl"
      showCloseButton={false}
      className="rounded-lg"
      maxHeight="90vh"
    >
      <div className="relative">
        {/* Header with close and download buttons */}
        <div className="absolute top-2 right-2 z-10 flex gap-2">
          <ButtonIcon
            icon="material-symbols:download"
            innerBtnCls="h-8 w-8 bg-black bg-opacity-50 hover:bg-opacity-70"
            btnIconCls="h-5 w-5 text-white"
            onClick={handleDownload}
            aria-label="Download image"
            withBackground={false}
          />
          <ButtonIcon
            icon="mdi:close"
            innerBtnCls="h-8 w-8 bg-black bg-opacity-50 hover:bg-opacity-70"
            btnIconCls="h-5 w-5 text-white"
            onClick={onClose}
            aria-label="Close modal"
            withBackground={false}
          />
        </div>

        {/* Image container */}
        <div className="flex items-center justify-center bg-black rounded-lg overflow-hidden">
          <div className="relative max-w-full max-h-[80vh]">
            <Image
              src={imageUrl}
              alt={imageAlt}
              width={800}
              height={600}
              className="object-contain w-auto h-auto max-w-full max-h-[80vh]"
              priority
              unoptimized // Allow external URLs
            />
          </div>
        </div>

        {/* Image name/caption */}
        {imageName && (
          <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white p-3 rounded-b-lg">
            <p className="text-sm text-center truncate">{imageName}</p>
          </div>
        )}
      </div>
    </Modal>
  );
};

export default ImagePreviewModal;
