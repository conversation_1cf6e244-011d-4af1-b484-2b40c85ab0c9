/** @type {import('next').NextConfig} */

// Get API base URL and extract protocol and hostname
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://**************:3010';

// Extract protocol and hostname from the full URL
const url = new URL(API_BASE_URL);
const protocol = url.protocol.slice(0, -1); // Remove the trailing ':'
const hostname = url.hostname;

const nextConfig = {
  reactStrictMode: false, // Helps catch potential React issues

  images: {
    remotePatterns: [
      {
        protocol: protocol,
        hostname: hostname,
        pathname: '/**',
      },
      {
        protocol: 'http',
        hostname: '************',
        pathname: '/**',
      },
      {
        protocol: 'http',
        hostname: '**************',
        pathname: '/**',
      },
      {
        protocol: 'http',
        hostname: 'localhost',
        port: '3000',
        pathname: '/uploads/**',
      },
      {
        protocol: 'https',
        hostname: 'api.dicebear.com',
        pathname: '/**',
      },
    ],
  },

  // Optimize handling of server-side modules in client-side code
  webpack: (config, { isServer }) => {
    if (!isServer) {
      config.resolve.fallback = {
        fs: false,
        path: false,
        os: false,
      };
    }
    return config;
  },

  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          { key: 'X-Frame-Options', value: 'SAMEORIGIN' },
          { key: 'X-Content-Type-Options', value: 'nosniff' },
          { key: 'Referrer-Policy', value: 'strict-origin-when-cross-origin' },
          {
            key: 'Permissions-Policy',
            value: 'camera=(), microphone=(), geolocation=()',
          },
        ],
      },
    ];
  },

  // Redirects: Helps with URL changes
  async redirects() {
    return [
      {
        source: '/old-path',
        destination: '/new-path',
        permanent: true,
      },
    ];
  },

  // Rewrites: Useful for proxying API requests (if needed)
  async rewrites() {
    return [
      {
        source: '/api/:path*',
        destination: `${API_BASE_URL}/api/:path*`,
      },
    ];
  },

  // Environment variables are now managed in src/lib/config.js
};

module.exports = nextConfig;
