'use client';

import React, { useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useRouter, useSearchParams } from 'next/navigation';
import Cookies from 'js-cookie';
import api from '@/lib/api';
import {
  setMe,
  setContacts,
  setActiveContact,
  setConversationId,
  setMessages,
  setIsLoadingContacts,
  setIsLoadingMessages,
  setIsSendingMessage,
  clearMessageInput,
  addMessage,
  replaceOptimisticMessage,
  updateContact,
  clearUnreadCount,
  selectActiveContact,
  selectConversationId,
  selectMe,
  selectMessageInput,
  selectAttachedFiles,
} from '@/store/features/chatSlice';
import { useChatSocket } from './hooks/useChatSocket';
import ContactList from './components/ContactList';
import ChatWindow from './components/ChatWindow';
import useDataFetch from '@/hooks/useDataFetch';

const UPLOAD_ENDPOINT = '/chat/upload';
const token = Cookies.get('token') || '';

// Helper function to map server message format
const mapServerMessage = (serverMessage) => ({
  id: serverMessage.id,
  conversationId: serverMessage.conversationId,
  content: serverMessage.content,
  senderId: serverMessage.senderId, // Ensure senderId is always set
  sender: { id: serverMessage.senderId }, // Ensure sender.id matches senderId
  createdAt: serverMessage.createdAt || new Date().toISOString(),
  status: serverMessage.status || 'sent',
  isSenderVirtualAdmin: serverMessage.isSenderVirtualAdmin,
  attachments: serverMessage.attachments || [],
  attachmentIds: serverMessage.attachmentIds || [],
});

export default function ChatPage() {
  const dispatch = useDispatch();
  const router = useRouter();
  const searchParams = useSearchParams();
  const urlConversationId = searchParams.get('conversationId');
  const userId = searchParams.get('userId');
  const { isAdmin, isStudent, user } = useSelector((state) => state.auth);

  // Redux selectors
  const activeContact = useSelector(selectActiveContact);
  const conversationId = useSelector(selectConversationId);
  const me = useSelector(selectMe);
  const messageInput = useSelector(selectMessageInput);
  const attachedFiles = useSelector(selectAttachedFiles);
  const [showChat, setShowChat] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  // Socket hook
  const { sendMessage, sendTyping } = useChatSocket();

  // Handle window resize for mobile detection
  useEffect(() => {
    const handleResize = () => {
      const mobile = window.innerWidth < 640; // sm breakpoint
      setIsMobile(mobile);

      // If switching to desktop and no active contact, show contact list
      if (!mobile && !activeContact) {
        setShowChat(false);
      }
      // If switching to desktop and there's an active contact, show both
      if (!mobile && activeContact) {
        setShowChat(true);
      }
    };

    // Set initial state
    handleResize();

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [activeContact]);

  // Refs for socket callbacks
  const conversationIdRef = useRef(null);
  const meRef = useRef(null);

  const { data: userData, isLoading: targetedContactLoading } = useDataFetch({
    queryKey: ['targeted-user-info', userId],
    endPoint: `/users/${userId}`,
    enabled: !!isAdmin && !!userId,
  });

  // Handle responsive sidebar behavior
  // useEffect(() => {
  //   const handleResize = () => {
  //     const isDesktop = window.innerWidth >= 1280; // xl breakpoint
  //     if (isDesktop) {
  //       setShowChat(false); // Always show sidebar on desktop
  //     } else {
  //       setShowChat(true); // Hide sidebar on mobile/tablet by default
  //     }
  //   };

  //   // Initial check
  //   handleResize();

  //   // Add event listener
  //   window.addEventListener('resize', handleResize);

  //   // Cleanup
  //   return () => window.removeEventListener('resize', handleResize);
  // }, []);

  // Update refs when values change
  useEffect(() => {
    conversationIdRef.current = conversationId;
  }, [conversationId]);

  useEffect(() => {
    meRef.current = me;
  }, [me]);

  // Initialize data on component mount
  useEffect(() => {
    if (!token) return;

    const initializeData = async () => {
      try {
        dispatch(setIsLoadingContacts(true));
        const contactsResponse = await api.get('/chat/contacts');
        dispatch(setContacts(contactsResponse.data || []));
      } catch (error) {
        console.error('Failed to load contacts:', error);
      } finally {
        dispatch(setIsLoadingContacts(false));
      }

      try {
        // const userResponse = await api.get('/users/profile');
        dispatch(setMe(user));
      } catch (error) {
        console.error('Failed to load user profile:', error);
      }
    };

    initializeData();
  }, [dispatch]);

  // Handle URL conversation ID parameter
  useEffect(() => {
    if (
      urlConversationId &&
      conversationId !== urlConversationId &&
      userData &&
      isAdmin &&
      userId // Ensure userId is still in URL
    ) {
      const contact = {
        id: userData?.id,
        name: userData?.name,
        userId: userData?.userId,
        email: userData?.email,
        type: userData?.type,
        profilePicture: userData?.profilePictureUrl,
        conversationId: urlConversationId,
        isSenderVirtualAdmin: isAdmin,
      };

      setShowChat(true);
      // console.log('changing from useEffect list');
      dispatch(setActiveContact(contact));
      dispatch(setConversationId(contact.conversationId));
      dispatch(setMessages([]));

      handleContactSelect(contact);
    }
  }, [urlConversationId, conversationId, userData, userId]);

  // Handle contact selection
  const handleContactSelect = async (contact) => {
    try {
      // On mobile, show chat window when contact is selected
      // On desktop, always show both
      if (!isMobile && userId && urlConversationId !== contact.conversationId) {
        router.push(`/chat-app?conversationId=${contact.conversationId}`);
      }
      setShowChat(true);
      dispatch(setIsLoadingMessages(true));
      // console.log('changing from function');
      dispatch(setActiveContact(contact));
      dispatch(setConversationId(contact.conversationId));
      dispatch(setMessages([]));

      // Clear unread count for this conversation
      dispatch(clearUnreadCount({ conversationId: contact.conversationId }));

      // Load messages for the conversation
      const response = await api.get(
        `/chat/conversations/${contact.conversationId}/messages`
      );
      const messages = response.data?.items || response.messages || [];
      // const mappedMessages = messages
      //   .map(mapServerMessage)
      //   .sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt));
      dispatch(
        setMessages(
          messages.sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt))
        )
      );
    } catch (error) {
      console.error('Failed to load conversation:', error);
    } finally {
      dispatch(setIsLoadingMessages(false));
    }
  };

  // Handle typing indicator
  const handleTyping = (isTyping) => {
    if (sendTyping) {
      sendTyping(isTyping);
    }
  };

  // Handle file upload
  const uploadFiles = async (files) => {
    if (!files.length) return { attachments: [], ids: [] };

    const attachments = [];
    const ids = [];

    for (const fileData of files) {
      try {
        const formData = new FormData();
        formData.append('file', fileData.file);
        formData.append('conversationId', conversationId);
        const response = await api.post(UPLOAD_ENDPOINT, formData, {
          headers: { 'Content-Type': 'multipart/form-data' },
        });

        if (response.success && response.data) {
          // Extract only serializable file metadata
          const attachment = {
            id: response?.data?.id,
            name: response?.data?.fileName,
            size: response?.data?.fileSize,
            type: response?.data?.mimeType,
            url: response?.data?.fileUrl,
          };
          attachments.push(attachment);
          ids.push(attachment?.id);
        }
      } catch (error) {
        console.error('Failed to upload file:', fileData.file.name, error);
      }
    }

    return { attachments, ids };
  };

  // Handle sending message
  const handleSendMessage = async (filesWithObjects = null) => {
    const filesToUpload = filesWithObjects || attachedFiles;

    if (
      (!messageInput.trim() && filesToUpload.length === 0) ||
      !conversationId ||
      !me
    ) {
      return;
    }

    try {
      dispatch(setIsSendingMessage(true));

      // Upload files first
      const { attachments, ids } = await uploadFiles(filesToUpload);

      // Create optimistic message
      const localId = `local-${Date.now()}-${Math.random()}`;

      const optimisticMessage = {
        id: localId,
        conversationId,
        content: messageInput.trim(),
        sender: { id: me?.id, name: me?.name, email: me?.email },
        senderId: me?.id,
        createdAt: new Date(),
        status: 'sending',
        isSenderVirtualAdmin: isAdmin,
        attachments,
      };

      // console.log('optimisticMessage id:', optimisticMessage?.senderId);

      // Add optimistic message to store
      dispatch(addMessage(optimisticMessage));

      // Update contact's last message
      dispatch(
        updateContact({
          conversationId,
          lastMessage: optimisticMessage.content,
          lastMessageTime: optimisticMessage.createdAt,
        })
      );

      // Clear input
      dispatch(clearMessageInput());

      // Prepare payload for socket
      const payload = {
        id: localId,
        conversationId,
        content: messageInput.trim(),
        senderId: me.id,
        sender: { id: me.id, name: me.name, email: me.email },
        recipientId: activeContact.id,
        type: 'text',
        isSenderVirtualAdmin: isAdmin,
        timestamp: new Date().toISOString(),
        attachments,
        attachmentIds: ids,
      };

      // Send via socket
      if (sendMessage) {
        sendMessage(payload, (ack) => {
          if (ack && ack.success !== false) {
            const serverMessage = mapServerMessage(ack.data || ack);

            dispatch(
              replaceOptimisticMessage({
                localId,
                serverMessage,
              })
            );
          } else {
            // If acknowledgment indicates failure
            dispatch(
              replaceOptimisticMessage({
                localId,
                serverMessage: {
                  ...optimisticMessage,
                  status: 'failed',
                },
              })
            );
          }
        });
      } else {
        // If no socket connection, mark as failed
        dispatch(
          replaceOptimisticMessage({
            localId,
            serverMessage: {
              ...optimisticMessage,
              status: 'failed',
            },
          })
        );
      }
    } catch (error) {
      console.error('Failed to send message:', error);
    } finally {
      dispatch(setIsSendingMessage(false));
    }
  };

  return (
    <div
      className={`
        flex flex-col sm:flex-row items-stretch border border-gray-300 rounded-lg max-sm:overflow-y-auto overflow-hidden
        ${
          isStudent
            ? 'h-[calc(100vh-55px)] lg:h-[calc(100vh-102px)]'
            : 'h-[calc(100vh-60px)] max-sm:h-[calc(100vh-55px)]'
        }
      `}
      style={{
        fontFamily: 'Inter, sans-serif',
      }}
    >
      <ContactList
        onContactSelect={handleContactSelect}
        showChat={showChat}
        isMobile={isMobile}
      />
      <ChatWindow
        onSendMessage={handleSendMessage}
        onTyping={handleTyping}
        showChat={showChat}
        setShowChat={setShowChat}
        isMobile={isMobile}
      />
    </div>
  );
}
