'use client';
import RegularGoBack from '@/components/shared/RegularGoBack';
import useDataFetch from '@/hooks/useDataFetch';
import { Icon } from '@iconify/react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import React, { useState, useEffect, useCallback } from 'react';
import { IMAGE_BASE_URL } from '@/lib/config';
import { useQueryClient } from '@tanstack/react-query';
import Link from 'next/link';
import DeleteModal from '@/components/form/modal/DeleteModal';

const Emoticons = () => {
  const router = useRouter();
  const [inputValue, setInputValue] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const queryClient = useQueryClient();
  const [deleteData, setDeleteData] = useState(null);
  const [searchTimeout, setSearchTimeout] = useState(null);

  const {data: emoticonId, isLoading: isIconLoading} = useDataFetch({
    queryKey: 'emoticon-id',
    endPoint: '/admin/shop/emoticon-category-id'
  })

  // Fetch data with debounced search query
  const { data, isLoading, refetch } = useDataFetch({
    queryKey: ['emoticons', searchQuery, emoticonId],
    endPoint: `/admin/shop/categories/${emoticonId}/items${
      searchQuery.length > 2 ? `?title=${searchQuery}` : ``
    }`,
    enabled: !!emoticonId
  });

  // Debounced search handler
  const handleSearch = (e) => {
    const value = e.target.value;
    setInputValue(value);

    // Clear any existing timeout
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    // Set a new timeout for the search query
    const timeout = setTimeout(() => {
      setSearchQuery(value);

      if (value.length === 0) {
        queryClient.invalidateQueries(['emoticons']);
      }
    }, 500); // 500ms debounce delay

    setSearchTimeout(timeout);
  };

  // Clean up timeout on component unmount
  useEffect(() => {
    return () => {
      if (searchTimeout) {
        clearTimeout(searchTimeout);
      }
    };
  }, [searchTimeout]);

  return (
    <div className="shadow-md rounded-lg">
      <div className="flex justify-between items-center p-5 bg-gray-50">
        <h2 className="sm:text-xl font-[500]">Emoticon Management</h2>
      </div>

      <div className="p-5">
        <RegularGoBack className={'pb-5 max-w-32'} />

        <div className="flex gap-4 mb-8">
          <div className="flex-1 relative">
            <input
              type="text"
              placeholder="Search by name"
              value={inputValue}
              onChange={handleSearch}
              className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-1 focus:ring-yellow-400"
            />
            <Icon
              icon="solar:magnifier-linear"
              className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 w-5 h-5"
            />
          </div>

          <Link
            href={'/dashboard/emoticons/add'}
            className="bg-yellow-400 hover:bg-yellow-500 text-black px-4 py-2 rounded-lg flex items-center"
          >
            <Icon icon="solar:add-circle-linear" className="mr-2" />
            Add Item
          </Link>
        </div>

        {/* Loading state */}
        {isLoading && (
          <div className="text-center py-8 text-gray-500">Loading...</div>
        )}

        {/* No results message */}
        {!isLoading && data?.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            {searchQuery.length > 2
              ? `No items found matching "${searchQuery}"`
              : 'No items available'}
          </div>
        )}

        {/* Items grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {data?.items.map((item) => (
            <div
              key={item.id}
              className="bg-[#F6F6F6] rounded-lg shadow-sm p-4 border bg-gray-100"
            >
              {/* Item Image */}
              <div className="relative h-48 mb-4">
                <Image
                  src={
                    item.filePath
                      ? `${item.filePath}`
                      : '/assets/images/all-img/noImage.png'
                  }
                  alt={item.title}
                  fill
                  className="rounded-lg object-contain"
                />
                {item.type === 'free' && (
                  <span className="absolute -top-4 -left-4 bg-green-500 text-white text-xs px-3 py-1 rounded-br-lg rounded-tl-lg">
                    New
                  </span>
                )}
              </div>

              {/* Item Details */}
              <div className="">
                <h4 className="font-medium text-gray-800">{item.title}</h4>
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <span className="font-[600]">
                      {item.price === 0 ? 'Free' : `${item.price} ₩`}
                    </span>
                    {item.isOnSale && (
                      <span className="ml-2 text-sm text-gray-500 line-through">
                        {item.discountedPrice} ₩
                      </span>
                    )}
                  </div>

                  {/* Action Buttons */}
                  <div className="flex items-center space-x-2 mt-2">
                    <button
                      onClick={() =>
                        router.push(`/dashboard/emoticons/edit/${item?.id}`)
                      }
                      className="p-1 hover:shadow bg-yellow-100 border border-yellow-300 rounded"
                    >
                      <Icon
                        icon="material-symbols:edit-outline"
                        className="w-5 h-5"
                      />
                    </button>
                    <button
                      onClick={() => setDeleteData(item)}
                      className="p-1 hover:shadow bg-yellow-100 rounded text-red-500 border border-red-300"
                    >
                      <Icon
                        icon="solar:trash-bin-trash-linear"
                        className="w-5 h-5"
                      />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      <DeleteModal
        isOpen={!!deleteData}
        onClose={() => setDeleteData(null)}
        onSuccess={refetch}
        data={deleteData}
        endPoint={`/admin/shop/items/${deleteData?.id}`}
        itemName="item"
      />
    </div>
  );
};

export default Emoticons;
