'use client';
import BasicTablePage from '@/components/form/BasicTablePage';
import useDataFetch from '@/hooks/useDataFetch';
import React, { use, useState } from 'react';
import { format } from 'date-fns';
import api from '@/lib/api';
import { useRouter } from 'next/navigation';
import DeleteModal from '@/components/form/modal/DeleteModal';

const Promotions = () => {
  const router = useRouter();
  const [page, setPage] = useState(1);
  const [limit] = useState(10);
  const [deleteData, setDeleteData] = useState(null);

  const { data, isLoading, refetch } = useDataFetch({
    queryKey: ['promotions', page, limit],
    endPoint: '/promotions/admin',
    params: { page, limit },
  });
  const promotions = data?.items || [];

  const tableData = promotions?.map((item, index) => {
    return {
      index: (page - 1) * limit + index + 1,
      name: item?.name,
      discountType: item?.discountType,
      startDate: format(new Date(item?.startDate), 'MMM dd, yyyy hh:mm aa'),
      endDate: format(new Date(item?.endDate), 'MMM dd, yyyy hh:mm aa'),
      status: item?.isActive ? (
        <span className="text-green-600 px-5 py-1.5 rounded bg-green-100 shadow">
          Active
        </span>
      ) : (
        <span className="text-red-600 px-5 rounded bg-red-100 shadow">
          Inactive
        </span>
      ),
    };
  });

  const columns = [
    {
      label: '#',
      field: 'index',
    },
    {
      label: 'Promotion Name',
      field: 'name',
    },
    {
      label: 'Discount Type',
      field: 'discountType',
    },
    {
      label: 'Start Date',
      field: 'startDate',
    },
    {
      label: 'End Date',
      field: 'endDate',
    },
    {
      label: 'Active Status',
      field: 'status',
    },
    {
      label: 'Action',
      field: '',
    },
  ];

  const actions = [
    {
      name: 'edit',
      icon: 'material-symbols:edit-outline',
      className: 'text-gray-600',
      onClick: (val) => {
        router.push(`/dashboard/promotions/edit/${promotions[val]?.id}`);
      },
    },
    {
      name: 'delete',
      icon: 'heroicons-outline:trash',
      className: 'text-red-600',
      onClick: (val) => {
        setDeleteData(promotions[val]);
      },
    },
  ];

  return (
    <div>
      <BasicTablePage
        title={'Promotions'}
        createButton={'Add Promotion'}
        createBtnLink={'/dashboard/promotions/add'}
        actions={actions}
        columns={columns}
        data={tableData}
        loading={isLoading}
        currentPage={page}
        changePage={setPage}
        totalItems={data?.totalCount || 0}
        rowsPerPage={limit}
      />

      <DeleteModal
        isOpen={!!deleteData}
        onClose={() => setDeleteData(null)}
        onSuccess={refetch}
        data={deleteData}
        endPoint={`/promotions/${deleteData?.id}`}
        itemName="promotion"
      />
    </div>
  );
};

export default Promotions;
