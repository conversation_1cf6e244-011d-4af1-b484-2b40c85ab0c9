'use client';
import { ButtonIcon } from '@/components/Button';
import CommonQnA from '@/components/CommonQnA';
import <PERSON><PERSON>iewer from '@/components/EditorViewer';
import GoBack from '@/components/shared/GoBack';
import useDataFetch from '@/hooks/useDataFetch';
import Image from 'next/image';
import { useParams, useRouter } from 'next/navigation';
import React, { useState } from 'react';

const StoryDetails = () => {
  const { id } = useParams();
  const router = useRouter();

  const { data: storyDetails, isLoading } = useDataFetch({
    queryKey: ['story-details', id],
    endPoint: `/play/story-maker/play/${id}`,
  });

  return (
    <div className="max-w-7xl mx-auto p-5 xl:px-0">
      <GoBack title={'HEC Play'} linkClass="my-5 w-full max-w-40" />
      <div className="bg-[#D9FFFF] p-4 rounded-lg">
        <div
          key={storyDetails?.id}
          className="bg-[#FFF9FB] rounded-lg shadow-lg p-4 transition-all duration-300 flex items-center gap-5 border border-gray-100 relative"
        >
          <div className="absolute -right-10 -top-10">
            <Image
              src={'/assets/images/all-img/butterfly.png'}
              alt="Frame"
              width={80}
              height={80}
              className=""
            />
          </div>

          <div className="relative">
            <Image
              src={
                storyDetails?.picture || '/assets/images/all-img/noImage.png'
              }
              alt={storyDetails?.title}
              height={300}
              width={300}
              className="rounded-lg object-cover max-h-60"
            />
          </div>

          <div className="text-gray-600">
            <h4 className="font-medium text-yellow-700 text-2xl ">
              {storyDetails?.title}
            </h4>
            <EditorViewer
              data={storyDetails?.description || storyDetails?.instruction || 'This is a dummy description'}
            />

            <button
              onClick={() => router.push(`/story-maker/${id}/submission`)}
              className="flex items-center gap-2 border border-yellow-800 text-2xl text-yellow-900 bg-gradient-to-b from-[#DCA600] to-[#FFDE5B] px-10 py-1 rounded-full mt-3"
            >
              Go{' '}
              <ButtonIcon
                icon={'tabler:arrow-right'}
                innerBtnCls={'h-8 w-8 '}
                btnIconCls={'h-4 w-4'}
              />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StoryDetails;
