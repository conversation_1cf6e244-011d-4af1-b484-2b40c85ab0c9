/**
 * Test utility for simulating token expiration
 * This is for development/testing purposes only
*/

import Cookies from 'js-cookie';
import { handleTokenExpiration } from './authUtils';

/**
 * Simulate token expiration by setting an expired token
 * This will trigger the automatic logout flow on the next API call
 */
export const simulateTokenExpiration = () => {
  if (process.env.NODE_ENV !== 'development') {
    console.warn('simulateTokenExpiration should only be used in development');
    return;
  }
  
  // Set an obviously expired token
  Cookies.set('token', 'expired_token_for_testing', { expires: -1 });
  console.log('Token expiration simulated. Next API call will trigger automatic logout.');
};

/**
 * Manually trigger the token expiration handler
 * This immediately logs out the user without making an API call
 */
export const triggerTokenExpirationHandler = () => {
  if (process.env.NODE_ENV !== 'development') {
    console.warn('triggerTokenExpirationHandler should only be used in development');
    return;
  }
  
  console.log('Manually triggering token expiration handler...');
  handleTokenExpiration();
};

// Make these functions available in the browser console for testing
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  window.simulateTokenExpiration = simulateTokenExpiration;
  window.triggerTokenExpirationHandler = triggerTokenExpirationHandler;
  console.log('Token expiration test utilities loaded. Use:');
  console.log('- window.simulateTokenExpiration() to set expired token');
  console.log('- window.triggerTokenExpirationHandler() to manually trigger logout');
}
