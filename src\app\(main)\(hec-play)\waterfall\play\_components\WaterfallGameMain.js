'use client';
import React, { useState, useEffect } from 'react';
import {
  DndContext,
  rectIntersection,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragOverlay,
} from '@dnd-kit/core';
import {
  restrictToWindowEdges,
  restrictToParentElement,
} from '@dnd-kit/modifiers';
import { Icon } from '@iconify/react';
import Image from 'next/image';
import {
  DraggableOption,
  DroppableBlank,
  DragOverlayContent,
  FeedbackComponent,
} from './WaterfallGame2';
import api from '@/lib/api';
import GameSummary2 from './GameSummary2';
import Button from '@/components/Button';
import { toast } from 'sonner';
import { useWaterfallTimer } from './hooks/useWaterfallTimer';
import { createAnswerData, updateAnswersArray } from './utils/answerUtils';
import TimeExpiredModal from './TimeExpiredModal';

const WaterfallGameMain = ({ initialData, refetchGame }) => {
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [score, setScore] = useState(0);
  const [gameCompleted, setGameCompleted] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [userAnswers, setUserAnswers] = useState([]);
  const [submitResult, setSubmitResult] = useState(null);
  const [error, setError] = useState(null);

  // Game state
  const [blanks, setBlanks] = useState([]);
  const [options, setOptions] = useState([]);
  const [activeId, setActiveId] = useState(null);
  const [activeOption, setActiveOption] = useState(null);
  const [showFeedback, setShowFeedback] = useState(false);
  const [isCorrect, setIsCorrect] = useState(false);
  const [hasChecked, setHasChecked] = useState(false);
  const [submissionInitiated, setSubmissionInitiated] = useState(false);
  const [showTimeExpiredModal, setShowTimeExpiredModal] = useState(false);

  const questions = initialData?.questions || [];
  const setId = initialData?.id || '';

  // Handle timer expiration - defined before hook
  const handleTimeExpired = () => {
    // Prevent multiple calls
    if (timerExpired || gameCompleted || isSubmitting || submissionInitiated)
      return;

    // Show the time expired modal instead of auto-submitting
    setShowTimeExpiredModal(true);
  };

  // Get current question's time limit - no default, null means no time limit
  const currentTimeLimit =
    initialData?.questions[currentQuestionIndex]?.time_limit_in_seconds;

  // Check if current question has a time limit
  const hasTimeLimit =
    currentTimeLimit !== null &&
    currentTimeLimit !== undefined &&
    currentTimeLimit > 0;

  // Timer hook - only use when there's a time limit
  const {
    timeLeft,
    timerExpired,
    gameStarted,
    resetTimer,
    restartCurrentTimer,
    formatTimeDigital,
  } = useWaterfallTimer(
    hasTimeLimit ? currentTimeLimit : null,
    handleTimeExpired
  );

  // console.log('Timer state:', {
  //   currentQuestionIndex,
  //   currentTimeLimit,
  //   hasTimeLimit,
  //   timeLeft,
  //   timerExpired,
  //   gameStarted
  // });

  
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor)
  );

  // Custom modifier to prevent dragged items from overlapping with waterfall container
  const preventWaterfallOverlap = ({ transform, draggingNodeRect, over }) => {
    if (!draggingNodeRect) return transform;

    // If not over a valid drop target, ensure the item doesn't overlap with waterfall container
    if (!over || !over.id.startsWith('blank-')) {
      const waterfallContainer = document.getElementById('options-container');
      if (waterfallContainer) {
        const containerRect = waterfallContainer.getBoundingClientRect();
        const itemBottom =
          draggingNodeRect.top + transform.y + draggingNodeRect.height;

        // If the dragged item would overlap with the waterfall container, adjust its position
        if (
          itemBottom > containerRect.top &&
          draggingNodeRect.top + transform.y < containerRect.bottom
        ) {
          return {
            ...transform,
            y: containerRect.bottom - draggingNodeRect.top + 10, // 10px buffer
          };
        }
      }
    }

    return transform;
  };

  // Handle Time Expired Modal actions
  const handlePlayAgain = () => {
    // Reset the current question state
    setShowTimeExpiredModal(false);

    // Reset blanks and options for current question
    const question = questions[currentQuestionIndex];
    const blankCount = (
      question.question_text_plain.match(/\[\[gap\]\]/g) || []
    ).length;
    setBlanks(Array(blankCount).fill(null));
    setOptions(
      question.options.map((option, index) => ({
        id: `option-${index}`,
        text: option,
        used: false,
      }))
    );

    // Reset question state
    setShowFeedback(false);
    setIsCorrect(false);
    setHasChecked(false);

    // Timer will auto-restart when restartCurrentTimer is called
    if (hasTimeLimit) {
      restartCurrentTimer();
    }
  };

  const handleSkipQuestion = () => {
    setShowTimeExpiredModal(false);

    // Create empty answer data for skipped question
    const currentQuestion = questions[currentQuestionIndex];
    const answerData = createAnswerData(currentQuestion, []);
    if (answerData) {
      const updatedAnswers = updateAnswersArray(userAnswers, answerData);
      setUserAnswers(updatedAnswers);
    }

    // Move to next question or finish game
    proceedToNextQuestion();
  };

  // Initialize blanks and options for the current question
  useEffect(() => {
    if (questions.length > 0 && currentQuestionIndex < questions.length) {
      const question = questions[currentQuestionIndex];

      // Count the number of blanks in the question
      const blankCount = (
        question.question_text_plain.match(/\[\[gap\]\]/g) || []
      ).length;

      // Initialize blanks array with empty values
      setBlanks(Array(blankCount).fill(null));

      // Initialize options array with the provided options
      setOptions(
        question.options.map((option, index) => ({
          id: `option-${index}`,
          text: option,
          used: false,
        }))
      );

      // Reset state for new question
      setShowFeedback(false);
      setIsCorrect(false);
      setHasChecked(false);
      setShowTimeExpiredModal(false);

      // Timer will auto-start when hasTimeLimit is true due to initialTime change
      console.log('Question changed:', {
        questionIndex: currentQuestionIndex,
        timeLimit: currentTimeLimit,
        hasTimeLimit
      });
    }
  }, [questions, currentQuestionIndex]);

  // Handle drag start
  const handleDragStart = (event) => {
    const { active } = event;
    setActiveId(active.id);

    if (active.id.startsWith('option-')) {
      const optionIndex = parseInt(active.id.split('-')[1]);
      setActiveOption(options[optionIndex].text);
    }
  };

  // Handle drag end
  const handleDragEnd = (event) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      // If dropping onto a blank
      if (over.id.startsWith('blank-')) {
        const blankIndex = parseInt(over.id.split('-')[1]);
        const newBlanks = [...blanks];

        // If dragging from options
        if (active.id.startsWith('option-')) {
          const optionIndex = parseInt(active.id.split('-')[1]);
          const newOptions = [...options];

          // Check if the blank already has a value
          const currentBlankValue = newBlanks[blankIndex];

          // If the blank already has a value, free up that option first
          if (currentBlankValue !== null) {
            const currentOptionIndex = newOptions.findIndex(
              (opt) => opt.text === currentBlankValue
            );

            if (currentOptionIndex !== -1) {
              newOptions[currentOptionIndex] = {
                ...newOptions[currentOptionIndex],
                used: false,
              };
            }
          }

          // Update the blank with the option text
          newBlanks[blankIndex] = newOptions[optionIndex].text;

          // Mark the option as used
          newOptions[optionIndex] = {
            ...newOptions[optionIndex],
            used: true,
          };

          setOptions(newOptions);
          setBlanks(newBlanks);

          // Reset feedback when making changes
          if (hasChecked) {
            setShowFeedback(false);
            setHasChecked(false);
          }
        }
      }
    }

    setActiveId(null);
    setActiveOption(null);
  };

  // Reset a single blank and return its option to the available options
  const handleResetBlank = (index) => {
    // Get the value of the blank being reset
    const blankValue = blanks[index];

    if (blankValue) {
      // Create a new blanks array with the specified blank set to null
      const newBlanks = [...blanks];
      newBlanks[index] = null;
      setBlanks(newBlanks);

      // Find the option that matches this value and mark it as unused
      const newOptions = [...options];
      const optionIndex = newOptions.findIndex(
        (opt) => opt.text === blankValue
      );

      if (optionIndex !== -1) {
        newOptions[optionIndex] = {
          ...newOptions[optionIndex],
          used: false,
        };
        setOptions(newOptions);
      }

      // Reset feedback when making changes
      if (hasChecked) {
        setShowFeedback(false);
        setHasChecked(false);
      }
    }
  };

  // Check answers
  const handleCheckAnswers = (autoProceed = false) => {
    const currentQuestion = questions[currentQuestionIndex];

    // If no blanks are filled, just go to next question
    if (blanks.every((blank) => blank === null)) {
      if (autoProceed) {
        proceedToNextQuestion();
      }
      return;
    }

    // Check if all blanks are filled
    const allFilled = blanks.every((blank) => blank !== null);
    if (!allFilled) {
      // Show a message that all blanks need to be filled
      toast.message('Please fill all the blanks before checking your answer.');
      return;
    }

    // Create answer data and save it
    const answerData = createAnswerData(currentQuestion, blanks);
    if (answerData) {
      setIsCorrect(answerData.is_correct);
      setShowFeedback(true);
      setHasChecked(true);

      // Update user answers
      const updatedAnswers = updateAnswersArray(userAnswers, answerData);
      setUserAnswers(updatedAnswers);

      // Award points if correct
      if (answerData.is_correct) {
        setScore(score + 10);
      }
    }

    // If auto-proceed is enabled, automatically move to next question after a short delay
    if (autoProceed) {
      setTimeout(() => {
        proceedToNextQuestion();
      }, 500); // 0.5 second delay to show feedback
    }
  };

  // Handle next question with validation
  const handleNextQuestion = () => {
    // Check if any blanks are filled
    const hasAnyAnswer = blanks.some((blank) => blank !== null);

    if (!hasAnyAnswer) {
      toast.error('Please complete the sentences to finish.');
      return;
    }

    // If answers are filled but not checked, check them first and auto-proceed
    if (!hasChecked) {
      handleCheckAnswers(true); // Pass true to indicate auto-proceed
      return;
    }

    // Move to next question or finish
    proceedToNextQuestion();
  };

  // Proceed to next question or finish game
  const proceedToNextQuestion = () => {
    if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
    } else {
      // Last question - ensure answer is saved before submitting
      const currentQuestion = questions[currentQuestionIndex];

      // If there are filled blanks and not yet saved, save them first
      if (blanks.some(blank => blank !== null) && !hasChecked) {
        const answerData = createAnswerData(currentQuestion, blanks);
        if (answerData) {
          const updatedAnswers = updateAnswersArray(userAnswers, answerData);
          // Submit with the updated answers including the last question
          handleSubmitAnswers(updatedAnswers);
          return;
        }
      }

      // Prevent duplicate submissions
      if (!submissionInitiated && !isSubmitting && !gameCompleted) {
        setSubmissionInitiated(true);
        handleSubmitAnswers();
      }
    }
  };

  // Handle submit answers
  const handleSubmitAnswers = async (answersToSubmit = null) => {
    // Prevent duplicate submissions
    if (isSubmitting || gameCompleted) return;

    const finalAnswers = answersToSubmit || userAnswers;

    console.log('Submitting answers:', {
      total_questions: questions.length,
      finalAnswers_count: finalAnswers.length,
      finalAnswers,
      userAnswers
    });

    // if (finalAnswers.length === 0) return;

    setIsSubmitting(true);
    setSubmissionInitiated(true);

    try {
      const response = await api.post('/play/waterfall/submit', {
        set_id: setId,
        answers: finalAnswers.map((a) => ({
          question_id: a.question_id,
          answers: a.answers,
        })),
      });

      console.log('Submit response:', response.data);
      setSubmitResult(response.data);
      setGameCompleted(true);
    } catch (err) {
      setError(err.message || 'Failed to submit answers');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Restart game
  const handleRestartGame = () => {
    // If refetchGame function is provided, call it to get new questions
    if (typeof refetchGame === 'function') {
      // Refetch new game data from the API
      refetchGame();
    }

    // Reset all game state
    setCurrentQuestionIndex(0);
    setScore(0);
    setGameCompleted(false);
    setUserAnswers([]);
    setSubmitResult(null);
    setError(null);
    setShowFeedback(false);
    setIsCorrect(false);
    setHasChecked(false);
    setSubmissionInitiated(false);
    setShowTimeExpiredModal(false);

    // Reset timer state
    resetTimer();
  };

  // Render the current question
  const renderCurrentQuestion = () => {
    if (!questions.length) return null;

    const currentQuestion = questions[currentQuestionIndex];
    const isLastQuestion = currentQuestionIndex === questions.length - 1;

    let gapIndex = 0;
    const correctAnswer = currentQuestion?.question_text_plain.replace(
      /\[\[gap\]\]/g,
      () => {
        const userAnswer = blanks[gapIndex] || '';
        const correctAnswerText =
          currentQuestion.correct_answers[gapIndex] || '';
        const isAnswerCorrect = userAnswer === correctAnswerText;

        // Choose background color based on whether the answer is correct
        const bgColor = isAnswerCorrect ? 'bg-green-100' : 'bg-red-100';
        const borderColor = isAnswerCorrect
          ? 'border-green-200'
          : 'border-red-200';

        gapIndex++;
        return `<span class="text-gray-600 font-semibold ${bgColor} border-2 ${borderColor} rounded px-1.5 py-0.5">${correctAnswerText}</span> `;
      }
    );
    // Split the question text by the [[gap]] markers
    const parts = currentQuestion?.question_text_plain.split(/\[\[gap\]\]/g);

    return (
      <div className="relative min-h-[calc(100vh-200px)] space-y-6 sm:space-y-10 z-10">
        <div className="flex justify-between items-start">
          <div className="w-full px-2 sm:px-4 md:px-0">
            <div className="flex flex-col sm:flex-row items-end sm:items-center justify-between mb-5">
              <h1 className="text-lg font-medium">
                Drag and drop blocks falling from the sky to match the words in
                the given sentence
              </h1>

              <div className="flex items-center gap-5">
                {initialData?.questions[currentQuestionIndex]?.level && (
                  <div className="flex flex-col items-center space-x-1 bg-yellow-200 px-3 py-1 border border-orange-400/30 backdrop-blur-sm rounded">
                    <span className="ml-2 text-2xl font-bold text-yellow-400 uppercase tracking-wide text-stroke">
                      Level
                    </span>
                    <div className="flex items-center">
                      {[...Array(3)].map((_, i) => (
                        <Icon
                          key={i}
                          icon="mdi:star"
                          className={`w-7 h-7 drop-shadow-sm ${
                            i <
                            initialData?.questions[currentQuestionIndex]?.level
                              ? 'text-orange-400'
                              : 'text-gray-400/50'
                          }`}
                        />
                      ))}
                    </div>
                  </div>
                )}

                {/* Digital Timer - only show if there's a time limit */}
                {hasTimeLimit && (
                  <div className=" flex justify-center">
                    <div
                      className={`inline-flex items-center p-1 sm:p-2.5 rounded sm:text-5xl font-mono font-bold border sm:border-2 transition-colors duration-300 border-yellow-500 text-yellow-600
                        `}
                    >
                      <span
                        className={`px-2 rounded-lg backdrop-blur-sm mx-1 transition-colors duration-300 bg-yellow-200/90`}
                      >
                        {formatTimeDigital(timeLeft).minutes}
                      </span>
                      <span
                        className={`mx-1 transition-colors duration-300 text-yellow-500`}
                      >
                        :
                      </span>
                      <span
                        className={`px-2 rounded-lg backdrop-blur-sm mx-1 transition-colors duration-300 bg-yellow-200/90`}
                      >
                        {formatTimeDigital(timeLeft).seconds}
                      </span>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Waterfall container */}
            <div className="relative min-h-[200px] sm:min-h-[220px] md:min-h-[250px] mb-8 rounded-lg border z-30 overflow-hidden bg-gradient-to-b from-white to-blue-100">
              <div id="options-container" className="w-full h-full relative">
                {options?.map(
                  (option, index) =>
                    !option.used && (
                      <DraggableOption
                        key={option.id}
                        id={option.id}
                        option={option.text}
                        isActive={activeId === option.id}
                        index={index}
                        totalOptions={options.filter((o) => !o.used).length}
                      />
                    )
                )}
              </div>
            </div>

            {/* Question with blanks */}
            <div className="text-sm sm:text-base md:text-lg mb-8 sm:mb-12 flex flex-wrap gap-1 sm:gap-2 items-center leading-relaxed">
              {parts?.map((part, index) => (
                <React.Fragment key={index}>
                  <span className="break-words">{part}</span>
                  {index < parts.length - 1 && (
                    <DroppableBlank
                      id={`blank-${index}`}
                      value={blanks[index]}
                      index={index}
                      onReset={handleResetBlank}
                      correctAnswers={currentQuestion.correct_answers}
                    />
                  )}
                </React.Fragment>
              ))}
            </div>

            {/* Next/Finish button */}
            <div className="flex justify-center mt-8">
              <button
                onClick={handleNextQuestion}
                className="bg-yellow-400 hover:bg-yellow-500 text-black px-6 py-2 rounded-full flex items-center disabled:bg-gray-300 disabled:cursor-not-allowed"
                disabled={isSubmitting || showTimeExpiredModal}
              >
                {isLastQuestion ? 'Finish' : 'Next'}
                <Icon
                  icon="material-symbols:arrow-forward-rounded"
                  className="ml-1"
                />
              </button>
            </div>
          </div>
        </div>
        {/* 
        <FeedbackComponent
          button={
            <Button
              onClick={handleNextQuestion}
              disabled={isSubmitting || timerExpired}
              className="ml-4"
              icon={'material-symbols:arrow-forward-rounded'}
              buttonText={isLastQuestion ? 'Finish' : 'Next'}
            />
          }
          isCorrect={isCorrect}
          showFeedback={showFeedback}
          correctAnswer={correctAnswer}
          onContinue={handleNextQuestion}
        /> */}
      </div>
    );
  };

  // If game is completed, show summary
  if (gameCompleted && submitResult) {
    return (
      <GameSummary2
        resultData={submitResult?.data || submitResult}
        questionsData={questions}
        onRestart={handleRestartGame}
      />
    );
  }

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={rectIntersection}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
      modifiers={[restrictToWindowEdges, preventWaterfallOverlap]}
    >
      <div className="max-w-7xl mx-auto h-full ">
        {renderCurrentQuestion()}

        <div className="absolute right-0 bottom-0 z-0 pointer-events-none">
          <Image
            src={'/assets/images/all-img/cat2.png'}
            alt={'cat'}
            width={400}
            height={500}
            className="max-w-[190px] sm:max-w-[550px] h-auto opacity-80"
          />
        </div>

        {/* Drag overlay */}
        <DragOverlay style={{ zIndex: 9999 }}>
          {activeOption ? <DragOverlayContent option={activeOption} /> : null}
        </DragOverlay>

        {/* Time Expired Modal */}
        <TimeExpiredModal
          isOpen={showTimeExpiredModal}
          onSkip={handleSkipQuestion}
          onPlayAgain={handlePlayAgain}
          currentQuestion={currentQuestionIndex + 1}
          totalQuestions={questions.length}
        />
      </div>
    </DndContext>
  );
};

export default WaterfallGameMain;
