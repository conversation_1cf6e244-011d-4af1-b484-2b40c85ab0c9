import React from 'react';
import { Icon } from '@iconify/react';

const StatsCard = ({
  title,
  value,
  subtitle,
  icon,
  bgColor = 'bg-white',
  iconBgColor = 'bg-gray-100',
  iconColor = 'text-gray-600',
  textColor = 'text-gray-900',
  subtitleColor = 'text-gray-700',
  className = '',
  firstShapeColor,
}) => {
  return (
    <div
      className={`p-4 ${className} relative`}
    >
      <div className="flex items-center justify-between">
        <div className="flex-1 z-20">
          <h3 className={`text-lg font-medium ${subtitleColor} mb-1`}>
            {title}
          </h3>
          {typeof value === 'object' ? (
            <div className="flex items-center gap-3">
              <p>
                <span
                  style={{ color: textColor }}
                  className={`text-3xl font-bold block`}
                >
                  {value.present.count}
                </span>
                <span className={`text-sm ${subtitleColor} ml-1`}>
                  {value.present.subtitle}
                </span>
              </p>
              <p>
                <span
                  style={{ color: textColor }}
                  className={`text-3xl font-bold block`}
                >
                  {value.absent.count}
                </span>
                <span className={`text-sm ${subtitleColor} ml-1`}>
                  {value.absent.subtitle}
                </span>
              </p>
            </div>
          ) : (
            <p style={{ color: textColor }} className={`text-3xl font-bold`}>
              {value}
            </p>
          )}
        </div>
        <div
          style={{ backgroundColor: firstShapeColor }}
          className={`w-10 h-10 rounded-full bg-orange-400 absolute -right-3 -top-3 z-10`}
        ></div>
        <div
          style={{ backgroundColor: firstShapeColor }}
          className={`w-12 h-12 rounded-full bg-orange-300 absolute -right-3 -top-3 z-0`}
        ></div>
      </div>
    </div>
  );
};

export default StatsCard;
