'use client';
import React, { useState, useRef } from 'react';
import { Icon } from '@iconify/react';
import Image from 'next/image';
import { useParams, useRouter } from 'next/navigation';
import { format } from 'date-fns';
import useDataFetch from '@/hooks/useDataFetch';
import api from '@/lib/api';
import { toast } from 'react-hot-toast';
import GoBack from '@/components/shared/GoBack';
import RegularGoBack from '@/components/shared/RegularGoBack';
import AdminEditForm from '../_components/AdminEditForm';
import { useDispatch, useSelector } from 'react-redux';
import { loginSuccess } from '@/store/features/authSlice';

const AdminProfile = () => {
  const disPatch = useDispatch();
  const { id } = useParams();
  const [selectedFile, setSelectedFile] = useState(null);
  const [preview, setPreview] = useState(null);
  const [isUploading, setIsUploading] = useState(false);
  const [activeTab, setActiveTab] = useState('account'); // Default tab is account
  const fileInputRef = useRef(null);
  const [isEditing, setIsEditing] = useState(false);
  const { user, token } = useSelector((state) => state.auth);


  const {
    data: response,
    isLoading,
    error,
    refetch,
  } = useDataFetch({
    queryKey: ['admin-profile', id],
    endPoint: `/users/profile`,
  });

  const adminProfile = response?.data || response;

  const handleFileSelect = (e) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Check if file type is allowed (only jpeg, png, gif)
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
    if (!allowedTypes.includes(file.type)) {
      toast.error('Please select a JPEG, PNG, or GIF image file');
      return;
    }

    if (file.size > 5 * 1024 * 1024) {
      toast.error('File size must be less than 5MB');
      return;
    }

    setSelectedFile(file);
    const reader = new FileReader();
    reader.onloadend = () => setPreview(reader.result);
    reader.readAsDataURL(file);
  };

  const handleUpload = async () => {
    if (!selectedFile) return;

    try {
      setIsUploading(true);
      const formData = new FormData();
      formData.append('file', selectedFile);

      const response = await api.post('/users/profile/picture', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      const imageUrl = response?.data?.profilePictureUrl;

      disPatch(
        loginSuccess({
          user: { ...user, profilePictureUrl: imageUrl },
          token: token,
        })
      );

      setSelectedFile(null);
      setPreview(null);
      refetch();
    } catch (error) {
      console.error('Error uploading image:', error);
    } finally {
      setIsUploading(false);
    }
  };

  const cancelUpload = () => {
    setSelectedFile(null);
    setPreview(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-[60vh]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-yellow-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <Icon
          icon="mdi:alert-circle"
          className="text-6xl text-red-500 mx-auto mb-4"
        />
        <h2 className="text-2xl font-bold text-gray-800 mb-2">
          Error Loading Profile
        </h2>
        <p className="text-gray-600 mb-6">
          {error.message || 'There was an error loading the admin profile.'}
        </p>
        <RegularGoBack title={'Admin Details'} className={'max-w-52 mb-5'} />
      </div>
    );
  }

  return (
    <div className="min-h-[80vh] overflow-y-auto max-w-5xl mx-auto">
      {/* Header */}
      <RegularGoBack title={'Admin Details'} className={'max-w-52 mb-5'} />

      {/* Profile Card */}
      <div className="max-w-5xl mx-auto">
        <div className="bg-white rounded-lg shadow-md border p-6">
          {/* Profile Header - Only visible when activeTab is 'account' */}
          <div className="flex items-start justify-between mb-6">
            <div className="flex items-center gap-6">
              <div className="relative">
                {/* Profile Image - Increased size */}
                <div className="relative h-24 w-24 rounded-full bg-gray-200 overflow-hidden border-2 border-gray-100">
                  {preview ? (
                    <Image
                      src={preview}
                      alt="Preview"
                      fill
                      className="object-cover"
                    />
                  ) : adminProfile?.profilePictureUrl ||
                    adminProfile?.profilePicture ? (
                    <Image
                      src={
                        adminProfile.profilePictureUrl ||
                        adminProfile.profilePicture
                      }
                      alt={adminProfile.name || 'Admin'}
                      onError={() =>
                        setPreview('/assets/images/all-img/avatar.png')
                      }
                      fill
                      className="object-cover"
                      unoptimized={true}
                    />
                  ) : (
                    <Image
                      src="/assets/images/all-img/avatar.png"
                      alt={adminProfile.name || 'Admin'}
                      fill
                      className="object-cover"
                    />
                  )}
                </div>

                {/* Upload Controls - Gray color */}
                {isEditing && (
                  <>
                    {!selectedFile ? (
                      <label
                        htmlFor="profileImage"
                        className="absolute -bottom-1 -right-1 bg-white border-2 border-gray-200 p-2 rounded-full shadow-md cursor-pointer hover:bg-gray-50 transition-colors"
                      >
                        <Icon
                          icon="solar:camera-bold"
                          className="w-5 h-5 text-gray-600"
                        />
                        <input
                          type="file"
                          id="profileImage"
                          ref={fileInputRef}
                          accept="image/jpeg,image/png,image/gif"
                          className="hidden"
                          onChange={handleFileSelect}
                        />
                      </label>
                    ) : (
                      <div className="absolute -bottom-1 -right-0 flex gap-1">
                        <button
                          onClick={cancelUpload}
                          className="bg-gray-400 p-1 rounded-full shadow-md cursor-pointer hover:bg-gray-500 transition-colors"
                        >
                          <Icon
                            icon="material-symbols:close"
                            className="w-4 h-4 text-white"
                          />
                        </button>
                        <button
                          onClick={handleUpload}
                          disabled={isUploading}
                          className="bg-yellow-500 p-1 rounded-full shadow-md cursor-pointer hover:bg-yellow-600 disabled:bg-yellow-300 transition-colors"
                        >
                          <Icon
                            icon={
                              isUploading
                                ? 'eos-icons:loading'
                                : 'mdi:content-save-check-outline'
                            }
                            className="w-4 h-4 text-white"
                          />
                        </button>
                      </div>
                    )}
                  </>
                )}
              </div>

              <div className="">
                <h1 className="text-xl font-semibold text-gray-900">
                  {adminProfile.name || 'Admin Full Name'}
                </h1>
                <p className="text-gray-600 text-sm mt-1">
                  User Id: {adminProfile.userId || 'admin'}
                </p>
              </div>
            </div>

            {!isEditing && (
              <button
                type="button"
                onClick={() => setIsEditing(!isEditing)}
                className="p-2 hover:bg-orange-100 rounded-xl border border-gray-300"
              >
                <Icon
                  icon={
                    isEditing
                      ? 'material-symbols:close-rounded'
                      : 'material-symbols:edit-outline'
                  }
                  className="w-5 h-5"
                />
              </button>
            )}
          </div>

          {/* Tabs */}
          <div className="border-b mb-6">
            <div className="flex">
              <button
                className={`px-4 py-2 text-sm font-medium text-amber-600 border-b-2 border-amber-600`}
                onClick={() => setActiveTab('account')}
              >
                Account Information
              </button>
            </div>
          </div>

          {/* Tab Content */}
          {isEditing ? (
            <AdminEditForm
              editValue={adminProfile}
              setIsEditing={setIsEditing}
            />
          ) : (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
              {/* Personal Details - Left Column */}
              <div>
                <h3 className="text-base font-medium mb-6 text-amber-800">
                  Personal Details
                </h3>
                <div className="space-y-6">
                  <div>
                    <label className="text-xs font-bold uppercase tracking-wide block mb-2">
                      Biography
                    </label>
                    <p className="text-sm text-gray-800">
                      {adminProfile.bio || 'Not available'}
                    </p>
                  </div>
                  <div>
                    <label className="text-xs font-bold uppercase tracking-wide block mb-2">
                      Age
                    </label>
                    <p className="text-sm text-gray-800">
                      {adminProfile.dateOfBirth
                        ? format(
                            new Date(adminProfile.dateOfBirth),
                            'MMM dd, yyyy'
                          )
                        : 'Not available'}
                    </p>
                  </div>
                  <div>
                    <label className="text-xs font-bold uppercase tracking-wide block mb-2">
                      Gender
                    </label>
                    <p className="text-sm text-gray-800 capitalize">
                      {adminProfile.gender || 'Not available'}
                    </p>
                  </div>
                  {/* <div>
                    <label className="text-xs font-bold uppercase tracking-wide block mb-2">
                      Blood Group
                    </label>
                    <p className="text-sm text-gray-800">
                      {adminProfile.bloodGroup || 'Not available'}
                    </p>
                  </div> */}
                </div>
              </div>

              {/* Contact Details - Right Column */}
              <div className="relative">
                {/* Vertical Dotted Line */}
                <div className="absolute left-0 top-0 bottom-0 w-0.5 border-l-2 border-dotted border-gray-300 hidden lg:block"></div>

                <div className="lg:pl-12">
                  <h3 className="text-base font-medium text-amber-800 mb-6">
                    Contact Details
                  </h3>
                  <div className="space-y-6">
                    <div>
                      <label className="text-xs font-bold uppercase tracking-wide block mb-2">
                        Address
                      </label>
                      <p className="text-sm text-gray-800">
                        {adminProfile.address || 'Not available'}
                      </p>
                    </div>
                    <div>
                      <label className="text-xs font-bold uppercase tracking-wide block mb-2">
                        Phone Number
                      </label>
                      <p className="text-sm text-gray-800">
                        {adminProfile.phoneNumber || 'Not available'}
                      </p>
                    </div>
                    <div>
                      <label className="text-xs font-bold uppercase tracking-wide block mb-2">
                        Email
                      </label>
                      <p className="text-sm text-gray-800">
                        {adminProfile.email || 'Not available'}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AdminProfile;
