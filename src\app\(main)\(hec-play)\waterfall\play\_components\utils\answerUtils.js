// Check if answers are correct
export const checkAnswers = (filledBlanks, correctAnswers) => {
  if (!correctAnswers) return false;
  return filledBlanks.every((answer, index) => answer === correctAnswers[index]);
};

// Generate correct answer HTML
export const generateCorrectAnswerHTML = (questionText, correctAnswers) => {
  let gapIndex = 0;
  return questionText
    .replace(/\[\[gap\]\]/g, () => {
      return (
        `<span class="text-gray-600 font-semibold border bg-yellow-50 rounded px-1.5 py-0.5">${
          correctAnswers[gapIndex++]
        }</span>` + ' ' || ''
      );
    })
    .trim();
};

// Generate user answer text
export const generateUserAnswerText = (questionText, userAnswers) => {
  let userGapIndex = 0;
  return questionText
    .replace(/\[\[gap\]\]/g, () => {
      return userAnswers[userGapIndex++] || '';
    })
    .trim();
};

// Generate sentence with color-coded answers for summary
export const generateSummaryAnswerHTML = (questionText, userAnswers, correctAnswers) => {
  let gapIndex = 0;
  return questionText
    .replace(/\[\[gap\]\]/g, () => {
      const userAnswer = userAnswers[gapIndex] || '';
      const correctAnswer = correctAnswers[gapIndex] || '';
      const isCorrect = userAnswer === correctAnswer;

      gapIndex++;

      // Always show the correct answer
      if (isCorrect) {
        // User got it right - green background with correct answer
        return `<span class="px-2 py-1 rounded bg-green-100 text-green-800 font-medium border border-green-200">${correctAnswer}</span>` + ' ';
      } else {
        // User got it wrong - red background with correct answer
        return `<span class="px-2 py-1 rounded bg-red-100 text-red-800 font-medium border border-red-200">${correctAnswer}</span>` + ' ';
      }
    })
    .trim();
};

// Create answer data object
export const createAnswerData = (question, blanks) => {
  if (!question) {
    return null;
  }

  // Handle empty blanks by filling with empty strings
  const filledBlanks = blanks.map(blank => blank || '');

  const isAnswerCorrect = checkAnswers(filledBlanks, question.correct_answers);
  const correctAnswer = generateCorrectAnswerHTML(question.question_text_plain, question.correct_answers);
  const userAnswer = generateUserAnswerText(question.question_text_plain, filledBlanks);
  const summaryHTML = generateSummaryAnswerHTML(question.question_text_plain, filledBlanks, question.correct_answers);

  return {
    question_id: question.id,
    answers: [...filledBlanks],
    is_correct: isAnswerCorrect,
    correctAnswer: correctAnswer,
    userAnswer: userAnswer,
    summaryHTML: summaryHTML,
    question_text_plain: question.question_text_plain,
  };
};

// Update or add answer to answers array
export const updateAnswersArray = (currentAnswers, newAnswerData) => {
  const existingIndex = currentAnswers.findIndex(
    (a) => a.question_id === newAnswerData.question_id
  );

  if (existingIndex !== -1) {
    const updatedAnswers = [...currentAnswers];
    updatedAnswers[existingIndex] = newAnswerData;
    return updatedAnswers;
  } else {
    return [...currentAnswers, newAnswerData];
  }
};

// Generate summary HTML from server response data
export const generateSummaryFromServerData = (answer, questionData) => {
  // console.log('generateSummaryFromServerData called with:', { answer, questionData });

  if (!questionData || !answer) {
    // console.log('Missing data:', { hasQuestionData: !!questionData, hasAnswer: !!answer });
    return '';
  }

  const { submitted_answers, correct_answers } = answer;
  const questionText = questionData.question_text_plain;

  // console.log('Processing answer:', { submitted_answers, correct_answers, questionText });

  let gapIndex = 0;
  return questionText
    .replace(/\[\[gap\]\]/g, () => {
      const submittedAnswer = submitted_answers[gapIndex] || '';
      const correctAnswer = correct_answers[gapIndex] || '';

      // Check if this specific word/answer is correct by comparing submitted vs correct
      const isThisWordCorrect = submittedAnswer === correctAnswer;

      gapIndex++;

      // Always show the correct answer in the sentence
      if (isThisWordCorrect) {
        // User got this word right - green background with correct answer
        return `<span class="px-2 py-1 rounded bg-green-100 text-green-800 font-medium border border-green-200">${correctAnswer}</span>` + ' ';
      } else {
        // User got this word wrong - red background with correct answer (but showing what should be there)
        return `<span class="px-2 py-1 rounded bg-red-100 text-red-800 font-medium border border-red-200">${correctAnswer}</span>` + ' ';
      }
    })
    .trim();
};
