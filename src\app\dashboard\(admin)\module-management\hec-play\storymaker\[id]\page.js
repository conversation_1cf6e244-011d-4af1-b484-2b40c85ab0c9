'use client';
import useDataFetch from '@/hooks/useDataFetch';
import { useParams, useRouter } from 'next/navigation';
import React from 'react';
import Image from 'next/image';
import { Icon } from '@iconify/react';
import RegularGoBack from '@/components/shared/RegularGoBack';
import EditorViewer from '@/components/EditorViewer';

const StoryDetails = () => {
  const { id } = useParams();
  const router = useRouter();

  const { data: storyData, isLoading } = useDataFetch({
    queryKey: ['story-details', id],
    endPoint: `/play/story-maker/admin/stories/${id}`,
  });


  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-yellow-500"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4">
      {/* Back button with title */}
      {/* <RegularGoBack title={storyData?.title} className="mb-6" /> */}

      {/* Main content */}
      <div className="bg-gray-50 rounded-lg p-6">
        <div className="flex flex-col md:flex-row gap-8">
          {/* Left side - Image */}
          <div className="w-full md:w-1/3">
            <div className="mb-2">
              <span className="text-sm text-gray-600">Question Image</span>
            </div>
            <div className="bg-white border border-gray-200 rounded-lg overflow-hidden p-2">
              <Image
                src={storyData?.picture || "/assets/images/all-img/noImage.png"}
                alt={"Story image"}
                width={300}
                height={300}
                className="w-full h-auto object-contain"
              />
            </div>
          </div>

          {/* Right side - Details */}
          <div className="w-full md:w-2/3">
            {/* Title */}
            <div className="mb-4">
              <div className="mb-1">
                <span className="text-sm text-gray-600">Title</span>
              </div>
              <h2 className="text-lg font-medium">{storyData?.title}</h2>
            </div>

            {/* Instruction */}
            <div className="mb-4">
              <div className="mb-1">
                <span className="text-sm text-gray-600">Instruction</span>
              </div>
              <div className="prose max-w-none">
                <EditorViewer data={storyData?.instruction} />
              </div>
            </div>

            {/* Score and Word Limit */}
            <div className="flex items-center gap-4 text-gray-700 mt-6">
              <span className="font-medium">Score: {storyData?.score}</span>
              <span className="font-medium text-yellow-400">•</span>
              <span className="font-medium">Word Limit: {storyData?.word_limit}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StoryDetails;
