'use client';
import React, { useState, useEffect, useCallback, useRef } from 'react';
import Image from 'next/image';
import { Icon } from '@iconify/react';
import { toast } from 'sonner';
import Button from '@/components/Button';
import api from '@/lib/api';
import CalendarFilter from './modalContents/share/CalendarFilter';

const DiaryCover = ({ hasEntries, onOpen, onCoverPhotoChange }) => {
  const [modalOpen, setModalOpen] = useState(false);
  const [file, setFile] = useState(null);
  const [preview, setPreview] = useState(null);
  const [uploading, setUploading] = useState(false);
  const [showCalendar, setShowCalendar] = useState(false);
  const inputRef = useRef(null);

  const fetchCoverPhoto = useCallback(async () => {
    try {
      const { data } = await api.get('/diary/cover-photo');
      if (data?.fileUrl) onCoverPhotoChange?.(data.fileUrl);
    } catch {}
  }, [onCoverPhotoChange]);

  useEffect(() => {
    fetchCoverPhoto();
  }, [fetchCoverPhoto]);

  const selectFile = (e) => {
    const f = e.target.files?.[0];
    if (!f) return;
    if (!f.type.startsWith('image/')) {
      toast.error('Please select an image file');
      return;
    }
    if (f.size > 5 * 1024 * 1024) {
      toast.error('File size must be less than 5MB');
      return;
    }
    setFile(f);
    const reader = new FileReader();
    reader.onloadend = () => setPreview(reader.result);
    reader.readAsDataURL(f);
  };

  const upload = async () => {
    if (!file) {
      toast.error('Select a file first');
      return;
    }
    try {
      setUploading(true);
      const formData = new FormData();
      formData.append('coverPhoto', file);
      const { data } = await api.post('/diary/cover-photo', formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      });
      if (data?.fileUrl) {
        onCoverPhotoChange?.(data.fileUrl);
        toast.success('Cover photo updated');
        closeModal();
      }
    } catch {
      toast.error('Failed to upload cover photo');
    } finally {
      setUploading(false);
    }
  };

  const closeModal = () => {
    setModalOpen(false);
    setFile(null);
    setPreview(null);
    inputRef.current?.value && (inputRef.current.value = '');
  };

  return (
    <>
      <div className="absolute left-0 top-0 bottom-0 w-[60px] bg-[#1E3A8A]" />
      <div className="absolute top-6 right-6 flex items-center gap-3">
        <Button
          icon="mdi:palette-outline"
          buttonText="Change Cover"
          onClick={() => setModalOpen(true)}
        />

        {/* Calendar button for cover view - top right corner */}
        <div className="">
          <Button
            icon="material-symbols:calendar-month-outline"
            buttonText="Find Diary"
            onClick={() => setShowCalendar(!showCalendar)}
          />

          {showCalendar && (
            <div
              className={
                showCalendar
                  ? 'block absolute max-w-xl min-w-80 max-h-[400px] max-sm:max-h-[300px] overflow-y-auto bg-white border rounded-lg shadow-lg z-20 mt-8 top-0 right-0'
                  : 'hidden'
              }
            >
              <CalendarFilter
                onDateSelect={() => {
                  // The CalendarFilter component will update the Redux store
                  // This callback is for any additional actions needed
                  if (fetchTodayEntry) {
                    fetchTodayEntry();
                  }
                }}
                onClose={() => setShowCalendar(false)}
              />
            </div>
          )}
        </div>
      </div>
      <div
        className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 bg-white/30 backdrop-blur-lg rounded-2xl shadow-xl px-12 py-6 max-sm:ml-6 text-center min-w-[200px] border border-white/40 ring-1 ring-black/5 cursor-pointer"
        onClick={onOpen}
      >
        <h1 className="text-2xl font-bold text-[#1E3A8A]">My Diary</h1>
        {hasEntries ? (
          <p className="text-[#3B82F6] mt-1">Open</p>
        ) : (
          <p className="text-gray-500 mt-1">No entries yet</p>
        )}
      </div>
      {/* <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 text-xs text-gray-500 bg-white px-2 py-0.5 rounded-t-md">
        750 Hpx × 50 Wpx
      </div> */}
      {modalOpen && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-gray-800">
                Change Cover Photo
              </h3>
              <button
                onClick={closeModal}
                className="text-gray-500 hover:text-gray-700"
              >
                <Icon icon="mdi:close" className="w-6 h-6" />
              </button>
            </div>
            <div className="space-y-4">
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                {preview ? (
                  <div className="space-y-4">
                    <div className="relative w-full h-48 mx-auto">
                      <Image
                        src={preview}
                        alt="Preview"
                        fill
                        className="object-cover rounded-lg"
                      />
                    </div>
                    <p className="text-sm text-gray-600">{file?.name}</p>
                  </div>
                ) : (
                  <div className="space-y-2">
                    <Icon
                      icon="mdi:cloud-upload"
                      className="w-12 h-12 text-gray-400 mx-auto"
                    />
                    <p className="text-gray-600">Click to select an image</p>
                    {/* <p className="text-xs text-gray-500">PNG, JPG up to 5MB</p> */}
                  </div>
                )}
                <input
                  ref={inputRef}
                  type="file"
                  accept="image/*"
                  onChange={selectFile}
                  className="hidden"
                  id="cover-photo-input"
                />
                <label
                  htmlFor="cover-photo-input"
                  className="inline-block mt-2 px-4 py-2 bg-blue-500 text-white rounded-lg cursor-pointer hover:bg-blue-600 transition-colors"
                >
                  {preview ? 'Change Image' : 'Select Image'}
                </label>
              </div>
              <div className="flex gap-3 justify-end">
                <button
                  onClick={closeModal}
                  className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={upload}
                  disabled={!file || uploading}
                  className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors flex items-center gap-2"
                >
                  {uploading && (
                    <Icon icon="mdi:loading" className="w-4 h-4 animate-spin" />
                  )}
                  {uploading ? 'Uploading...' : 'Upload'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default DiaryCover;
